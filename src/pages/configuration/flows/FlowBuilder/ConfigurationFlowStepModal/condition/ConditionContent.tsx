import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useOutletContext } from "react-router-dom";

import {
  Accordion,
  CustomAccordionTrigger,
  Inline,
  OpenCloseIcon,
  PillSelect,
  SelectOptionType,
  SelectValue,
  Stack,
  Text
} from "@oneteam/onetheme";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { ConfigurationFlowMode } from "@pages/configuration/ConfigurationFlowMode";
import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { Dictionary } from "@src/hooks/useDictionary";
import {
  CompoundCondition,
  Condition,
  ConditionItem,
  LogicalOperators,
  Operators
} from "@src/types/FlowConfiguration/Condition";
import { ConditionStepBranch } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties.ts";

import { ConditionContentAddLine } from "./ConditionContentAddLine";
import "./ConditionFlowStepProperties.scss";
import { SimpleCondition } from "./SimpleCondition";

const createLogicalOperatorOptions = (d: Dictionary): SelectOptionType[] => {
  return Object.values(LogicalOperators).map((value: LogicalOperators) => ({
    label: d(
      `ui.configuration.flows.step.variants.condition.logicalOperator.${value}.label`
    ),
    value
  }));
};

const createEmptyCondition = (): ConditionItem => ({
  lhs: "",
  operator: Operators.EQUALS,
  rhs: ""
});

export const ConditionContent = ({
  condition,
  docAccessor,
  depth = 0,
  d,
  isSimpleConditionInRoot = false,
  isCompoundConditionType,
  isRoot = false,
  parentLogicalOperatorType,
  indexInParent
}: {
  condition: Condition;
  docAccessor: string[];
  depth?: number;
  d: Dictionary;
  isSimpleConditionInRoot?: boolean;
  isCompoundConditionType: (
    condition: Condition
  ) => condition is CompoundCondition;
  isRoot?: boolean;
  parentLogicalOperatorType?: LogicalOperators;
  indexInParent?: number;
}) => {
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();
  const logicalOperatorOptions = useMemo(
    () => createLogicalOperatorOptions(d),
    [d]
  );

  const { configurationFlowMode } = useConfigurationFlowContext();

  const {
    logicalOperatorType,
    conditions
  }: { logicalOperatorType: LogicalOperators; conditions: Condition[] } =
    useMemo(() => {
      const type = (
        "AND" in condition ? LogicalOperators.AND : LogicalOperators.OR
      ) as LogicalOperators;
      return {
        logicalOperatorType: type,
        conditions: (condition as CompoundCondition)[type] ?? []
      };
    }, [condition]);

  const [isExpandedCondition, setIsExpandedCondition] = useState<{
    [key: string]: boolean;
  }>(() =>
    conditions.reduce<{ [key: string]: boolean }>(
      (acc, _, index) => ({ ...acc, [`${depth}-${index}`]: true }),
      {}
    )
  );

  const docAccessorForRoot = useMemo(
    () => (isRoot ? docAccessor.slice(0, docAccessor.length - 1) : []),
    [docAccessor, isRoot]
  );

  const addCondition = useCallback(() => {
    if (configurationFlowMode !== ConfigurationFlowMode.EDIT) {
      return;
    }
    docChange(d => {
      if (isSimpleConditionInRoot) {
        const branch = getByPath<ConditionStepBranch>(
          d,
          isRoot ? docAccessorForRoot : docAccessor
        );
        if (!(branch?.condition as CompoundCondition)) {
          return;
        }

        const existingTopLevelSimpleCondition = JSON.parse(
          JSON.stringify(branch.condition)
        );
        (branch.condition as CompoundCondition) = {
          AND: [existingTopLevelSimpleCondition, createEmptyCondition()]
        };
      } else {
        const anyRootCondition = getByPath<Condition>(d, docAccessor);
        const rootConditionArray = (anyRootCondition as CompoundCondition)?.[
          logicalOperatorType
        ];
        if (!rootConditionArray) {
          return;
        }
        rootConditionArray.push(createEmptyCondition());
      }
    });
  }, [
    isSimpleConditionInRoot,
    logicalOperatorType,
    docChange,
    docAccessor,
    isRoot,
    docAccessorForRoot,
    configurationFlowMode
  ]);

  const addNestedCondition = useCallback(() => {
    if (configurationFlowMode !== ConfigurationFlowMode.EDIT) {
      return;
    }
    docChange(d => {
      if (isSimpleConditionInRoot && !isCompoundConditionType(condition)) {
        const branch = getByPath<ConditionStepBranch>(
          d,
          isRoot ? docAccessorForRoot : docAccessor
        );
        if (!(branch?.condition as CompoundCondition)) {
          return;
        }

        const existingTopLevelSimpleCondition = JSON.parse(
          JSON.stringify(branch.condition)
        );
        (branch.condition as CompoundCondition) = {
          AND: [
            existingTopLevelSimpleCondition,
            { AND: [createEmptyCondition()] }
          ]
        };
      } else {
        const nestedInnerCondition = getByPath<Condition>(d, docAccessor);
        const nestedConditionArray = (
          nestedInnerCondition as CompoundCondition
        )?.[logicalOperatorType];
        if (!nestedConditionArray) {
          return;
        }
        nestedConditionArray.push({
          AND: [createEmptyCondition()]
        } as CompoundCondition);
      }
    });
  }, [
    docChange,
    isSimpleConditionInRoot,
    isCompoundConditionType,
    condition,
    isRoot,
    docAccessorForRoot,
    docAccessor,
    configurationFlowMode,
    logicalOperatorType
  ]);

  const handleLogicalOperatorChange = useCallback(
    (newOperator: SelectValue | undefined) => {
      if (configurationFlowMode !== ConfigurationFlowMode.EDIT) {
        return;
      }
      docChange(d => {
        const condition = getByPath<CompoundCondition>(d, docAccessor);
        if (
          condition?.[logicalOperatorType] &&
          (newOperator as LogicalOperators)
        ) {
          condition[newOperator as LogicalOperators] = JSON.parse(
            JSON.stringify(condition[logicalOperatorType])
          );
          delete condition[logicalOperatorType];
        }
      });
    },
    [docAccessor, docChange, logicalOperatorType, configurationFlowMode]
  );

  const conditionTrigger: CustomAccordionTrigger = useCallback(
    ({ isOpen, onClick }) => (
      <Inline
        width="fit"
        onClick={onClick}
        style={{ cursor: "pointer" }}
        alignment="left"
        gap="050"
      >
        <Inline alignment="left" gap="000">
          <OpenCloseIcon isOpen={isOpen} />
        </Inline>
      </Inline>
    ),
    []
  );

  if (!condition) {
    return <></>;
  }

  if (!isCompoundConditionType(condition)) {
    return (
      <Stack gap="100">
        <SimpleCondition
          condition={condition}
          docAccessor={[
            ...docAccessor,
            parentLogicalOperatorType ?? "",
            indexInParent?.toString() ?? ""
          ]}
        />
        {isSimpleConditionInRoot &&
          configurationFlowMode === ConfigurationFlowMode.EDIT && (
            <ConditionContentAddLine
              addCondition={addCondition}
              addNestedCondition={addNestedCondition}
              d={d}
            />
          )}
      </Stack>
    );
  }

  return (
    <Stack gap="100">
      {conditions.map((cond, index) => {
        const accordionKey = `${depth}-${index}`;
        let nestedCondition: Condition[] | undefined;
        if (isCompoundConditionType(cond)) {
          nestedCondition = LogicalOperators.AND in cond ? cond.AND : cond.OR;
        }

        return (
          <Stack gap="100" key={accordionKey}>
            {index !== 0 && (
              <PillSelect
                value={logicalOperatorType as SelectValue}
                options={logicalOperatorOptions}
                handleChange={(value: SelectValue | undefined) =>
                  handleLogicalOperatorChange(value)
                }
                required
                disabled={index > 1}
              />
            )}
            {nestedCondition ? (
              <Stack>
                <Accordion
                  contentOverflow="visible"
                  trigger={conditionTrigger}
                  isOpen={isExpandedCondition[accordionKey]}
                  onOpenChange={() => {
                    setIsExpandedCondition(prev => ({
                      ...prev,
                      [accordionKey]: !prev[accordionKey]
                    }));
                  }}
                >
                  <Stack
                    className="nested-condition-block"
                    style={{ paddingLeft: "var(--spacing-150)" }}
                  >
                    <ConditionContent
                      condition={cond}
                      docAccessor={[
                        ...docAccessor,
                        logicalOperatorType,
                        index.toString()
                      ]}
                      depth={depth + 1}
                      d={d}
                      isCompoundConditionType={isCompoundConditionType}
                    />
                  </Stack>
                </Accordion>
                {!isExpandedCondition[accordionKey] && (
                  <Stack gap="100" className="nested-condition-block">
                    <Text
                      color="secondary"
                      className="nested-condidtion-block__message"
                    >
                      {`${nestedCondition.length} ${d("ui.configuration.flows.step.variants.condition.propertiesModal.nestedConditionBlocks")}`}
                    </Text>
                  </Stack>
                )}
              </Stack>
            ) : (
              <ConditionContent
                condition={cond}
                docAccessor={docAccessor}
                depth={depth + 1}
                d={d}
                isCompoundConditionType={isCompoundConditionType}
                parentLogicalOperatorType={logicalOperatorType}
                indexInParent={index}
              />
            )}
            {index === conditions.length - 1 &&
              configurationFlowMode == ConfigurationFlowMode.EDIT && (
                <ConditionContentAddLine
                  addCondition={addCondition}
                  addNestedCondition={addNestedCondition}
                  d={d}
                />
              )}
          </Stack>
        );
      })}
    </Stack>
  );
};
