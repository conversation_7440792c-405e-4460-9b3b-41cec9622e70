import React from "react";

import { Inline, MultiSelect, Select } from "@oneteam/onetheme";

import { Dictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import {
  SelectQuestionMultipleValue,
  SelectQuestionProperties,
  SelectQuestionSingleValue
} from "@src/types/QuestionProperties";

import { optionsAdapterForComponent } from "./SelectHelper";

export const SelectDefault = ({
  question,
  name,
  disabled,
  onChange,
  d
}: {
  question: Question<SelectQuestionProperties>;
  name: string;
  onChange: (
    value: SelectQuestionSingleValue | SelectQuestionMultipleValue
  ) => void;
  disabled?: boolean;
  d: Dictionary;
}) => {
  const multiSelect = () => {
    return (
      <MultiSelect
        label={d("ui.configuration.forms.question.defaultValue.label")}
        name={name}
        required={false}
        options={optionsAdapterForComponent(question.properties?.options)}
        disabled={disabled}
        value={question.properties?.defaultValue}
        onChange={value => onChange?.(value as SelectQuestionMultipleValue)}
      />
    );
  };

  const singleSelect = () => {
    return (
      <Select
        label={d("ui.configuration.forms.question.defaultValue.label")}
        name={name}
        required={false}
        options={optionsAdapterForComponent(question.properties?.options)}
        disabled={disabled}
        value={question.properties?.defaultValue}
        onChange={value => onChange?.(value as SelectQuestionSingleValue)}
      />
    );
  };

  return (
    <Inline contentsWidth="fill" width="fill">
      {question.properties?.isMultiSelect ? multiSelect() : singleSelect()}
    </Inline>
  );
};
