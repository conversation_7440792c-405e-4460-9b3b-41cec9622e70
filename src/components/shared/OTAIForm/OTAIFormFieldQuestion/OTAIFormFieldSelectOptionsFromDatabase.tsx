import React, { useEffect } from "react";
import { useOutletContext } from "react-router-dom";

import { useFoundationCollectionOptions } from "@src/hooks/formConfiguration/select/useFoundationCollectionOptions.ts";
import { DynamicOptionsReturnType } from "@src/types/DynamicSelectOptions.ts";
import { SelectQuestionProperties } from "@src/types/QuestionProperties.ts";
import { Workspace } from "@src/types/workspace.ts";

interface OTAIFormFieldSelectOptionsProps {
  dynamicOptions: SelectQuestionProperties["dynamicOptions"];
  onOptionsFetched: (options: DynamicOptionsReturnType) => void;
  useKeyAsValue?: boolean;
}

const OTAIFormFieldSelectOptionsFromDatabase: React.FC<
  OTAIFormFieldSelectOptionsProps
> = ({ dynamicOptions, onOptionsFetched, useKeyAsValue = false }) => {
  const { workspace } = useOutletContext<{ workspace: Workspace }>();

  const options = useFoundationCollectionOptions(
    dynamicOptions,
    workspace?.id,
    useKeyAsValue
  );

  useEffect(() => {
    if (options) {
      onOptionsFetched(options);
    }
  }, [options, onOptionsFetched]);

  return null;
};

export default OTAIFormFieldSelectOptionsFromDatabase;
