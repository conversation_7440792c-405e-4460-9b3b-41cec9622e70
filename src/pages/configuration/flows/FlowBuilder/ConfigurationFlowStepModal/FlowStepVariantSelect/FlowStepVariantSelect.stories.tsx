import React from "react";

import { Form } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { z } from "zod";

import { FlowStepVariant } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { FlowStepVariantSelect } from "./FlowStepVariantSelect";

const meta: Meta<typeof FlowStepVariantSelect> = {
  component: FlowStepVariantSelect,
  title: "configuration/FlowStepVariantSelect"
};

export default meta;

type Story = StoryObj<typeof FlowStepVariantSelect>;

const defaultArgs: Story["args"] = {
  disabled: false
};

const defaultRenderer = (args: Story["args"]) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [variant, setVariant] = React.useState(FlowStepVariant.TRIGGER);
  return (
    <Form
      schema={z.object({ variant: z.string() })}
      d={() => ""}
      handleSubmit={() => console.log("Submit")}
      hideFormButtons
    >
      <FlowStepVariantSelect
        {...args}
        accessor="variant"
        variant={variant}
        onChange={setVariant}
      />
    </Form>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRenderer
};
