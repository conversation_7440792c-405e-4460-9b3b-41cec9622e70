import "@picocss/pico/css/pico.min.css";
import "./App.css";
import { useDocument } from "@automerge/automerge-repo-react-hooks";
import { type AutomergeUrl } from "@automerge/automerge-repo";
import { useEffect, useState } from "react";
import { JsonData, JsonEditor } from "json-edit-react";
import set from "lodash/set";
import appBuildInfo from "./build.json";

type SampleType = {
    name: string;
    prefix: string;
};

function App({ docUrl }: { docUrl: AutomergeUrl }) {
  const [doc, changeDoc] = useDocument<any>(docUrl);
  const [json, setJson] = useState<string>(JSON.stringify(doc, undefined, 2));
  const [searchText, setSearchText] = useState<string>("");

  useEffect(() => {
    setJson(JSON.stringify(doc, undefined, 2));
  }, [doc]);

    const postSampleData = async (type: SampleType) => {
        const timestamp = new Date().toISOString().replace(/-/g, "").replace(/:/g, "").split(".")[0];
        const body = {
            name: `${type.prefix} ${timestamp}`,
            key: `${type.prefix}${timestamp}`,
            description: `${type.name} workspace`
        };
        await fetch(`/ai/api/sample-data/${type.name}`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(body)
        });
    };

  return (
    <>
      <header>
        <h1>AutoMerge Document JSON</h1>
          <div>
          <button className="secondary" onClick={() => postSampleData({name:"SGPIT", prefix:"SG"})}>Create SGPIT</button>
          <button onClick={() => postSampleData({name:"TESTRUNNER", prefix:"TR"})}>Create TestRunner</button>
          </div>
          <br/>
          <p className="small">
          Note: if you can't see the document, please make sure you are logged
          in to the application in another tab.
        </p>
        <a href={`/ai/api/sync/show/${docUrl}`} target="_blank">
          JSON
        </a>
      </header>
      {doc?.metadata?.createdAt &&
        new Date(doc.metadata.createdAt).toISOString()}
      {doc?.metadata?.createdAt && doc?.metadata?.updatedAt && (
        <>&nbsp;|&nbsp;</>
      )}
      {doc?.metadata?.updatedAt &&
        new Date(doc.metadata.updatedAt).toISOString()}
      <div>
        Search:{" "}
        <input
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
        />
      </div>
      <div style={{ width: "100%" }}>
        <JsonEditor
          data={JSON.parse(json ?? "{}")}
          searchFilter="all"
          searchText={searchText}
          rootName="Document"
          onUpdate={(data: JsonData) => {
            console.log(data);
          }}
          maxWidth="100%"
          setData={(data: JsonData) => {
            setJson(JSON.stringify(data, undefined, 2));
            changeDoc((d) => {
              const keys: string[] = [];
              // update top level fields
              for (const [key, value] of Object.entries(data as Record<string, never>)) {
                set(d, key, value);
                keys.push(key);
              }
              // remove top level fields
              const removedKeys = Object.keys(d).filter(
                (key) => !keys.includes(key)
              );
              for (const key of removedKeys) {
                delete d[key];
              }
            });
          }}
        />
      </div>
      <footer>
        sync-debug-version: {appBuildInfo.version}
      </footer>
    </>
  );
}

export default App;
