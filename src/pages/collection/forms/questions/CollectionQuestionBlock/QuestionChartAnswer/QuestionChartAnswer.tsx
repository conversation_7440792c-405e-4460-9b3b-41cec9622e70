import React, { useEffect, useMemo, useState } from "react";

import {
  Box,
  Chart,
  ChartConfig,
  ChartProps,
  ChartType,
  Inline,
  PillSelect,
  SelectValue,
  Stack,
  Text
} from "@oneteam/onetheme";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import {
  ChartQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { ChartAnswer } from "@src/types/collection/CollectionForm";

import "./QuestionChartAnswer.scss";
import {
  AggregationForPieChart,
  generateMockData,
  mapConfigIdsToNames,
  mapIdsToNames
} from "./questionChartAnswerHelper";

const mockDataRowNumber = 3;
const sliderThreshold = 50;

export const QuestionChartAnswer = ({
  question,
  parentQuestion,
  answer
}: {
  question: Question<ChartQuestionProperties>;
  parentQuestion: Question<TableQuestionProperties>;
  answer?: ChartAnswer;
}) => {
  const [pieFilter, setPieFilter] = useState<SelectValue | undefined>("");
  const [advancedOptions, setAdvancedOptions] = useState<
    ChartProps["advancedOptions"]
  >({});
  const d = useDictionary();

  console.log("answer", answer);

  const formattedData = useMemo((): Record<string, string>[] => {
    if (!answer) {
      return generateMockData(
        parentQuestion?.properties?.columns || [],
        mockDataRowNumber
      );
    } else {
      // Map IDs to names before aggregation
      const rawConfig = question?.properties?.chartConfig;
      if (!rawConfig) {
        return answer;
      }
      const chartConfigWithNames = mapConfigIdsToNames(
        rawConfig,
        parentQuestion?.properties?.columns
      );

      // Type guard to ensure we have a PieChartConfig
      if (
        chartConfigWithNames.type === ChartType.PIE &&
        "groupBy" in chartConfigWithNames
      ) {
        return AggregationForPieChart(
          answer,
          chartConfigWithNames.groupBy,
          chartConfigWithNames.series
        );
      }

      if (
        (chartConfigWithNames.type === ChartType.LINE ||
          chartConfigWithNames.type === ChartType.BAR) &&
        "xAxis" in chartConfigWithNames
      ) {
        return AggregationForPieChart(
          answer,
          chartConfigWithNames.xAxis,
          chartConfigWithNames.series
        );
      }
      return answer;
    }
  }, [
    parentQuestion?.properties?.columns,
    answer,
    question.properties?.chartConfig
  ]);

  const formattedConfig = useMemo(() => {
    const rawConfig = question?.properties?.chartConfig;
    if (!rawConfig) {
      return undefined;
    }
    const chartConfigWithNames = mapConfigIdsToNames(
      rawConfig,
      parentQuestion?.properties?.columns
    );

    const chartConfig = { ...chartConfigWithNames } as ChartConfig;

    if (formattedData && pieFilter && chartConfig.type === ChartType.PIE) {
      if (chartConfig.swapColumns) {
        chartConfig.rowIndex = Number(pieFilter);
        chartConfig.series =
          formattedData[Number(pieFilter)]?.[chartConfig?.series[0]];
      } else {
        chartConfig.rowIndex = undefined;
        chartConfig.series = pieFilter as string;
      }
    }

    return chartConfig;
  }, [
    question?.properties?.chartConfig,
    pieFilter,
    formattedData,
    parentQuestion?.properties?.columns
  ]);

  const pieChartFilterOptions = useMemo(() => {
    if (question?.properties?.chartConfig.type !== ChartType.PIE) {
      return [];
    }

    const mapSeriesIdsToNames = mapIdsToNames(
      question?.properties?.chartConfig.series,
      parentQuestion.properties?.columns
    );

    if (question?.properties?.chartConfig.swapColumns) {
      const seriesByKey = mapSeriesIdsToNames?.[0];
      if (!seriesByKey) {
        return [];
      }
      return formattedData.map((row, index) => ({
        label: row[seriesByKey],
        value: String(index)
      }));
    } else {
      return (mapSeriesIdsToNames || []).map(seriesKey => ({
        label: seriesKey,
        value: seriesKey
      }));
    }
  }, [
    question?.properties?.chartConfig,
    formattedData,
    parentQuestion.properties?.columns
  ]);

  useEffect(() => {
    if (pieChartFilterOptions.length > 0) {
      setPieFilter(pieChartFilterOptions[0].value);
    } else {
      setPieFilter("");
    }
  }, [pieChartFilterOptions]);

  const handlePieChartFilterChange = (value: SelectValue | undefined) => {
    setPieFilter(value);
  };

  // Check if chart has enough configurations to render
  const shouldRenderChart = useMemo(() => {
    const config = question?.properties?.chartConfig;
    if (!config?.type) {
      return false;
    }

    if (config.type === ChartType.LINE || config.type === ChartType.BAR) {
      return (
        config.xAxis &&
        config.xAxis.length > 0 &&
        config.series &&
        config.series.length > 0
      );
    } else if (config.type === ChartType.PIE) {
      return (
        config.groupBy &&
        config.groupBy.length > 0 &&
        config.series &&
        config.series.length > 0
      );
    }
    return false;
  }, [question?.properties?.chartConfig]);

  useEffect(() => {
    if (formattedData && formattedData.length > sliderThreshold) {
      setAdvancedOptions((prevOptions: ChartProps["advancedOptions"]) => ({
        ...prevOptions,
        dataZoom: [
          {
            type: "slider",
            show: true,
            start: 0,
            end: 9,
            handleSize: 10,
            height: 30,
            bottom: 0
          },
          {
            type: "inside",
            start: 0,
            end: 9
          }
        ]
      }));
    }
  }, [formattedConfig, formattedData]);

  console.log("formattedData", formattedData, formattedConfig);

  // Return empty fragment if chart configuration is incomplete
  if (!shouldRenderChart || !formattedConfig) {
    return (
      <Stack style={{ overflow: "hidden" }} gap="100">
        <Text size="m" color="text-tertiary">
          {question.text}
        </Text>
        {d("ui.configuration.forms.question.chart.unablePreviewMessage")}
      </Stack>
    );
  }
  return (
    <Stack gap="100">
      <Inline width="100" spaceBetween>
        <Text size="m" color="text-tertiary">
          {question.text}
        </Text>
        {formattedConfig?.type === ChartType.PIE && (
          <PillSelect
            label="Filter"
            value={pieFilter}
            options={pieChartFilterOptions}
            handleChange={value => handlePieChartFilterChange(value)}
          />
        )}
      </Inline>
      <Box className="chart-preview-container">
        <Chart
          data={formattedData}
          config={formattedConfig}
          width="100"
          height="100"
          advancedOptions={advancedOptions}
        />
      </Box>
    </Stack>
  );
};
