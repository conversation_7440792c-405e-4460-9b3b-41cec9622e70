.flow-variable {
  cursor: pointer;
  color: var(--color-text-primary);
  padding: var(--spacing-025, 4px) var(--spacing-050, 8px);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-rounded);
  width: fit-content;
  max-width: 100%;
  background: var(--color-surface-secondary);
  min-height: fit-content;
  &--variant-line-item {
    padding: var(--spacing-050, 4px) 0px;
    border: 0px solid transparent;
    border-radius: 0px;
    background-color: transparent;
    width: 100%;

    &:has(.renamable) {
      padding: var(--spacing-025, 4px) 0px;
    }

    &:hover {
      background-color: var(--color-surface-primary);
      color: var(--color-text-primary);
      & .flow-variable__copy,
      & .flow-variable__kebab,
      & .flow-variable__description {
        opacity: 1;
        display: flex;
      }
    }
  }
  &--variant-default {
    color: var(--color-text-secondary);
    background-color: #e3e7ed;
    border: 1px solid var(--color-border);
    padding: 0 var(--spacing-025);
    border-radius: var(--border-radius-rounded-slightly);
    &:hover {
      color: var(--color-text-primary);
      background: var(--color-surface-primary);
      border-color: var(--color-border);
    }
    & .flow-variable__description {
      display: none !important;
      visibility: hidden !important;
    }
    & .flow-variable__kebab {
      display: none;
    }
  }
  &__copy,
  &__kebab,
  &__description {
    opacity: 0;
    transition: ease-in 0.1s;
  }
  &__description {
    display: none;
    opacity: 0;
    transition: ease-in 0.1s;
  }
  &:has(.flow-variable__kebab--open) {
    & .flow-variable__kebab {
      opacity: 1;
    }
  }
}
