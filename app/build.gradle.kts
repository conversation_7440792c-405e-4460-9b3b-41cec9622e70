plugins {
    alias(ktorLibs.plugins.ktor)
    id("project.conventions")
}

group = "services.oneteam.ai.app"
version = "0.0.1"

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    test {
        resources {
            srcDir("scripts/db") // so that h2 can find the db scripts and set up the users for migrations to succeed
        }
    }
}

ktor {
    fatJar {
        archiveFileName.set("oneteam-oa.jar")
    }
    docker {
        jib {
            from {
                image = "eclipse-temurin:21"
            }
            container {
                mainClass = "io.ktor.server.netty.EngineMain"
            }
        }
        jreVersion.set(JavaVersion.VERSION_21)
        localImageName.set("oneteam:oneteam-oa")

        imageTag.set(providers.environmentVariable("VERSION"))
        externalRegistry.set(
            io.ktor.plugin.features.DockerImageRegistry.externalRegistry(
                hostname = providers.environmentVariable("ACR_HOST"),
                project = providers.environmentVariable("ACR_PROJECT"),
                username = providers.environmentVariable("ACR_USERNAME"),
                password = providers.environmentVariable("ACR_PASSWORD")
            )
        )
    }
}

dependencies {
    implementation(project(":flows"))
    implementation(project(":shared"))
    implementation(project(":permissions"))

    //ktor version is coming from plugin which includes the bom
    implementation(ktorLibs.server.core)
    implementation(ktorLibs.server.contentNegotiation)
    implementation(ktorLibs.server.netty)
    implementation(ktorLibs.server.config.yaml)
    implementation(ktorLibs.server.resources)
    implementation(ktorLibs.server.statusPages)
    implementation(ktorLibs.server.auth)
    implementation(ktorLibs.server.auth.jwt)
    implementation(ktorLibs.server.sessions)
    implementation(ktorLibs.server.cors)
    implementation(ktorLibs.server.requestValidation)
    implementation(ktorLibs.server.websockets)
    implementation(ktorLibs.serialization.kotlinx.json)

    implementation("com.networknt:json-schema-validator")

    implementation(libs.dataframe)
    implementation(libs.json.schema.validator)
    implementation(libs.json.path)

    implementation(libs.opentelemetry.ktor)
    implementation(libs.logstash)

    implementation(libs.postgresql)
    implementation(libs.bundles.logback)
    implementation(libs.kotlinx.datetime)
    implementation(libs.bundles.exposed)
    implementation(libs.hikari)
    implementation(libs.bundles.flyway) // https://documentation.red-gate.com/fd/migrations-184127470.html
    implementation(libs.automerge)
    implementation(libs.nanoid)

    implementation(platform(libs.koin.bom))
    implementation("io.insert-koin:koin-core")
    implementation("io.insert-koin:koin-ktor")

    testImplementation(ktorLibs.server.testHost)
    testImplementation(ktorLibs.client.mock)
    testImplementation(kotlin("test"))
    testImplementation(libs.bundles.kotest)
    testImplementation("io.insert-koin:koin-test-junit5")
    testImplementation(libs.h2)
    testImplementation(libs.mockito)

}
