import React, { useEffect } from "react";

import { ChangeFn } from "@automerge/automerge/next";
import { Form } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { z } from "zod";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext.tsx";
import { ConfigurationFlowProvider } from "@pages/configuration/flows/ConfigurationFlowProvider.tsx";

import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { ConfigurationFlowStepModal } from "./ConfigurationFlowStepModal.tsx";
import { MockOutletContextProvider } from "./MockOutletContextProvider.tsx";
import {
  MockFlowForStory,
  MockFlowStep,
  sampleWorkspaceDocumentForFlow
} from "./storyConstants.ts";

const contextValue = {
  docChange: (changeFn: ChangeFn<WorkspaceDocument>) => {
    changeFn(sampleWorkspaceDocumentForFlow);
  }
};

const meta: Meta<typeof ConfigurationFlowStepModal> = {
  component: ConfigurationFlowStepModal,
  title: "collection/ConfigurationFlowStepModal",
  decorators: [
    Story => (
      <MockOutletContextProvider context={contextValue}>
        <Story />
      </MockOutletContextProvider>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ConfigurationFlowStepModal>;

const defaultArgs: Story["args"] = {};

const ModalWithSetStep = ({
  args,
  stepId
}: {
  args: Story["args"];
  stepId: string;
}) => {
  const { setConfigurationFlow, setSelectedStepId } =
    useConfigurationFlowContext();

  useEffect(() => {
    setConfigurationFlow(MockFlowForStory);
    setSelectedStepId(stepId);
  }, [setConfigurationFlow, setSelectedStepId, stepId]);

  return <ConfigurationFlowStepModal {...args} docChange={() => {}} />;
};
const conditionRender = (args: Story["args"]) => {
  return (
    <Form
      schema={z.any()}
      d={(key: string) => key}
      handleSubmit={() => console.log("Submit")}
      defaultValues={MockFlowStep}
      hideFormButtons
    >
      <ConfigurationFlowProvider>
        <ModalWithSetStep args={args} stepId="3" />
      </ConfigurationFlowProvider>
    </Form>
  );
};

export const Condition: Story = {
  args: defaultArgs,
  render: conditionRender
};

const setVariablesRender = (args: Story["args"]) => {
  return (
    <Form
      schema={z.any()}
      d={(key: string) => key}
      handleSubmit={() => console.log("Submit")}
      defaultValues={MockFlowStep}
      hideFormButtons
    >
      <ConfigurationFlowProvider>
        <ModalWithSetStep args={args} stepId="4" />
      </ConfigurationFlowProvider>
    </Form>
  );
};

export const SetVariables: Story = {
  args: defaultArgs,
  render: setVariablesRender
};
