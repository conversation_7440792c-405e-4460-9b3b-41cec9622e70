.handsontable {
  --ht-border-color: var(--color-border);
  --ht-cell-vertical-border-color: var(--color-border);
  --ht-cell-horizontal-border-color: var(--color-border);
  --ht-cell-vertical-padding: var(--components-inputs-padding-vertical, 6px);
  --ht-cell-horizontal-padding: var(
    --components-inputs-padding-horizontal,
    6px
  );
  --ht-cell-editor-border-color: var(
    --components-inputs-focus-color-border,
    #0083ff
  );
  font-family:
    var(--font-family), Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  & .highlighted-row {
    background-color: #f0f8ff; /* Light blue background */
    /* Add other styles as needed, e.g., font-weight, color */
  }

  & .wtHolder {
    overscroll-behavior: contain;
  }
  & .wtBorder.current,
  & .wtBorder.area {
    background-color: var(
      --components-inputs-focus-color-border,
      #0083ff
    ) !important;
  }
  & td:has(.highlight-location) {
    padding: 0px;
    > div {
      height: 100%;
    }
    & .highlight-location {
      height: 100%;
      width: -webkit-fill-available;
      border: 0px;
      &__highlight-box {
        height: calc(100% - 4px);
        width: calc(100% - 4px);
      }
    }
    & .annotation-message-field {
      & textarea {
        box-sizing: border-box;
      }
    }

    transition: none;
    .collection-question-block-comment {
      visibility: hidden;
      opacity: 0;
      &--with-annotations,
      &--open {
        opacity: 1;
        visibility: visible;
      }
    }
    &:hover,
    &:focus-within {
      .collection-question-block-comment {
        visibility: visible;
        opacity: 1;
      }
    }
  }

  & td:has(.question-table-answer-cell-editor.open) {
    overflow: visible;
    & > div {
      overflow: visible;
    }
  }
}

.question-table-answer {
  &__add-row-button {
    opacity: 0;
    bottom: -1px;
    left: 0;
    z-index: 1000;
    margin-right: var(--spacing-050);
    margin-bottom: var(--spacing-050);
  }

  &__container {
    padding-bottom: var(--spacing-100);
    gap: var(--spacing-050);
    &:hover {
      & .question-table-answer__add-row-button {
        opacity: 1;
      }
    }
  }
}

.question-table-view__tabs {
  align-items: flex-end !important;
}

.question-table-view__header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--color-surface-primary);
}
