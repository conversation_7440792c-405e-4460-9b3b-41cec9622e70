#!/bin/zsh

FAST_MODE=0
for arg in "$@"; do
  #  -f for FAST mode, skip git pull
  if [[ "$arg" == "-f" ]]; then
    FAST_MODE=1
  fi
done

#docker pull authzed/spicedb:latest
#https://authzed.com/docs/spicedb/concepts/datastores#postgresql

# See ./Readme.md for initial Postgres setup

# exit immediately if any command fails.
set -e

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

## docker host ip
HOST=host.docker.internal
## get token from .env.local
SPICEDB_TOKEN=$(grep '^SPICEDB_TOKEN=' "$SCRIPT_DIR"/../../.env.local | cut -d'=' -f2-)

if [[ "$FAST_MODE" != "1" ]]; then
  docker build -t otai_spicedb "$SCRIPT_DIR"/docker
fi

docker run --rm --name spicedb \
 -p 50051:50051 \
 -p 9999:9090 \
 -p 9998:8443 \
 -e SPICEDB_DATASTORE_ENGINE="postgres" \
 -e SPICEDB_MIGRATION_DATASTORE_CONNECTION_STRING="*********************************************************/otai_spicedb?sslmode=disable" \
 -e SPICEDB_DATASTORE_CONNECTION_STRING="***********************************************/otai_spicedb?sslmode=disable" \
 -e SPICEDB_AUTH_TOKEN="$SPICEDB_TOKEN" \
 otai_spicedb

# The following is the manual steps, now all in the Dockerfile above
#
## migrate
#docker run --name spicedb-migrate --rm -p 50051:50051 authzed/spicedb datastore migrate head --datastore-engine=postgres --datastore-conn-uri="***************************************************/spicedb?sslmode=disable"
## start
#docker run --name spicedb --rm -p 50051:50051 authzed/spicedb serve --grpc-preshared-key "$SPICEDB_TOKEN" --datastore-engine=postgres --datastore-conn-uri="***************************************************/spicedb?sslmode=disable"
#
## Note
## SpiceDB's Watch API requires PostgreSQL's Commit Timestamp tracking to be enabled.
## This can be done by providing the --track_commit_timestamp=on flag, configuring postgresql.conf, or executing ALTER SYSTEM SET track_commit_timestamp = on; and restarting the instance.
