import React from "react";

import type { Meta, StoryObj } from "@storybook/react";

import { FlowExecutionDocument } from "@src/types/FlowConfiguration/FlowExecution";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { FlowPathItem } from "./FlowPath";

const meta: Meta<typeof FlowPathItem> = {
  component: FlowPathItem,
  title: "collection/FlowPathPill"
};

export default meta;

type Story = StoryObj<typeof FlowPathItem>;

const defaultArgs: Story["args"] = {
  configurationStep: {
    id: "aXfYIKVM3VQ5H5MHi7hRs",
    metadata: {
      createdAt: "2025-08-07T08:31:06.871Z",
      updatedAt: "2025-08-07T08:31:06.873Z"
    },
    name: "Set my variable",
    next: "ar6PtF2JKzeD4pPYys7uW",
    properties: {
      variables: [
        {
          identifier: "variable_1",
          type: "text",
          value: ""
        }
      ]
    },
    variant: "setVariables"
  }
};

const defaultRender = (args: Story["args"]) => {
  return (
    <FlowPathItem
      {...args}
      configurationStep={args?.configurationStep || ({} as FlowStep)}
      executionFlow={{} as FlowExecutionDocument}
    />
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRender
};
