import React from "react";

import { isCompoundConditionType } from "@helpers/flows/conditionFlowStepFormatHelper";

import { ConditionContent } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowStepModal/condition/ConditionContent";

import { useDictionary } from "@src/hooks/useDictionary";
import { ExecutionStep } from "@src/types/FlowConfiguration/FlowExecution";
import { ConditionStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

export const ConditionFields = ({
  executionStep
}: {
  executionStep: ExecutionStep<ConditionStepProperties>;
}) => {
  const d = useDictionary();
  return (
    <ConditionContent
      condition={executionStep.properties?.branches?.[0]?.condition}
      docAccessor={[]}
      d={d}
      isCompoundConditionType={isCompoundConditionType}
    />
  );
};
