.accordion__trigger {
  width: 100%;
}

.set-variable-properties-columns {
  &__list {
    gap: var(--spacing-050);
  }
}

.set-variable-item {
  & .set-variable-kebab-menu:not(:has(.floating-with-parent)) {
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease-in-out;
  }
  &:hover {
    & .set-variable-kebab-menu {
      opacity: 1;
      visibility: visible;
    }
  }

  &__modal {
    & .modal__content {
      height: 100%;
    }
  }
}
