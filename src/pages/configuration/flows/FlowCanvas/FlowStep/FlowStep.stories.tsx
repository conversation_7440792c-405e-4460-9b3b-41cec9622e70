import React from "react";

import { Box } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";

import {
  FlowStep as FlowStepType,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { FlowStep } from "./FlowStep";

const meta: Meta<typeof FlowStep> = {
  component: FlowStep,
  title: "configuration/FlowStep"
};

export default meta;

type Story = StoryObj<typeof FlowStep>;

const defaultFlowStep: FlowStepType = {
  id: "1",
  variant: "trigger",
  name: "Untitled",
  properties: {},
  metadata: {
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
};
const defaultArgs: Story["args"] = {
  flowStep: defaultFlowStep
};

const defaultRender = (args: Story["args"]) => {
  return (
    <Box padding="200">
      <FlowStep {...args} flowStep={args?.flowStep ?? defaultFlowStep} />
    </Box>
  );
};

export const Trigger: Story = {
  args: defaultArgs,
  render: defaultRender
};

export const Action: Story = {
  args: {
    ...defaultArgs,
    flowStep: {
      ...defaultFlowStep,
      variant: FlowStepVariant.ACTION
    }
  },
  render: defaultRender
};

export const Condition: Story = {
  args: {
    ...defaultArgs,
    flowStep: {
      ...defaultFlowStep,
      variant: FlowStepVariant.CONDITION
    }
  },
  render: defaultRender
};

export const SetVariables: Story = {
  args: {
    ...defaultArgs,
    flowStep: {
      ...defaultFlowStep,
      variant: FlowStepVariant.SET_VARIABLES
    }
  },
  render: defaultRender
};

export const Flow: Story = {
  args: {
    ...defaultArgs,
    flowStep: {
      ...defaultFlowStep,
      variant: FlowStepVariant.FLOW
    }
  },
  render: defaultRender
};

export const Highlighted: Story = {
  args: { ...defaultArgs, isHighlighted: true },
  render: defaultRender
};

export const Selected: Story = {
  args: { ...defaultArgs, isSelected: true },
  render: defaultRender
};

export const Disabled: Story = {
  args: { ...defaultArgs, isDisabled: true },
  render: defaultRender
};
