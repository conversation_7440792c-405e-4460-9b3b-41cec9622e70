package main

import (
    "fmt"
    "os"
    "os/exec"
)

func main() {
    requiredVars := []string{
        "SPICEDB_DATASTORE_ENGINE",
        "SPICEDB_AUTH_TOKEN",
        "SPICEDB_DATASTORE_CONNECTION_STRING",
        "SPICEDB_MIGRATION_DATASTORE_CONNECTION_STRING",
    }

    env := make(map[string]string)
    missing := []string{}

    for _, key := range requiredVars {
        val := os.Getenv(key)
        env[key] = val
        if val == "" {
            missing = append(missing, key)
        }
    }

    if len(missing) > 0 {
        fmt.Fprintf(os.Stderr, "Missing required environment variables:\n")
        for _, key := range missing {
            fmt.Fprintf(os.Stderr, "  %s\n", key)
        }
        os.Exit(1)
    }

    paramEngine := "--datastore-engine=" + env["SPICEDB_DATASTORE_ENGINE"]
    paramToken := "--grpc-preshared-key=" + env["SPICEDB_AUTH_TOKEN"]

    // Migrate
    {
        paramMigrationConnectionString := "--datastore-conn-uri=" + env["SPICEDB_MIGRATION_DATASTORE_CONNECTION_STRING"]

        fmt.Println("Running spicedb datastore migrate head...")

        migrateCmd := exec.Command("spicedb", "datastore", "migrate", "head", paramEngine, paramMigrationConnectionString)
        migrateCmd.Stdout = os.Stdout
        migrateCmd.Stderr = os.Stderr
        migrateCmd.Env = os.Environ()

        if err := migrateCmd.Run(); err != nil {
            fmt.Printf("Migration failed: %v\n", err)
            os.Exit(1)
        }

        fmt.Println("Migration completed successfully")
    }

    // Serve
    {
        paramConnectionString := "--datastore-conn-uri=" + env["SPICEDB_DATASTORE_CONNECTION_STRING"]

        fmt.Println("Starting spicedb serve...")

        serveCmd := exec.Command("spicedb", "serve", paramToken, paramEngine, paramConnectionString, "--http-enabled=true")
        serveCmd.Stdout = os.Stdout
        serveCmd.Stderr = os.Stderr
        serveCmd.Env = os.Environ()

        if err := serveCmd.Run(); err != nil {
            fmt.Printf("Serve failed: %v\n", err)
            os.Exit(1)
        }
    }
}