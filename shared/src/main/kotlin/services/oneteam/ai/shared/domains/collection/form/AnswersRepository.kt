package services.oneteam.ai.shared.domains.collection.form

import kotlinx.serialization.*
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.*
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.workspace.*
import java.math.BigDecimal

typealias NumberAsString = String

@Serializable
@OptIn(ExperimentalSerializationApi::class)
@JsonClassDiscriminator("type")
sealed class FormAnswer<T> {
    @OptIn(ExperimentalSerializationApi::class)
    @EncodeDefault
    abstract val type: QuestionType

    abstract val value: T?

    abstract val questionId: BaseSection.Id

    abstract fun toJsonElement(workspace: Workspace.ForJson): JsonElement

    @Serializable
    @SerialName("text")
    data class TextAnswer(
        override val questionId: BaseSection.Id, override val value: String? = null
    ) : FormAnswer<String>() {
        override val type: QuestionType
            get() = QuestionType.TEXT

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            return if (value != null) JsonPrimitive(value) else JsonNull
        }
    }

    @Serializable
    @SerialName("number")
    data class NumberAnswer(
        override val questionId: BaseSection.Id, override val value: NumberAsString? = null
    ) : FormAnswer<NumberAsString>() {
        override val type: QuestionType
            get() = QuestionType.NUMBER

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            return if (!value.isNullOrEmpty()) {
                JsonPrimitive(BigDecimal(value).toDouble())
            } else {
                JsonNull
            }
        }
    }

    @Serializable
    @SerialName("boolean")
    data class BooleanAnswer(
        override val questionId: BaseSection.Id, override val value: Boolean? = null
    ) : FormAnswer<Boolean>() {
        override val type: QuestionType
            get() = QuestionType.BOOLEAN

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            return TypeToJsonElementConverter.convert(VariableDataType.BOOLEAN, value)
        }

    }

    @Serializable
    @SerialName("date")
    data class DateAnswer(
        override val questionId: BaseSection.Id, override val value: String? = null
    ) : FormAnswer<String>() {
        override val type: QuestionType
            get() = QuestionType.DATE

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            return TypeToJsonElementConverter.convert(VariableDataType.DATE, value)
        }
    }

    @Serializable
    @SerialName("select")
    data class SelectAnswer(
        override val questionId: BaseSection.Id, override val value: String? = null
    ) : FormAnswer<String>() {
        override val type: QuestionType
            get() = QuestionType.SELECT

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            return if (value != null) JsonPrimitive(value) else JsonNull
        }
    }

    @Serializable
    @SerialName("multiSelect")
    data class MultiSelectAnswer(
        override val questionId: BaseSection.Id, override val value: List<JsonElement>? = emptyList()
    ) : FormAnswer<List<JsonElement>>() {
        override val type: QuestionType
            get() = QuestionType.MULTISELECT

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            return JsonArray(value ?: emptyList())
        }

    }

    @Serializable(with = RowMapSerializer::class)
    data class RowMap(
        val _rowId: RowId,
        val _rowIndex: Int,
        private val delegate: Map<String, TableAnswer.FormatedTableAnswer.FormattedTableAnswerRow>
    ) : Map<String, TableAnswer.FormatedTableAnswer.FormattedTableAnswerRow> by delegate

    object RowMapSerializer : KSerializer<RowMap> {
        override val descriptor: SerialDescriptor = buildClassSerialDescriptor("RowMap")
        override fun serialize(encoder: Encoder, value: RowMap) {
            if (encoder !is JsonEncoder) throw SerializationException("Json only")
            val jsonObject = buildJsonObject {
                value.forEach { (k, v) -> put(k, encoder.json.encodeToJsonElement(v.answer)) }
                put("_rowId", Json.encodeToJsonElement(value._rowId))
                put("_rowIndex", Json.encodeToJsonElement(value._rowIndex))
            }
            encoder.encodeJsonElement(jsonObject)
        }

        // Deserialization will not work with the result of Serialization above
        // as for FormattedTableAnswerRow serialization, it only serializes the answer field and not the whole object
        override fun deserialize(decoder: Decoder): RowMap {
            if (decoder !is JsonDecoder) throw SerializationException("Json only")
            val jsonObject = decoder.decodeJsonElement().jsonObject
            val rowId = jsonObject["_rowId"]?.jsonPrimitive?.content ?: throw SerializationException("Missing _rowId")
            val rowIndex = jsonObject["_rowIndex"]?.jsonPrimitive?.toString()?.toIntOrNull()
                ?: throw SerializationException("Missing _rowIndex")
            val map = jsonObject.filterKeys { it != "_rowId" && it != "_rowIndex" }.mapValues {
                decoder.json.decodeFromJsonElement<TableAnswer.FormatedTableAnswer.FormattedTableAnswerRow>(it.value)
            }
            return RowMap(RowId(rowId), rowIndex, map)
        }
    }

    @Serializable(with = ColumnMapSerializer::class)
    data class ColumnMap(
        val ids: List<BaseSection.Id>,
        private val delegate: Map<String, TableAnswer.FormatedTableAnswer.FormattedTableAnswerColumn>
    ) : Map<String, TableAnswer.FormatedTableAnswer.FormattedTableAnswerColumn> by delegate

    object ColumnMapSerializer : KSerializer<ColumnMap> {
        override val descriptor: SerialDescriptor = buildClassSerialDescriptor("ColumnMap")
        override fun serialize(encoder: Encoder, value: ColumnMap) {
            if (encoder !is JsonEncoder) throw SerializationException("Json only")
            val jsonObject = buildJsonObject {
                value.forEach { (k, v) -> put(k, encoder.json.encodeToJsonElement(v)) }
                put("ids", Json.encodeToJsonElement(value.ids))
            }
            encoder.encodeJsonElement(jsonObject)
        }

        override fun deserialize(decoder: Decoder): ColumnMap {
            if (decoder !is JsonDecoder) throw SerializationException("Json only")
            val jsonObject = decoder.decodeJsonElement().jsonObject
            val ids = jsonObject["ids"]?.jsonPrimitive?.jsonArray ?: throw SerializationException("Missing ids")
            val map = jsonObject.filterKeys { it != "ids" }.mapValues {
                decoder.json.decodeFromJsonElement<TableAnswer.FormatedTableAnswer.FormattedTableAnswerColumn>(it.value)
            }
            return ColumnMap(ids.map { BaseSection.Id(it.toString()) }, map)
        }
    }

    @Serializable
    @SerialName("table")
    data class TableAnswer(
        override val questionId: BaseSection.Id, override val value: OrderedMap<RowId, TableAnswerRow>
    ) : FormAnswer<OrderedMap<RowId, TableAnswerRow>>() {
        override val type: QuestionType get() = QuestionType.TABLE

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            val thisQuestion =
                workspace.forms.values.firstNotNullOf { it.findQuestion(questionId) } as BaseSection.TableQuestion
            return Json.encodeToJsonElement(formatTableIntoJsonPathableStructure(thisQuestion, workspace))
        }

        private fun formatTableIntoJsonPathableStructure(
            thisQuestion: BaseSection.TableQuestion, workspace: Workspace.ForJson
        ): FormatedTableAnswer {
            return FormatedTableAnswer(
                id = questionId,
                type = this.type,
                text = thisQuestion.text,
                answer = value.toList()
                    .mapIndexed { index, it -> it.formatTableGetAnswers((index + 1), thisQuestion, workspace) },
                columns = formatTableGetColumns(thisQuestion, workspace),
            )
        }

        private fun formatTableGetColumns(
            thisQuestion: BaseSection.TableQuestion, workspace: Workspace.ForJson
        ): ColumnMap {
            val columnsList = thisQuestion.properties?.columns?.map { configurationColumn ->
                val answerValue = value.toList().map { tableAnswerRow ->
                    val cell = tableAnswerRow.findAnswerByQuestionId(configurationColumn.id)
                    if (cell is ListAnswer) {
                        cell.toDirectValueJsonElement(workspace)
                    } else {
                        cell?.toJsonElement(workspace) ?: JsonNull
                    }
                }
                FormatedTableAnswer.FormattedTableAnswerColumn(
                    id = configurationColumn.id,
                    type = configurationColumn.type,
                    text = configurationColumn.text,
                    answer = answerValue
                )
            }
            return ColumnMap(ids = thisQuestion.properties?.columns?.map { it.id } ?: emptyList(),
                delegate = columnsList?.associate { column ->
                    column.id.value to column
                } ?: emptyMap())
        }

        private fun TableAnswerRow.formatTableGetAnswers(
            rowIndex: Int, thisQuestion: BaseSection.TableQuestion, workspace: Workspace.ForJson
        ): RowMap {
            val columnsMap = this.columns.formatColumns(thisQuestion, workspace)
            return RowMap(_rowId = this.id, _rowIndex = rowIndex, delegate = columnsMap)
        }

        private fun Map<BaseSection.Id, FormAnswer<*>>.formatColumns(
            thisQuestion: BaseSection.TableQuestion, workspace: Workspace.ForJson
        ): Map<String, FormatedTableAnswer.FormattedTableAnswerRow> {
            return thisQuestion.properties?.columns?.associate {
                val answer = this[it.id]
                var answerValue =
                    if (answer == null || answer.value == null) JsonPrimitive("") else answer.toJsonElement(
                        workspace
                    )
                if (answer is ListAnswer) {
                    answerValue = answer.toDirectValueJsonElement(workspace)
                }
                it.id.value to FormatedTableAnswer.FormattedTableAnswerRow(
                    id = it.id, type = it.type, answer = answerValue
                )
            } ?: emptyMap()
        }

        @Serializable
        class FormatedTableAnswer(
            val id: BaseSection.Id,
            val type: QuestionType,
            val text: String? = null,
            val answer: List<RowMap>,
            val columns: ColumnMap,
            val rows: List<JsonObject>? = null
        ) {
            @Serializable
            class FormattedTableAnswerRow(
                val id: BaseSection.Id, val type: QuestionType, val answer: JsonElement
            )

            @Serializable
            class FormattedTableAnswerColumn(
                val id: BaseSection.Id, val type: QuestionType, val answer: List<JsonElement>, val text: String? = null
            )
        }
    }


    @Serializable
    @SerialName("json")
    data class JsonAnswer(
        override val questionId: BaseSection.Id, override val value: Map<BaseSection.Id, FormAnswer<*>>
    ) : FormAnswer<Map<BaseSection.Id, FormAnswer<*>>>() {
        override val type: QuestionType
            get() = QuestionType.JSON

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            val map = mutableMapOf<String, JsonElement>()
            value.forEach { (key, answer) ->
                map[key.value] = answer.toJsonElement(workspace = workspace)
            }
            return JsonObject(map)
        }
    }

    @Serializable(with = ListItemMapSerializer::class)
    data class ListItemMap(
        val _itemId: ItemId,
        val _itemIndex: Int,
        private val delegate: Map<String, ListAnswer.FormatedListAnswer.FormattedListAnswerItem>
    ) : Map<String, ListAnswer.FormatedListAnswer.FormattedListAnswerItem> by delegate

    object ListItemMapSerializer : KSerializer<ListItemMap> {
        override val descriptor: SerialDescriptor = buildClassSerialDescriptor("ListItemMap")
        override fun serialize(encoder: Encoder, value: ListItemMap) {
            if (encoder !is JsonEncoder) throw SerializationException("Json only")
            val jsonObject = buildJsonObject {
                value.forEach { (k, v) -> put(k, encoder.json.encodeToJsonElement(v.answer)) }
                put("_itemId", Json.encodeToJsonElement(value._itemId))
                put("_itemIndex", Json.encodeToJsonElement(value._itemIndex))
            }
            encoder.encodeJsonElement(jsonObject)
        }

        // Deserialization will not work with the result of Serialization above
        // as for FormattedListAnswerItem serialization, it only serializes the answer field and not the whole object
        override fun deserialize(decoder: Decoder): ListItemMap {
            if (decoder !is JsonDecoder) throw SerializationException("Json only")
            val jsonObject = decoder.decodeJsonElement().jsonObject
            val itemId =
                jsonObject["_itemId"]?.jsonPrimitive?.content ?: throw SerializationException("Missing _itemId")
            val itemIndex = jsonObject["_itemIndex"]?.jsonPrimitive?.toString()?.toIntOrNull()
                ?: throw SerializationException("Missing _itemIndex")
            val map = jsonObject.filterKeys { it != "_itemId" && it != "_itemIndex" }.mapValues {
                decoder.json.decodeFromJsonElement<ListAnswer.FormatedListAnswer.FormattedListAnswerItem>(it.value)
            }
            return ListItemMap(ItemId(itemId), itemIndex, map)
        }
    }

    @Serializable(with = ItemMapSerializer::class)
    data class ItemMap(
        val ids: List<BaseSection.Id>,
        private val delegate: Map<String, ListAnswer.FormatedListAnswer.FormattedItemAnswer>
    ) : Map<String, ListAnswer.FormatedListAnswer.FormattedItemAnswer> by delegate

    object ItemMapSerializer : KSerializer<ItemMap> {
        override val descriptor: SerialDescriptor = buildClassSerialDescriptor("ItemMap")
        override fun serialize(encoder: Encoder, value: ItemMap) {
            if (encoder !is JsonEncoder) throw SerializationException("Json only")
            val jsonObject = buildJsonObject {
                value.forEach { (k, v) -> put(k, encoder.json.encodeToJsonElement(v)) }
                put("ids", Json.encodeToJsonElement(value.ids))
            }
            encoder.encodeJsonElement(jsonObject)
        }

        override fun deserialize(decoder: Decoder): ItemMap {
            if (decoder !is JsonDecoder) throw SerializationException("Json only")
            val jsonObject = decoder.decodeJsonElement().jsonObject
            val ids = jsonObject["ids"]?.jsonPrimitive?.jsonArray ?: throw SerializationException("Missing ids")
            val map = jsonObject.filterKeys { it != "ids" }.mapValues {
                decoder.json.decodeFromJsonElement<ListAnswer.FormatedListAnswer.FormattedItemAnswer>(it.value)
            }
            return ItemMap(ids.map { BaseSection.Id(it.toString()) }, map)
        }
    }

    @Serializable
    @SerialName("list")
    data class ListAnswer(
        override val questionId: BaseSection.Id, override val value: OrderedMap<ItemId, ListAnswerItem>
    ) : FormAnswer<OrderedMap<ItemId, ListAnswerItem>>() {
        override val type: QuestionType
            get() = QuestionType.LIST

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            val thisQuestion = findQuestionRecursively(workspace, questionId) as? BaseSection.ListQuestion
                ?: throw SerializationException("Missing list question with ID ${questionId.value}")
            return Json.encodeToJsonElement(formatListIntoJsonPathableStructure(thisQuestion))
        }

        // Instead of returning a structure containing .answer, _itemId, etc. this returns the direct answer list e.g. ['a', 'b', 'c']
        fun toDirectValueJsonElement(workspace: Workspace.ForJson): JsonElement {
            val thisQuestion = findQuestionRecursively(workspace, questionId) as? BaseSection.ListQuestion
                ?: throw SerializationException("Missing list question with ID ${questionId.value}")
            return Json.encodeToJsonElement(
                value.toList().flatMapIndexed { index, it ->
                    it.formatListGetAnswers((index + 1), thisQuestion).values.map {
                        it.answer
                    }
                })
        }

        private fun findQuestionRecursively(
            workspace: Workspace.ForJson, questionId: BaseSection.Id
        ): BaseSection.BaseQuestion? {
            return workspace.forms.values.flatMap { it.listAllQuestions() }
                .firstNotNullOfOrNull { findQuestionInMultiLevelQuestion(it, questionId) }
        }

        private fun findQuestionInMultiLevelQuestion(
            question: BaseSection.BaseQuestion, questionId: BaseSection.Id
        ): BaseSection.BaseQuestion? {
            if (question.id == questionId) return question
            val nestedQuestions = when (question) {
                is BaseSection.JsonQuestion -> question.properties?.items ?: emptyList()
                is BaseSection.ListQuestion -> question.properties?.items ?: emptyList()
                is BaseSection.TableQuestion -> question.properties?.columns ?: emptyList()
                else -> emptyList()
            }
            return nestedQuestions.firstNotNullOfOrNull { findQuestionInMultiLevelQuestion(it, questionId) }
        }

        private fun formatListIntoJsonPathableStructure(thisQuestion: BaseSection.ListQuestion): FormatedListAnswer {
            return FormatedListAnswer(
                id = questionId,
                type = this.type,
                answer = value.toList().mapIndexed { index, it -> it.formatListGetAnswers((index + 1), thisQuestion) },
                items = formatListGetItems(thisQuestion)
            )
        }

        private fun ListAnswerItem.formatListGetAnswers(
            itemIndex: Int, thisQuestion: BaseSection.ListQuestion
        ): ListItemMap {
            val itemMap = this.item.formatItems(thisQuestion)
            return ListItemMap(_itemId = this.id, _itemIndex = itemIndex, delegate = itemMap)
        }

        @OptIn(ExperimentalSerializationApi::class)
        private fun Map<BaseSection.Id, FormAnswer<*>>.formatItems(
            thisQuestion: BaseSection.ListQuestion,
        ): Map<String, FormatedListAnswer.FormattedListAnswerItem> {
            return thisQuestion.properties?.items?.associate { itemQuestion ->
                itemQuestion.id.value to FormatedListAnswer.FormattedListAnswerItem(
                    id = itemQuestion.id,
                    type = itemQuestion.type,
                    answer = getListItemValueAsType(itemQuestion, this[itemQuestion.id]?.value)
                )
            } ?: emptyMap()
        }

        private fun formatListGetItems(thisQuestion: BaseSection.ListQuestion): ItemMap {
            val itemsList = thisQuestion.properties?.items?.map { itemQuestion ->
                FormatedListAnswer.FormattedItemAnswer(
                    id = itemQuestion.id, type = itemQuestion.type, answer = value.toList().map { listAnswerItem ->
                        getListItemValueAsType(
                            itemQuestion,
                            listAnswerItem.item.values.firstOrNull { it.questionId == itemQuestion.id }?.value
                        )
                    })
            }
            return ItemMap(ids = thisQuestion.properties?.items?.map { it.id } ?: emptyList(),
                delegate = itemsList?.associate { item ->
                    item.id.value to item
                } ?: emptyMap())
        }

        private fun getListItemValueAsType(itemQuestion: BaseSection.BaseQuestion, value: Any?): JsonElement {
            if (value == null) {
                JsonNull
            }

            // So far we only support text (string), date (string) or number (int) types of in list items
            return when (itemQuestion.type) {
                QuestionType.TEXT, QuestionType.DATE -> JsonPrimitive(value?.toString() ?: "")
                QuestionType.NUMBER -> JsonPrimitive(value?.toString()?.toBigDecimalOrNull())
                else -> throw IllegalArgumentException("Unsupported list item question type: ${itemQuestion.type}")
            }
        }

        @Serializable
        class FormatedListAnswer(
            val id: BaseSection.Id,
            val type: QuestionType,
            val answer: List<ListItemMap>,
            val items: ItemMap,
            val rows: List<JsonObject>? = null
        ) {
            @Serializable
            class FormattedListAnswerItem(
                val id: BaseSection.Id, val type: QuestionType, val answer: JsonElement
            )

            @Serializable
            class FormattedItemAnswer(
                val id: BaseSection.Id, val type: QuestionType, val answer: List<JsonElement>
            )
        }
    }

    @Serializable
    @SerialName("files")
    data class FileAnswer(
        override val questionId: BaseSection.Id, override val value: List<FileAnswerValue>
    ) : FormAnswer<List<FileAnswerValue>>() {
        override val type: QuestionType
            get() = QuestionType.FILES

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            val files = mutableListOf<JsonObject>()
            value.forEach { file ->
                files.add(
                    JsonObject(mapOf("path" to JsonPrimitive(file.path), "name" to JsonPrimitive(file.name)))
                )
            }
            return JsonArray(files)
        }
    }

    @Serializable
    data class ExpectedPayload(
        val sample: JsonElement?, val schema: BaseSection.BaseQuestion
    )

    @Serializable
    @SerialName("schema")
    data class SchemaAnswer(
        override val questionId: BaseSection.Id, override val value: ExpectedPayload?
    ) : FormAnswer<ExpectedPayload>() {

        override val type: QuestionType
            get() = QuestionType.SCHEMA

        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            if (value == null) {
                return JsonNull
            }
            val sample = value.sample
            val schema = value.schema

            return JsonObject(
                mapOf(
                    "sample" to (sample ?: JsonNull), "schema" to Json.encodeToJsonElement(schema)
                )
            )
        }
    }

    @Serializable
    @SerialName("chart")
    data class ChartAnswer(
        override val questionId: BaseSection.Id, override val value: String? = null
    ) : FormAnswer<String>() {
        override val type: QuestionType
            get() = QuestionType.CHART

        // Chart answers are not needed in collection
        override fun toJsonElement(workspace: Workspace.ForJson): JsonElement {
            return JsonNull
        }
    }

    @Serializable
    data class ForJson(
        val id: Form.Id, val answers: Map<BaseSection.Id, FormAnswer<*>>
    ) : CollaborationDocument {
        override val type = CollaborationDocumentType.FORM_ANSWER

        override fun descriptor(): String {
            return id.value.toString()
        }

        fun getAnswer(tableQuestionId: BaseSection.Id, columnQuestionId: BaseSection.Id, rowId: RowId): FormAnswer<*> {
            var tableAnswer: TableAnswer? = answers[tableQuestionId] as TableAnswer?
            if (tableAnswer == null) {
                throw IllegalArgumentException("Answer for table question $tableQuestionId found")
            }

            val row: TableAnswerRow? = tableAnswer.value.entities[rowId]
            if (row == null) {
                throw IllegalArgumentException("Row $rowId for table question $tableQuestionId not found")
            }

            val answer = row.columns[columnQuestionId]
            if (answer == null) {
                throw IllegalArgumentException("Answer $columnQuestionId for Row $rowId for table question $tableQuestionId not found")
            }

            return answer
        }

        /**
         * Returns the answer if it exists, otherwise returns null. If we want to represent all the answers even when not answered,
         * then this should be done at a higher level.
         */
        fun getAnswer(questionId: BaseSection.Id): FormAnswer<*>? {
            return answers[questionId]
        }

    }
}
