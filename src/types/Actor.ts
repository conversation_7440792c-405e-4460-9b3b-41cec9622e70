export enum ActorType {
  USER = "user",
  FLOW = "flow",
  SYSTEM = "system"
}

export type UserActor = {
  type: `${ActorType.USER}`;
  userId: number;
};

export type FlowActor = {
  type: `${ActorType.FLOW}`;
  workspaceId: string;
  flowConfigurationId: string;
  stepId?: string;
  flowExecutionId: string;
};

export type SystemActor = {
  type: `${ActorType.SYSTEM}`;
};

export type Actor = UserActor | FlowActor | SystemActor;
