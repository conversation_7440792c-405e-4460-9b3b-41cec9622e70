import React from "react";

import { Form } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { z } from "zod";

import { QuestionTypes, getQuestionSchema } from "@src/types/Question.ts";

import { CollectionQuestionBlock } from "./CollectionQuestionBlock.tsx";

const meta: Meta<typeof CollectionQuestionBlock> = {
  component: CollectionQuestionBlock,
  title: "collection/CollectionQuestionBlock"
};

export default meta;

type Story = StoryObj<typeof CollectionQuestionBlock>;

const mockQuestion = {
  id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
  identifier: "WhatIsYourName",
  type: QuestionTypes.TEXT,
  text: "What is your name?",
  properties: {
    required: true,
    allowReuseAcrossForms: false,
    minLength: 1,
    maxLength: 100,
    defaultValue: "default"
  }
};

const defaultArgs: Story["args"] = {
  question: mockQuestion
};

const defaultRenderer = (args: Story["args"]) => {
  return (
    <Form
      schema={z.object({ question: getQuestionSchema() })}
      d={() => ""}
      handleSubmit={() => console.log("Submit")}
      defaultValues={{
        question: mockQuestion
      }}
      hideFormButtons
    >
      <CollectionQuestionBlock {...args} question={mockQuestion} />
    </Form>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRenderer
};
