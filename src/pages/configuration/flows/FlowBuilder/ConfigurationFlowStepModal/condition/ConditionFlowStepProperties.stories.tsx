import React from "react";

import { ChangeFn } from "@automerge/automerge/next";
import { Form, Stack } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { z } from "zod";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { Operators } from "@src/types/FlowConfiguration/Condition.ts";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep.ts";
import { ConditionStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { MockOutletContextProvider } from "../MockOutletContextProvider.tsx";
import {
  MockFlowStep,
  sampleWorkspaceDocumentForFlow
} from "../storyConstants.ts";
import { ConditionFlowStepProperties } from "./ConditionFlowStepProperties.tsx";

const contextValue = {
  docChange: (changeFn: ChangeFn<WorkspaceDocument>) => {
    changeFn(sampleWorkspaceDocumentForFlow);
  }
};

const meta: Meta<typeof ConditionFlowStepProperties> = {
  component: ConditionFlowStepProperties,
  title: "collection/ConfigurationFlowStepModal/condition",
  decorators: [
    Story => (
      <MockOutletContextProvider context={contextValue}>
        <Story />
      </MockOutletContextProvider>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ConditionFlowStepProperties>;

const mockCondition = MockFlowStep["3"] as FlowStep<ConditionStepProperties>;

const defaultArgs: Story["args"] = {
  step: mockCondition
};

const defaultRender = (args: Story["args"]) => {
  const pathInDocument = [
    "flows",
    "entities",
    "flow-id-1",
    "steps",
    "3",
    "properties"
  ];

  return (
    <Stack
      gap="100"
      width="fill"
      overflow="auto"
      style={{ maxHeight: "100vh" }}
    >
      <Form
        schema={z.any()}
        d={(key: string) => key}
        handleSubmit={() => console.log("Submit")}
        hideFormButtons
      >
        <ConditionFlowStepProperties
          {...args}
          step={mockCondition}
          onBranchAdd={() => {
            // This doesn't work as expected since we aren't mocking document accurately
            contextValue.docChange(d => {
              const properties = getByPath<ConditionStepProperties>(
                d,
                pathInDocument
              );
              properties.branches.push({
                name: `If ${properties.branches.length + 1}`,
                condition: {
                  lhs: "",
                  operator: Operators.EQUALS,
                  rhs: ""
                },
                next: null
              });
            });
          }}
          onBranchRemove={index => {
            // This doesn't work as expected since we aren't mocking document accurately
            contextValue.docChange(d => {
              const properties = getByPath<ConditionStepProperties>(
                d,
                pathInDocument
              );
              properties.branches.splice(index, 1);
            });
          }}
          docAccessor={pathInDocument}
        />
      </Form>
    </Stack>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRender
};
