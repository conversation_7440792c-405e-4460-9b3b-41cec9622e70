import React, { useMemo } from "react";
import { useOutletContext } from "react-router-dom";

import { Doc } from "@automerge/automerge-repo";
import { Box, Divider, Inline, Stack, WhiteboardLine } from "@oneteam/onetheme";
import { useNodes, useReactFlow } from "@xyflow/react";

import { getVariablesForStep } from "@helpers/flows/flowVariables/flowVariableHelpers";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { ConfigurationFlowVariable } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowVariable/ConfigurationFlowVariable";
import { FlowAddButton } from "@pages/configuration/flows/FlowCanvas/FlowAddButton/FlowAddButton";
import { FlowCanvas } from "@pages/configuration/flows/FlowCanvas/FlowCanvas";
import { getParentPath } from "@pages/configuration/flows/FlowCanvas/FlowCanvasHelper";
import { useFlowStepHandlers } from "@pages/configuration/flows/hooks/useFlowStepHandlers";

import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStepId,
  FlowStep as FlowStepType,
  FlowStepVariant,
  flowStepVariantsWithTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  CommonTypedStepProperties,
  IteratorStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { FlowStepTypeConfiguration } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { CanvasType } from "../../CanvasType";
import { useFlowCanvas } from "../../FlowCanvasContext";
import { FlowNodeFactory } from "../../FlowNodeFactory";
import { IteratorFlowStepWrapper } from "./IteratorFlowStepWrapper";

interface IteratorFlowStepProps {
  canvasType: CanvasType;
  flowFactory: FlowNodeFactory;
  flowStep: FlowStepType<IteratorStepProperties>;
  name?: string;
  readonly variant?: FlowStepVariant.ITERATOR;
  isSelected?: boolean;
  onClick?: (stepId: FlowStepId) => void;
  handleDelete?: (stepId: FlowStepId, variant: FlowStepVariant) => void;
  handleDuplicate?: (stepId: FlowStepId) => void;
  isDisabled?: boolean;
  iteratorDepth?: number;
  style?: React.CSSProperties;
  className?: string;
  flowStepTypeConfiguration?: FlowStepTypeConfiguration;
  hasConnections?: boolean;
}

export const IteratorFlowStep = (props: IteratorFlowStepProps) => {
  const {
    flowStep,
    canvasType,
    onClick,
    handleDelete,
    handleDuplicate,
    hasConnections,
    flowFactory,
    isSelected = false
    // isDisabled = false,
    // style = {},
    // iteratorDepth = 1,
    // className = "",
    // flowStepTypeConfiguration
  } = props;

  const { mockFlowContext, flowStepTypeConfigByPrimaryIdentifier } =
    useConfigurationFlowContext();

  const { document } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();

  const { outputVariables } = useMemo(() => {
    const flowStepTypeConfig = flowStepVariantsWithTypeConfiguration.includes(
      flowStep?.variant as FlowStepVariant
    )
      ? flowStepTypeConfigByPrimaryIdentifier?.[
          (flowStep?.properties as CommonTypedStepProperties)
            ?.typePrimaryIdentifier
        ]
      : undefined;

    const { variables, stepOutputVariables } = getVariablesForStep({
      step: flowStep,
      workspaceDocument: document,
      flowStepTypeConfig,
      flowContext: mockFlowContext
    });
    const outputVariables = variables.filter(variable => {
      return stepOutputVariables?.includes(variable.identifier);
    });
    return { outputVariables };
  }, [
    document,
    flowStep,
    flowStepTypeConfigByPrimaryIdentifier,
    mockFlowContext
  ]);

  const { handleAddFlowStep, handlePasteFlowStep } = useFlowStepHandlers();

  const iteratorFlowInstance = useReactFlow();
  const nodes = useNodes();

  const containerBounds = useMemo(() => {
    if (!iteratorFlowInstance || nodes.length === 0) {
      return { width: 100, height: 100 };
    }

    return iteratorFlowInstance.getNodesBounds(nodes);
  }, [iteratorFlowInstance, nodes]);

  const { path } = useFlowCanvas();
  const { setPath } = useConfigurationFlowContext();

  const bottomContents = useMemo(() => {
    return (
      <>
        {outputVariables.length > 0 && (
          <>
            <Box
              className="flow-step__internal-divider"
              style={{ height: "var(--spacing-100)" }}
              alignment="center"
            >
              <Divider direction="vertical" />
            </Box>
            <Inline
              className="flow-step__variables"
              alignment="center"
              gap="050"
              style={{
                maxWidth: "100%"
              }}
              wrap
            >
              {outputVariables.map(variable => {
                return (
                  <ConfigurationFlowVariable
                    variable={variable}
                    variant="default"
                    key={`iterator-variable-${variable.id ?? variable.identifier}-${flowStep.id}`}
                  />
                );
              })}
            </Inline>
          </>
        )}
        <Box
          position="absolute"
          width="100"
          alignment="center"
          style={{ top: "calc(100% + 20px)", zIndex: 1 }}
        >
          {hasConnections && (
            <Stack alignment="center">
              <WhiteboardLine className="flow-step-last-edge" />
              <FlowAddButton
                addStep={variant => {
                  setPath(getParentPath(path));
                  handleAddFlowStep({
                    variant,
                    parentStepId: flowStep.id,
                    path: getParentPath(path)
                  });
                }}
                pasteStep={async clipboardStep => {
                  setPath(getParentPath(path));
                  handlePasteFlowStep({
                    parentStepId: flowStep.id,
                    skipTypeSelect: true,
                    path: getParentPath(path),
                    clipboardStep
                  });
                }}
              />
            </Stack>
          )}
        </Box>
      </>
    );
  }, [
    outputVariables,
    flowStep.id,
    hasConnections,
    handleAddFlowStep,
    handlePasteFlowStep,
    path,
    setPath
  ]);

  if (!containerBounds) {
    return <></>;
  }

  const verticalPadding = 174;

  return (
    <IteratorFlowStepWrapper
      {...props}
      isSelected={isSelected}
      onClick={() => onClick?.(flowStep.id)}
      handleDelete={
        handleDelete
          ? () => handleDelete(flowStep.id, flowStep.variant as FlowStepVariant)
          : undefined
      }
      handleDuplicate={
        handleDuplicate ? () => handleDuplicate(flowStep.id) : undefined
      }
      bottomContents={bottomContents}
    >
      <div
        style={{
          width: containerBounds.width,
          height: containerBounds.height + verticalPadding,
          position: "relative"
        }}
        className="iterator-flow-step__container"
      >
        <FlowCanvas
          flowFactory={flowFactory}
          canvasType={canvasType}
          flow={flowStep.properties.configuration as FlowConfiguration}
          spacing={{ x: 150, y: 100 }}
          isIterator
          stepIdOnParentFlow={flowStep.id}
        />
      </div>
    </IteratorFlowStepWrapper>
  );
};
