import React from "react";

import type { <PERSON>a, StoryObj } from "@storybook/react";

import { DeleteConfirmationModal } from "./DeleteConfirmationModal";

const meta: Meta<typeof DeleteConfirmationModal> = {
  component: DeleteConfirmationModal,
  title: "components/Modal/ConfirmationModal/DeleteConfirmationModal",
  parameters: {
    docs: {
      description: {
        component:
          "Use with <Floating position={FloatingPosition.CENTER}> to center the modal on the screen."
      }
    }
  },
  argTypes: {},
  args: {}
};

export default meta;

type Story = StoryObj<typeof DeleteConfirmationModal>;

const defaultArgs: Story["args"] = {
  heading: "Delete"
};

const defaultRenderer = (args: Story["args"]) => {
  return (
    <DeleteConfirmationModal
      {...args}
      message={args?.message ?? "Are you sure you want to delete?"}
      onConfirm={() => {
        console.log("Confirmed");
      }}
      onCancel={() => {
        console.log("Cancelled");
      }}
    />
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRenderer
};

export const WithTextEntry: Story = {
  args: {
    ...defaultArgs,
    heading: "Delete workspace",
    message:
      "Are you sure you want to delete 'Workspace ABC'? This action is irreversible",
    confirmLabel: "Delete",
    cancelLabel: "Cancel",
    withTextEntry: true,
    textEntry: {
      label: "Type the name of the workspace to confirm",
      expectedValue: "Workspace ABC"
    }
  },
  render: defaultRenderer
};
