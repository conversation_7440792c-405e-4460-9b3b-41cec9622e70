import React, { useMemo } from "react";

import { Stack } from "@oneteam/onetheme";

import { getIteratorStartingVariables } from "@helpers/flows/flowVariables/flowVariableHelpers";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { FieldDivider } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowStepModal/ConfigurationFlowBlockFields/FieldDivider";

import { useDictionary } from "@src/hooks/useDictionary";
import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  ExecutionStep,
  FlowExecutionDocument,
  FlowResult
} from "@src/types/FlowConfiguration/FlowExecution";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { IteratorStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

import { useFlowExecutionContext } from "../../FlowExecutionContext";
import { getResolvedVariableValue } from "../../helpers/flowExecutionVariableHelper";
import { FlowExecutionErrorBox } from "../components/FlowExecutionErrorBox";
import { FlowExecutionVariable } from "../components/FlowExecutionVariable";
import { FlowPath } from "../components/FlowPath";

export const IteratorFields = ({
  iteratorExecutionStep,
  configurationStep
}: {
  iteratorExecutionStep: ExecutionStep<IteratorStepProperties>;
  configurationStep: FlowStep;
}) => {
  const { executionFlowForModal } = useFlowExecutionContext();
  const d = useDictionary();

  const { variablesByPath, variablesByName } = useConfigurationFlowContext();

  const failedStepConfiguration = useMemo(() => {
    const failedStepId = executionFlowForModal?.state.steps.order?.at(-1);
    const failedStep = (configurationStep as FlowStep<IteratorStepProperties>)
      .properties.configuration?.steps[failedStepId ?? ""];
    return failedStep;
  }, [executionFlowForModal?.state.steps.order, configurationStep]);

  const stepVariables = useMemo(() => {
    if (!configurationStep) {
      return;
    }

    if (!configurationStep || !variablesByPath) {
      return;
    }

    const variables = getIteratorStartingVariables(
      configurationStep as FlowStep<IteratorStepProperties>,
      variablesByPath,
      variablesByName
    );

    return variables;
  }, [configurationStep, variablesByName, variablesByPath]);

  return (
    <>
      {executionFlowForModal?.state?.result === FlowResult.FAILED && (
        <FlowExecutionErrorBox
          message={executionFlowForModal?.state.message ?? ""}
          failedStepConfiguration={failedStepConfiguration}
          executionFlow={executionFlowForModal}
          iteratorParentId={configurationStep?.id}
        />
      )}
      <FieldDivider
        label={d("ui.collection.flowExecution.stepModal.iterator.inputLabel")}
      />
      <FlowExecutionVariable
        configurationVariable={{
          type: "list",
          identifier: "input"
        }}
        outputValue={iteratorExecutionStep.properties?.inputs?.list}
        variableFieldValue={
          (configurationStep as FlowStep<IteratorStepProperties>).properties
            .inputs.list as string
        }
      />
      <FieldDivider
        label={d("ui.collection.flowExecution.stepModal.iterator.iteration")}
      />
      <Stack gap="100">
        {Object.values(stepVariables ?? [])?.map(variable => {
          if (!executionFlowForModal) {
            return;
          }
          const resolvedVariable = getResolvedVariableValue(
            variable.identifier,
            executionFlowForModal
          );

          return (
            <FlowExecutionVariable
              key={variable.identifier}
              configurationVariable={variable}
              outputValue={resolvedVariable}
            />
          );
        })}
      </Stack>
      <FlowPath
        iteratorId={configurationStep.id}
        configurationFlow={
          (configurationStep as FlowStep<IteratorStepProperties>).properties
            .configuration as FlowConfiguration
        }
        flowExecution={executionFlowForModal as FlowExecutionDocument}
      />
    </>
  );
};
