import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";

import {
  Breadcrumbs,
  BreadcrumbsItem,
  Inline,
  Stack,
  Text
} from "@oneteam/onetheme";

import { FlowStepVariantPill } from "@components/flows/FlowStepVariantPill/FlowStepVariantPill";
import { FloatingModal } from "@components/shared/FloatingModal/FloatingModal";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import {
  findConfigurationStep,
  findExecutionStep
} from "@pages/configuration/flows/FlowCanvas/FlowCanvasHelper";

import { useDictionary } from "@src/hooks/useDictionary";
import { MockFlowContext } from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStepVariant } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { formatDate } from "../../FlowExecutionHelper";
import { useFlowExecutionContext } from "../FlowExecutionContext";
import { useExecutionStepMetadata } from "../hooks/useExecutionMetadata";
import { FlowExecutionCanvasStepModalFields } from "./FlowExecutionCanvasStepModalFields";
import { IteratorPagination } from "./components/IteratorPagination";

export const FlowExecutionCanvasStepModal = ({
  workspaceDocument,
  flowContext
}: {
  workspaceDocument: WorkspaceDocument;
  flowContext: MockFlowContext;
}) => {
  const [width, setWidth] = useState<string | undefined>("450px");
  const {
    setSelectedStepId,
    executionFlow,
    selectedStepId,
    executionFlowForModal,
    setSummaryModalOpen
  } = useFlowExecutionContext();
  const { configurationFlow, path, setPath } = useConfigurationFlowContext();

  const parentStepIds = useMemo(() => {
    return (path ?? []).filter((_, index) => (index - 1) % 4 === 0);
  }, [path]);

  const handleClose = useCallback(() => {
    if (parentStepIds.length === 0 && selectedStepId === undefined) {
      setSelectedStepId(undefined);
      setPath([]);
      setSummaryModalOpen?.(false);
    }

    if (!path) {
      return;
    }

    const parentStepId = parentStepIds[0];
    setSelectedStepId(parentStepId);
    const newPath = path.slice(0, path.length - 4);
    setPath(newPath);
  }, [
    parentStepIds,
    setPath,
    setSelectedStepId,
    path,
    setSummaryModalOpen,
    selectedStepId
  ]);

  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeydown);
    return () => {
      document.removeEventListener("keydown", handleKeydown);
    };
  }, [setSelectedStepId, handleClose]);

  const executionStep = useMemo(() => {
    if (!selectedStepId || !executionFlow) {
      return undefined;
    }

    const executionStep = findExecutionStep(selectedStepId, executionFlow);

    if (executionStep) {
      return executionStep;
    }

    if (executionFlowForModal) {
      return findExecutionStep(selectedStepId, executionFlowForModal);
    }
  }, [executionFlow, selectedStepId, executionFlowForModal]);

  const configurationStep = useMemo(() => {
    if (!selectedStepId || !configurationFlow) {
      return undefined;
    }

    const configurationStep = findConfigurationStep(
      selectedStepId,
      configurationFlow,
      path
    );
    return configurationStep;
  }, [selectedStepId, configurationFlow, path]);

  const executionMetadata = useExecutionStepMetadata(
    selectedStepId ?? "",
    executionFlowForModal,
    []
  );

  const d = useDictionary();

  const intermediateBreadcrumbs = useMemo(() => {
    if (!configurationFlow) {
      return [];
    }

    const intermediateBreadcrumbs = parentStepIds.map((stepId, index) => {
      const intermediateStep = findConfigurationStep(stepId, configurationFlow);
      return (
        <BreadcrumbsItem
          key={`intermediate-${stepId}`}
          text=""
          href=""
          onClick={() => {
            setSelectedStepId(stepId);
            const newPath = path?.splice(0, index * 4);
            if (newPath) {
              setPath(newPath);
            }
          }}
          leftElement={
            <FlowStepVariantPill
              variant={intermediateStep?.variant ?? FlowStepVariant.TRIGGER}
              label={intermediateStep?.name}
            />
          }
        />
      );
    });

    return intermediateBreadcrumbs;
  }, [path, configurationFlow, setSelectedStepId, setPath, parentStepIds]);

  const breadcrumbs = useMemo(() => {
    return [
      <BreadcrumbsItem
        key="summary"
        text={d("ui.collection.flowExecution.stepModal.summary.title")}
        href=""
        isActive={selectedStepId === undefined}
        onClick={
          selectedStepId === undefined
            ? undefined
            : () => {
                setSelectedStepId(undefined);
                setPath([]);
              }
        }
      />,
      ...intermediateBreadcrumbs,
      ...(configurationStep
        ? [
            <BreadcrumbsItem
              key="question"
              text=""
              href=""
              leftElement={
                <FlowStepVariantPill
                  variant={
                    configurationStep?.variant ?? FlowStepVariant.TRIGGER
                  }
                  label={configurationStep?.name}
                />
              }
            />
          ]
        : [])
    ];
  }, [
    configurationStep,
    selectedStepId,
    setSelectedStepId,
    setPath,
    intermediateBreadcrumbs,
    d
  ]);

  const footer = useMemo(() => {
    if (!executionFlowForModal) {
      return <></>;
    }

    let startedAt: string | undefined =
      executionFlowForModal?.state.metadata?.startedAt;
    if (configurationStep && executionMetadata?.metadata?.startedAt) {
      startedAt = executionMetadata.metadata.startedAt;
    }

    let finishedAt: string | undefined =
      executionFlowForModal?.state.metadata?.finishedAt;
    if (configurationStep && executionMetadata?.metadata?.finishedAt) {
      finishedAt = executionMetadata.metadata.finishedAt;
    }

    const start = new Date(startedAt ?? "");
    const end = new Date(finishedAt ?? "");

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return <></>;
    }

    const difference = (end.getTime() - start.getTime()) / 1000;
    return (
      <Stack gap="050" alignment="center">
        {executionStep &&
          configurationStep?.variant === FlowStepVariant.ITERATOR && (
            <IteratorPagination iteratorExecutionStep={executionStep} />
          )}
        <Inline gap="100" spaceBetween width="fill">
          <Text size="m">
            {d("ui.collection.flowExecution.stepModal.footer.executionTime")}:{" "}
            {difference}s
          </Text>
          <Text color="secondary" size="s">
            {formatDate(startedAt ?? "")}
          </Text>
        </Inline>
      </Stack>
    );
  }, [
    d,
    executionStep,
    configurationStep,
    executionFlowForModal,
    executionMetadata
  ]);

  return (
    <FloatingModal
      headingOverride={<Breadcrumbs>{breadcrumbs}</Breadcrumbs>}
      width={width}
      setWidth={setWidth}
      onClose={handleClose}
      footer={footer}
      minWidth="250px"
      className="flow-execution-step-modal"
    >
      <FlowExecutionCanvasStepModalFields
        executionFlow={executionFlowForModal}
        executionStep={executionStep}
        executionMetadata={executionMetadata}
        configurationStep={configurationStep}
        flowContext={flowContext}
        workspaceDocument={workspaceDocument}
      />
    </FloatingModal>
  );
};
