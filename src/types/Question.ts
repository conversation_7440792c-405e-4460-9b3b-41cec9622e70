import { IconType } from "@oneteam/onetheme";
import { ChartSubType, ChartType } from "@oneteam/onetheme";
import { z } from "zod";

import { commonIcons } from "@src/constants/iconConstants.ts";

import {
  BooleanQuestionProperties,
  ChartQuestionProperties,
  CommonQuestionProperties,
  ConfigurationQuestionProperties,
  DateQuestionProperties,
  FilesQuestionProperties,
  JSONQuestionProperties,
  ListQuestionProperties,
  NumberQuestionProperties,
  SelectQuestionProperties,
  TableQuestionProperties,
  TextQuestionProperties,
  booleanQuestionPropertiesSchema,
  commonQuestionPropertiesSchema,
  dateQuestionPropertiesSchema,
  numberQuestionPropertiesSchema,
  selectQuestionPropertiesSchema,
  textQuestionPropertiesSchema
} from "./QuestionProperties.ts";

export enum QuestionTypes {
  TEXT = "text",
  NUMBER = "number",
  DATE = "date",
  SELECT = "select",
  MULTISELECT = "multiSelect",
  BOOLEAN = "boolean",
  FILES = "files",
  TABLE = "table",
  JSON = "json",
  LIST = "list",
  SCHEMA = "schema", // question to provide section/question configuration
  CHART = "chart"
  // GENERATE_DOCUMENT = "generateDocument",
  // REUSE_QUESTION = "reuseQuestion"
}

export enum ExtendedQuestionTypes {
  FORM = "form",
  FOUNDATION = "foundation",
  SERIES_INTERVAL = "seriesInterval",
  WORKSPACE_VARIABLES = "workspaceVariables"
}

export enum ExtendedConfigurationQuestionTypes {
  FORM_CONFIGURATION = "formConfiguration",
  QUESTION_CONFIGURATION = "questionConfiguration",
  FOUNDATION_CONFIGURATION = "foundationConfiguration",
  SERIES_INTERVAL_CONFIGURATION = "seriesIntervalConfiguration"
}

export const multiLevelQuestionTypes: `${QuestionTypes}`[] = [
  QuestionTypes.TABLE,
  QuestionTypes.JSON,
  QuestionTypes.LIST
];

// Note: Use getChildQuestionTypeOptions to get the options for child questions based on the parent question type
export const TableQuestionTypes = [
  QuestionTypes.TEXT,
  QuestionTypes.NUMBER,
  QuestionTypes.DATE,
  QuestionTypes.SELECT,
  QuestionTypes.MULTISELECT,
  QuestionTypes.BOOLEAN,
  QuestionTypes.LIST
  // QuestionTypes.FILES,
  // QuestionTypes.TABLE, // - TODO: support multiple levels
  // QuestionTypes.JSON // - TODO: support multiple levels
];

export const JSONQuestionTypes = [
  QuestionTypes.TEXT,
  QuestionTypes.NUMBER,
  QuestionTypes.DATE,
  // QuestionTypes.SELECT,
  QuestionTypes.BOOLEAN,
  QuestionTypes.JSON,
  QuestionTypes.LIST
];

export const ListQuestionTypes = [
  QuestionTypes.TEXT,
  QuestionTypes.NUMBER,
  QuestionTypes.DATE
  // QuestionTypes.SELECT,
  // QuestionTypes.BOOLEAN
  // QuestionTypes.TABLE, // - TODO: support multiple levels
  // QuestionTypes.JSON // - TODO: support multiple levels
];

export const ChartQuestionTypes = [
  ChartType.LINE,
  ChartType.BAR,
  ChartType.PIE
];

export const ChartSubTypes: Record<ChartType, ChartSubType[]> = {
  [ChartType.LINE]: [
    ChartSubType.BASIC_LINE,
    ChartSubType.SMOOTH_LINE,
    ChartSubType.STACKED_LINE,
    ChartSubType.STACKED_AREA_LINE
  ],
  [ChartType.BAR]: [ChartSubType.BASIC_BAR, ChartSubType.STACKED_BAR],
  [ChartType.PIE]: [ChartSubType.BASIC_PIE, ChartSubType.DONUT_PIE]
};

export const questionTypeIcon: {
  [key: string]: IconType;
} = {
  [QuestionTypes.TEXT]: { name: "text_fields", fillStyle: "outlined" },
  [QuestionTypes.NUMBER]: { name: "tag" },
  [QuestionTypes.DATE]: { name: "calendar_today", fillStyle: "outlined" },
  [QuestionTypes.SELECT]: {
    name: "expand_circle_down",
    fillStyle: "outlined"
  },
  [QuestionTypes.MULTISELECT]: {
    name: "expand_circle_down",
    fillStyle: "outlined"
  },
  [QuestionTypes.BOOLEAN]: { name: "join_left", fillStyle: "outlined" },
  [QuestionTypes.FILES]: { name: "draft", fillStyle: "outlined" },
  [QuestionTypes.TABLE]: { name: "view_module" },
  [QuestionTypes.JSON]: { name: "data_object", fillStyle: "filled" },
  [QuestionTypes.LIST]: { name: "data_array", fillStyle: "filled" },
  [QuestionTypes.SCHEMA]: { name: "settings" },

  // [QuestionTypes.GENERATE_DOCUMENT]: { name: "download" },
  // [QuestionTypes.REUSE_QUESTION]: { name: "cycle" }

  [ExtendedQuestionTypes.FORM]: commonIcons.forms,
  [ExtendedQuestionTypes.FOUNDATION]: commonIcons.foundations,
  [ExtendedQuestionTypes.SERIES_INTERVAL]: commonIcons.series,
  [ExtendedQuestionTypes.WORKSPACE_VARIABLES]: commonIcons.variables,

  [ChartType.LINE]: { name: "show_chart", fillStyle: "outlined" },
  [ChartType.BAR]: { name: "bar_chart", fillStyle: "outlined" },
  [ChartType.PIE]: { name: "pie_chart", fillStyle: "outlined" },

  unknown: {
    name: "question_mark",
    fillStyle: "filled"
  },

  foundationConfiguration: {
    name: "settings"
  },
  formConfiguration: {
    name: "settings"
  },
  seriesConfiguration: {
    name: "settings"
  },
  questionConfiguration: {
    name: "settings"
  }
};

export type QuestionProperties =
  | Partial<TextQuestionProperties>
  | Partial<NumberQuestionProperties>
  | Partial<SelectQuestionProperties>
  | Partial<DateQuestionProperties>
  | Partial<BooleanQuestionProperties>
  | Partial<TableQuestionProperties>
  | Partial<FilesQuestionProperties>
  | Partial<JSONQuestionProperties>
  | Partial<ListQuestionProperties>
  | Partial<ConfigurationQuestionProperties>
  | Partial<ChartQuestionProperties>;

// Where T is QuestionType and P is the properties for that question type
///     e.g. TextQuestionProperties
export interface Question<P = QuestionProperties> {
  id: string;
  type: `${QuestionTypes}`;
  identifier: string; // default to question name with no spaces, each word start upper cased ; // WhatIsYourName
  text: string; // What is your name?
  description?: string;
  tooltip?: string;
  properties?: CommonQuestionProperties & P;
}

export type MultiLevelQuestion = Question & {
  properties: {
    // table columns or json items or list items
    [key: string]: Question[];
  };
};

export const questionTypesSchema = z.nativeEnum(QuestionTypes);

export const questionTypePropertiesSchemas: {
  [questionType: string]:
    | typeof textQuestionPropertiesSchema
    | typeof numberQuestionPropertiesSchema
    | typeof selectQuestionPropertiesSchema
    | typeof dateQuestionPropertiesSchema
    | typeof booleanQuestionPropertiesSchema;
} = {
  [QuestionTypes.TEXT]: textQuestionPropertiesSchema
  // [QuestionTypes.NUMBER]: numberQuestionPropertiesSchema,
  // [QuestionTypes.SELECT]: selectQuestionPropertiesSchema,
  // [QuestionTypes.DATE]: dateQuestionPropertiesSchema,
  // [QuestionTypes.BOOLEAN]: booleanQuestionPropertiesSchema
};

export const getQuestionSchema = (questionType?: string) => {
  const questionTypeProperties = questionType
    ? questionTypePropertiesSchemas[questionType]
    : undefined;

  return z.object({
    id: z.string(),
    type: questionTypesSchema,
    identifier: z.string(),
    text: z.string(),
    description: z.string().optional(),
    required: z.boolean().optional(),
    properties: questionTypeProperties
      ? z.union([commonQuestionPropertiesSchema, questionTypeProperties])
      : z.union([
          commonQuestionPropertiesSchema,
          textQuestionPropertiesSchema,
          numberQuestionPropertiesSchema,
          selectQuestionPropertiesSchema,
          dateQuestionPropertiesSchema,
          booleanQuestionPropertiesSchema
        ])
  });
};
