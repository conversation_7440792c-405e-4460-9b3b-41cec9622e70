import { useCallback } from "react";
import { useOutletContext } from "react-router-dom";

import { Doc } from "@automerge/automerge-repo";

import { getQuestionTypeDetail } from "@components/flows/VariableField/variableFieldHelpers";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import {
  ExtendedConfigurationQuestionTypes,
  ExtendedQuestionTypes,
  QuestionTypes
} from "@src/types/Question";
import { WorkspaceDocument } from "@src/types/documentTypes";

export type DisplayQuestionType = (variableType?: string) => {
  label: string;
  description?: string;
};

export const useQuestionTypeDisplay = () => {
  const d = useDictionary();
  const { document } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();

  const displayQuestionType: DisplayQuestionType = useCallback(
    (variableType?: string) => {
      const { type, configurationId } = getQuestionTypeDetail(variableType);
      if (!variableType || variableType === "unknown") {
        return {
          label: d("ui.configuration.flows.variables.type.unknown.label")
        };
      } else if (
        (Object.values(ExtendedQuestionTypes) as string[]).includes(type)
      ) {
        return displayExtendedQuestionType({
          type,
          configurationId,
          document,
          d
        });
      } else if (
        (
          Object.values(ExtendedConfigurationQuestionTypes) as string[]
        ).includes(type)
      ) {
        return displayConfigurationQuestionType({ type, d });
      }

      const dictionaryKey = Object.values(QuestionTypes).includes(
        variableType as QuestionTypes
      )
        ? `ui.configuration.forms.question.type.${variableType}`
        : `ui.configuration.forms.question.type.chart.type.${variableType}`;
      const label = d(`${dictionaryKey}.label`);

      if (label === `${dictionaryKey}.label`) {
        return {
          label: variableType
        };
      }

      return {
        label: label,
        description: d(`${dictionaryKey}.description`)
      };
    },
    [d, document]
  );

  return { displayQuestionType };
};

const displayExtendedQuestionType = ({
  type,
  configurationId,
  document,
  d
}: {
  type: string;
  configurationId?: string;
  document?: Doc<WorkspaceDocument>;
  d: Dictionary;
}) => {
  if (type === ExtendedQuestionTypes.FORM) {
    if (!configurationId) {
      return { label: d("ui.terminology.form") };
    }

    const formConfiguration = configurationId
      ? document?.forms?.[configurationId]
      : undefined;

    if (!formConfiguration) {
      return { label: d("ui.terminology.form") };
    }
    return {
      label: `${formConfiguration.key} ${d("ui.terminology.form")}`,
      description: formConfiguration.name
    };
  } else if (type === ExtendedQuestionTypes.FOUNDATION) {
    if (!configurationId) {
      return { label: d("ui.terminology.foundation") };
    }

    const foundationConfiguration = configurationId
      ? document?.foundations?.entities?.[configurationId]
      : undefined;

    if (!foundationConfiguration) {
      return { label: d("ui.terminology.foundation") };
    }
    return {
      label: `${foundationConfiguration.name} ${d("ui.terminology.foundation")}`,
      description: foundationConfiguration.description ?? undefined
    };
  } else if (type === ExtendedQuestionTypes.SERIES_INTERVAL) {
    if (!configurationId) {
      return { label: d("ui.terminology.seriesInterval") };
    }

    const seriesConfiguration = configurationId
      ? document?.series?.[configurationId]
      : undefined;

    if (!seriesConfiguration) {
      return { label: d("ui.terminology.seriesInterval") };
    }
    return {
      label: `${seriesConfiguration.name} ${d("ui.terminology.seriesInterval")}`,
      description: seriesConfiguration.description ?? undefined
    };
  }

  return {
    label: type
  };
};

const displayConfigurationQuestionType = ({
  type,
  d
}: {
  type: string;
  d: Dictionary;
}) => {
  switch (type) {
    case ExtendedConfigurationQuestionTypes.FORM_CONFIGURATION:
      return {
        label: `${d("ui.terminology.form")} ${d("ui.terminology.configuration")}`
      };
    case ExtendedConfigurationQuestionTypes.QUESTION_CONFIGURATION:
      return {
        label: `${d("ui.terminology.question")} ${d("ui.terminology.configuration")}`
      };
    case ExtendedConfigurationQuestionTypes.FOUNDATION_CONFIGURATION:
      return {
        label: `${d("ui.terminology.foundation")} ${d("ui.terminology.configuration")}`
      };
    case ExtendedConfigurationQuestionTypes.SERIES_INTERVAL_CONFIGURATION:
      return {
        label: `${d("ui.terminology.seriesInterval")} ${d("ui.terminology.configuration")}`
      };
    default:
      return {
        label: type
      };
  }
};
