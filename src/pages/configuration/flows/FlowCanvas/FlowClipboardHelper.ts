import { customNanoId } from "@helpers/customNanoIdHelper";

import {
  FlowStep,
  FlowStepId,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  ConditionStepProperties,
  IteratorStepFlowConfiguration,
  IteratorStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

export const getClipboardStep = async (): Promise<FlowStep | undefined> => {
  try {
    const clipboardText = await navigator.clipboard.readText();
    const clipboardStep = JSON.parse(clipboardText) as FlowStep;

    return clipboardStep;
  } catch (error) {
    console.error("Failed to read clipboard contents:", error);
    return undefined;
  }
};

export const getUpdatedIteratorProperties = (
  iteratorStep: FlowStep<IteratorStepProperties>
): IteratorStepProperties => {
  const updatedConfiguration = updateIteratorConfigurationStepIds(iteratorStep);
  return {
    ...iteratorStep.properties,
    configuration: updatedConfiguration
  };
};

const updateIteratorConfigurationStepIds = (
  iteratorStep: FlowStep<IteratorStepProperties>
): IteratorStepFlowConfiguration => {
  const currentConfiguration = JSON.parse(
    JSON.stringify(iteratorStep.properties.configuration)
  ) as IteratorStepFlowConfiguration;
  const updatedConfiguration: IteratorStepFlowConfiguration = { steps: {} };
  const newStepIdMap: {
    [currentId: FlowStepId]: string;
  } = {};

  if (
    !currentConfiguration.start ||
    Object.keys(currentConfiguration.steps).length === 0
  ) {
    return currentConfiguration;
  }

  newStepIdMap[currentConfiguration.start] = customNanoId();
  updatedConfiguration.start = newStepIdMap[currentConfiguration.start];

  for (const [currentId, step] of Object.entries(currentConfiguration.steps)) {
    const updatedStepId = newStepIdMap[currentId] ?? customNanoId();
    newStepIdMap[currentId] = updatedStepId;
    updatedConfiguration.steps[updatedStepId] = step;
    const updatedStep = updatedConfiguration.steps[updatedStepId];
    updatedStep.id = updatedStepId;

    if (!updatedStep.next || updatedStep.next === "") {
      continue;
    }

    if (newStepIdMap[updatedStep.next]) {
      updatedStep.next = newStepIdMap[updatedStep.next];
    } else {
      newStepIdMap[updatedStep.next] = customNanoId();
      updatedStep.next = newStepIdMap[updatedStep.next];
    }
  }

  for (const step of Object.values(updatedConfiguration.steps)) {
    if (step?.variant !== FlowStepVariant.CONDITION) {
      continue;
    }

    const conditionStep = step as FlowStep<ConditionStepProperties>;
    for (const branch of conditionStep.properties["branches"]) {
      if (!branch.next || branch.next === "") {
        continue;
      }
      branch.next = newStepIdMap[branch.next];
    }
  }

  return updatedConfiguration;
};

export const validateClipboard = (
  clipboardStep: FlowStep | Record<string, unknown> | undefined | null
): boolean => {
  if (!clipboardStep) {
    return false;
  }

  if (!clipboardStep.variant) {
    return false;
  }
  if (clipboardStep.variant === FlowStepVariant.TRIGGER) {
    return false;
  }

  if (
    clipboardStep.variant === FlowStepVariant.CONDITION &&
    !(clipboardStep as FlowStep<ConditionStepProperties>).properties.branches
  ) {
    return false;
  }

  if (
    clipboardStep.variant === FlowStepVariant.ITERATOR &&
    !(clipboardStep as FlowStep<IteratorStepProperties>).properties
      .configuration
  ) {
    return false;
  }

  return true;
};
