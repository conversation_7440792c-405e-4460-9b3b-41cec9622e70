import React from "react";

import type { Meta, StoryObj } from "@storybook/react-vite";

import { MockVariable } from "@src/types/FlowConfiguration/FlowConfiguration";

import { ConfigurationFlowVariable } from "./ConfigurationFlowVariable";

const meta: Meta<typeof ConfigurationFlowVariable> = {
  component: ConfigurationFlowVariable,
  title: "configuration/ConfigurationFlowVariable"
};

export default meta;

type Story = StoryObj<typeof ConfigurationFlowVariable>;

const variableSample: MockVariable = {
  identifier: "totalAmount",
  type: "number"
};

const defaultArgs: Story["args"] = {
  variable: variableSample
};

const defaultRender = (args: Story["args"]) => {
  return (
    <ConfigurationFlowVariable
      {...args}
      variable={args?.variable ?? variableSample}
    />
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRender
};
