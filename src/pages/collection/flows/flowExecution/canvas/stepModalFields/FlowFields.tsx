import React, { useMemo } from "react";

import { ExecutionStep } from "@src/types/FlowConfiguration/FlowExecution";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { useFlowExecutionContext } from "../../FlowExecutionContext";
import { FlowPath } from "../components/FlowPath";

export const FlowFields = ({
  executionStep,
  workspaceDocument
}: {
  executionStep: ExecutionStep;
  workspaceDocument: WorkspaceDocument;
}) => {
  const { pathToCurrentExecution } = useFlowExecutionContext();

  const executionFlow =
    Object.values(executionStep.subFlows ?? {})[0] ?? undefined;

  const configurationFlow = useMemo(() => {
    return workspaceDocument.flows?.entities?.[
      executionFlow.context.global.flowConfigurationId
    ];
  }, [
    workspaceDocument.flows?.entities,
    executionFlow?.context.global.flowConfigurationId
  ]);

  return (
    <FlowPath
      flowExecution={executionFlow}
      configurationFlow={configurationFlow}
      pathToCurrentExecution={[
        ...(pathToCurrentExecution ?? []),
        "steps",
        executionStep.id,
        "subFlows",
        executionStep.id
      ]}
    />
  );
};
