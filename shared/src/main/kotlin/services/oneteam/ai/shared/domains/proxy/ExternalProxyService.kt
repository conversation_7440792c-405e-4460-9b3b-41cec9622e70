package services.oneteam.ai.shared.domains.proxy

import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.helpers.getContent
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

class ExternalProxyService(
    private val serviceAccountToken: String, private val _client: HttpClient = HttpClient {
        install(ContentNegotiation) { json(); }
        install(HttpTimeout)
    }
) : ProxyService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun call(
        body: ProxyService.ProxyEndpointBody, isExternalResponse: Boolean?, timeoutMillis: Long?
    ): ProxyService.ProxyEndpointResponse {
        try {
            logger.debug("Proxy call to {} has started", body.url)
            val method = getClientMethod(body)
            val block = createBlock(body, timeoutMillis)

            val res = _client.method(body.url, block)

            val isSuccess = res.status.value.toString().substring(0, 1) == "2"
            if (isSuccess) {
                logger.debug("Proxy call to {} was successful", body.url)
            } else {
                logger.error("Proxy call to {} failed with status {}", body.url, res.status.value)
            }

            val bodyAsText = res.bodyAsText().ifEmpty {
                "{}"
            }

            return if (isExternalResponse == true) {
                val externalEndpointResponse = ProxyService.ExternalEndpointResponse(
                    status = res.status.value,
                    payload = Json.parseToJsonElement(bodyAsText),
                    errors = if (!isSuccess) JsonPrimitive(res.bodyAsText()) else JsonNull
                )
                ProxyService.ProxyEndpointResponse(
                    response = Json.encodeToString(externalEndpointResponse),
                    status = if (isSuccess) ProxyService.ProxyEndpointResponseStatus.SUCCESS else ProxyService.ProxyEndpointResponseStatus.FAIL,
                    error = if (!isSuccess) bodyAsText else null
                )
            } else {
                ProxyService.ProxyEndpointResponse(
                    response = bodyAsText,
                    status = if (isSuccess) ProxyService.ProxyEndpointResponseStatus.SUCCESS else ProxyService.ProxyEndpointResponseStatus.FAIL,
                    error = if (!isSuccess) bodyAsText else null
                )
            }
        } catch (e: Exception) {
            logger.error("Proxy call to ${body.url} failed with error: ${e.message}", e)
            return ProxyService.ProxyEndpointResponse(
                response = null, status = ProxyService.ProxyEndpointResponseStatus.FAIL, error = e.message
            )
        }
    }

    private suspend fun createBlock(
        proxyParams: ProxyService.ProxyEndpointBody, timeoutMillis: Long?
    ): HttpRequestBuilder.() -> Unit {
        val tenant = coroutineContext[RequestContext]!!.tenant
        return {
            val resolvedTimeout = if (timeoutMillis != null && timeoutMillis > 0) {
                timeoutMillis
            } else {
                // TODO: make default an environment variable
                60_000L
            }
            timeout {
                requestTimeoutMillis = resolvedTimeout
            }
            header(HttpHeaders.Referrer, tenant.originUrl)

            if (proxyParams.authentication?.get("useOtaiServiceAccount") == "true") {
                header("Authorization", "Bearer $serviceAccountToken")
            }

            proxyParams.headers?.forEach { (key, value) -> header(key, value) }
            proxyParams.authentication?.forEach { (key, value) -> header(key, value) }
            proxyParams.options?.forEach { (key, value) -> header(key, value) }
            proxyParams.queryParams?.forEach { (key, value) -> parameter(key, value) }

            if (proxyParams.body != null && proxyParams.contentType == ContentType.Application.Json) {
                //need to re-work this area. //FIXME
                //maybe theres an inbuilt way to do this with ktor, somehow deal with the polymorphism of the body,
                //since it seems that each content type has a special subclass for its body
                val contentTypeHeader = proxyParams.headers?.get("Content-Type")
                if (contentTypeHeader != null && contentTypeHeader.equals(
                        "application/x-www-form-urlencoded",
                        ignoreCase = true
                    )
                ) {
                    this.contentType(ContentType.Application.FormUrlEncoded)
                    val body = proxyParams.body
                    require(body is JsonObject)
                    val params = Parameters.build {
                        body.forEach { (k, v) ->
                            append(k, v.getContent())
                        }
                    }
                    this.setBody(FormDataContent(params))
                } else {
                    this.contentType(ContentType.Application.Json)
                    this.setBody(proxyParams.body)
                }
            }
        }
    }

    private fun getClientMethod(
        body: ProxyService.ProxyEndpointBody
    ): suspend HttpClient.(String, HttpRequestBuilder.() -> Unit) -> HttpResponse {
        return when {
            body.contentType == ContentType.MultiPart.FormData -> { urlString, builder ->
                submitFormWithBinaryData(
                    block = builder, url = urlString, formData = formData {
                        body.fileInfos!!.forEach { fileInfo ->
                            append("files", fileInfo.fileBytes, Headers.build {
                                append(
                                    HttpHeaders.ContentDisposition,
                                    "form-data; name=${fileInfo.fileName}; filename=\"${fileInfo.fileName}\""
                                )
                                append(HttpHeaders.ContentType, fileInfo.contentType)
                            })
                        }
                    })
            }

            body.method == HttpMethod.Get.value -> { urlString, builder -> get(urlString, builder) }
            body.method == HttpMethod.Post.value -> { urlString, builder -> post(urlString, builder) }
            body.method == HttpMethod.Put.value -> { urlString, builder -> put(urlString, builder) }
            body.method == HttpMethod.Delete.value -> { urlString, builder -> delete(urlString, builder) }
            body.method == HttpMethod.Patch.value -> { urlString, builder -> patch(urlString, builder) }
            body.method == HttpMethod.Head.value -> { urlString, builder -> head(urlString, builder) }
            body.method == HttpMethod.Options.value -> { urlString, builder -> options(urlString, builder) }
            else -> throw Exception("Not supported yet")
        }
    }

}
