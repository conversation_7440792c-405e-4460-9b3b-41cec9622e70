import React, { useEffect, useMemo, useRef } from "react";
import { useOutletContext, useParams } from "react-router-dom";

import {
  Breadcrumbs,
  BreadcrumbsItem,
  BreadcrumbsItemType,
  Button,
  FontWeight,
  Heading,
  HeadingSize,
  IconButton,
  Inline,
  Pill,
  Stack
} from "@oneteam/onetheme";
import { useQuery } from "@tanstack/react-query";
import { MiniMap, Panel, ReactFlowProvider } from "@xyflow/react";
import { keyBy } from "lodash";

import { getData } from "@helpers/getData";

import { ConfigurationFlowMode } from "@pages/configuration/ConfigurationFlowMode";
import { CanvasType } from "@pages/configuration/flows/FlowCanvas/CanvasType";
import { ExecutionStepNodeFactory } from "@pages/configuration/flows/FlowCanvas/FlowNodeFactory";
import { FlowPageZoomControls } from "@pages/configuration/flows/FlowCanvas/FlowPageZoomControls";

import { useDictionary } from "@src/hooks/useDictionary";
import {
  FlowExecutionDocument,
  FlowResult
} from "@src/types/FlowConfiguration/FlowExecution";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace";

import { useConfigurationFlowContext } from "../../../../configuration/flows/ConfigurationFlowContext";
import { FlowCanvas } from "../../../../configuration/flows/FlowCanvas/FlowCanvas";
import { useFlowCanvas } from "../../../../configuration/flows/FlowCanvas/FlowCanvasContext";
import { useFlowVariables } from "../../../../configuration/flows/hooks/useFlowVariables";
import { useGenerateMockFlowContext } from "../../../../configuration/flows/hooks/useGenerateMockFlowContext";
import { resultColumnGetVariant } from "../../FlowExecutionHelper";
import { useFlowExecutionContext } from "../FlowExecutionContext";
import { getNestedFlowExecution } from "../helpers/ navigationHelper";
import { useFlowExecutionNavigationHelper } from "../hooks/useFlowExecutionNavigationHelper";
import { useFlowExecutionBreadcrumbs } from "../useFlowExecutionBreadcrumbs";
import { FlowExecutionCanvasStepModal } from "./FlowExecutionCanvasStepModal";
import { FlowExecutionToolbar } from "./FlowExecutionToolbar";

interface FlowExecutionCanvasProps {
  breadcrumbs: BreadcrumbsItemType[];
  executionFlow: FlowExecutionDocument;
  workspaceConfiguration: WorkspaceDocument;
}

export const FlowExecutionCanvas = ({
  executionFlow,
  workspaceConfiguration,
  breadcrumbs
}: FlowExecutionCanvasProps) => {
  const {
    setConfigurationFlow,
    setMockFlowContext,
    setFlowStepTypeConfigByPrimaryIdentifier,
    setConfigurationFlowMode
  } = useConfigurationFlowContext();

  const {
    setExecutionFlow,
    pathToCurrentExecution,
    setPathToCurrentExecution,
    summaryModalOpen,
    setSummaryModalOpen,
    setRootExecutionFlow,
    settings,
    setSettings
  } = useFlowExecutionContext();

  const { navigateToPreviousExecution } = useFlowExecutionNavigationHelper();

  const d = useDictionary();
  const { setCanvasType } = useFlowCanvas();
  useEffect(() => {
    setCanvasType(CanvasType.EXECUTION);
  }, [setCanvasType]);

  const dynamicBreadcrumbs = useFlowExecutionBreadcrumbs({
    rootFlowExecution: executionFlow,
    workspaceConfiguration
  });

  useEffect(() => {
    setPathToCurrentExecution?.([]);
  }, [setPathToCurrentExecution, executionFlow]);

  useEffect(() => {
    setConfigurationFlowMode(ConfigurationFlowMode.VIEW);
  }, [setConfigurationFlowMode]);

  useEffect(() => {
    if (!setRootExecutionFlow) {
      return;
    }
    setRootExecutionFlow(executionFlow);
  }, [executionFlow, setRootExecutionFlow]);

  const ref = useRef<HTMLDivElement>(null);

  const flowExecutionToUse = useMemo(() => {
    if (!pathToCurrentExecution) {
      return executionFlow;
    }
    const flowExecutionToUse = getNestedFlowExecution(
      executionFlow,
      pathToCurrentExecution
    );
    return flowExecutionToUse;
  }, [executionFlow, pathToCurrentExecution]);

  const configurationFlow = useMemo(() => {
    return workspaceConfiguration?.flows?.entities?.[
      flowExecutionToUse?.context?.global?.flowConfigurationId ?? ""
    ];
  }, [flowExecutionToUse, workspaceConfiguration]);

  useEffect(() => {
    setConfigurationFlow(configurationFlow);
  }, [setConfigurationFlow, configurationFlow]);

  const { workspace } = useOutletContext<{
    workspace: Workspace;
  }>();

  const { data: flowStepTypeConfigByPrimaryIdentifier } = useQuery({
    queryKey: ["/flowStepTypeConfiguration"],
    queryFn: () =>
      getData("/flowStepTypeConfiguration").then(res => {
        const byPrimaryIdentifier = keyBy(res, "primaryIdentifier");
        setFlowStepTypeConfigByPrimaryIdentifier(byPrimaryIdentifier);
        return byPrimaryIdentifier;
      })
  });

  useEffect(() => {
    if (!setExecutionFlow) {
      return;
    }
    setExecutionFlow(flowExecutionToUse);
  }, [flowExecutionToUse, setExecutionFlow]);

  const generatedMockFlowContext = useGenerateMockFlowContext({
    configurationFlow: configurationFlow,
    flowStepTypeConfigByPrimaryIdentifier,
    workspace
  });

  useEffect(() => {
    setMockFlowContext(generatedMockFlowContext);
  }, [
    generatedMockFlowContext,
    setMockFlowContext,
    flowStepTypeConfigByPrimaryIdentifier,
    workspace,
    configurationFlow
  ]);
  useFlowVariables(generatedMockFlowContext);
  const { flowExecutionId } = useParams();

  const executionResult = useMemo(() => {
    const result = executionFlow.state.result;
    if (!result) {
      return <></>;
    }

    return (
      <Pill
        icon={result === FlowResult.PENDING ? "custom" : "standard"}
        customIcon={{ name: "pending", fillStyle: "filled" }}
        variant={resultColumnGetVariant(result)}
        key={result}
        label={d(`ui.flow.result.${result}`)}
      />
    );
  }, [executionFlow.state.result, d]);

  const exitExecutionButton = useMemo(() => {
    if (!pathToCurrentExecution || pathToCurrentExecution.length === 0) {
      return <></>;
    }

    return (
      <IconButton
        onClick={navigateToPreviousExecution}
        size="l"
        name="arrow_insert"
      />
    );
  }, [pathToCurrentExecution, navigateToPreviousExecution]);

  const handleUpdateSetting = (key: string, value: boolean) => {
    setSettings({
      ...settings,
      [key]: value ?? false
    } as { displayJson: boolean; showMiniMap: boolean });
  };

  return (
    <Stack height="100" width="100" ref={ref}>
      <ReactFlowProvider>
        {/* TODO: Add floating layer style */}
        <FlowCanvas
          flowFactory={new ExecutionStepNodeFactory()}
          canvasType={CanvasType.EXECUTION}
          interactive
          centreOnLoad
          flow={configurationFlow}
          spacing={{ x: 150, y: 100 }}
        >
          {/* Top left */}
          <Panel position="top-left">
            <Stack gap="025" className="whiteboard-page-body-template__heading">
              {breadcrumbs?.length > 0 && (
                <Breadcrumbs>
                  {/* {breadcrumbs?.map(breadcrumb => (
                    <BreadcrumbsItem key={breadcrumb.href} {...breadcrumb} />
                  ))} */}
                  {dynamicBreadcrumbs.map(breadcrumb => (
                    <BreadcrumbsItem key={breadcrumb.href} {...breadcrumb} />
                  ))}
                </Breadcrumbs>
              )}
              <Inline gap="050">
                {exitExecutionButton}
                <Heading weight={FontWeight.REGULAR} size={HeadingSize.L}>
                  {configurationFlow.name}
                </Heading>
              </Inline>
              {executionResult}
            </Stack>
          </Panel>
          {/* Bottom Right */}
          <Panel position="bottom-right" style={{ marginBottom: "80px" }}>
            {summaryModalOpen && (
              <FlowExecutionCanvasStepModal
                flowContext={generatedMockFlowContext}
                workspaceDocument={workspaceConfiguration}
              />
            )}
          </Panel>
          <Panel position="bottom-right">
            <FlowPageZoomControls />
          </Panel>
          {!summaryModalOpen && settings?.showMiniMap && (
            <MiniMap
              className="flow-canvas__mini-map"
              nodeStrokeWidth={3}
              pannable
              zoomable
            />
          )}
          <Panel position="top-center">
            <FlowExecutionToolbar
              flowExecutionId={flowExecutionId ?? ""}
              handleUpdateSetting={handleUpdateSetting}
            />
          </Panel>
          <Panel position="top-right">
            {!summaryModalOpen && (
              <Button
                label={d("ui.collection.flowExecution.summaryButton")}
                variant="secondary"
                leftIcon={{
                  name: "family_history"
                }}
                onClick={() => {
                  setSummaryModalOpen?.(true);
                }}
              />
            )}
          </Panel>
        </FlowCanvas>
      </ReactFlowProvider>
    </Stack>
  );
};
