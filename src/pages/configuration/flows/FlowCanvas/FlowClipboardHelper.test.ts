import { describe, expect, test } from "vitest";

import { FlowStep } from "../../../../types/FlowConfiguration/FlowStep/FlowStep";
import {
  ConditionStepProperties,
  IteratorStepProperties
} from "../../../../types/FlowConfiguration/FlowStep/FlowStepProperties";
import {
  getUpdatedIteratorProperties,
  validateClipboard
} from "./FlowClipboardHelper";

describe("Validate clipboard", () => {
  test("should handle undefined step", () => {
    const clipboardStep = undefined;
    const isValid = validateClipboard(clipboardStep);
    expect(isValid).toBe(false);
  });

  test("should handle null step", () => {
    const clipboardStep = null;
    const isValid = validateClipboard(clipboardStep);
    expect(isValid).toBe(false);
  });

  test("should handle empty object", () => {
    const clipboardStep = {};
    const isValid = validateClipboard(clipboardStep);
    expect(isValid).toBe(false);
  });

  test("should handle trigger step", () => {
    const triggerStep = {
      id: "FVLbkThxxoBKeqw_Cp5XP",
      metadata: {
        createdAt: "2025-03-19T02:54:15.888Z",
        updatedAt: "2025-03-19T02:54:15.888Z"
      },
      name: "Manually trigger from a form",
      next: "",
      properties: {
        inputs: {
          buttonLabel: "Tax Income",
          formConfigurationId: "9X3QIKA4sFDtgc5-_g1rt",
          formVariableName: "form"
        },
        typePrimaryIdentifier: "manualTriggerFromForm"
      },
      variant: "trigger"
    };

    const isValid = validateClipboard(triggerStep);
    expect(isValid).toBe(false);
  });

  test("should handle valid condition step", () => {
    const conditionStep: FlowStep<ConditionStepProperties> = {
      id: "uJrXSNKXdkFE6lWSJ6ASf",
      variant: "condition",
      name: "Condition",
      properties: {
        branches: [
          {
            name: "If",
            condition: { lhs: "", operator: "=", rhs: "" },
            next: null
          }
        ]
      },
      next: "",
      metadata: {
        createdAt: "2025-04-04T06:09:07.886Z",
        updatedAt: "2025-04-04T06:09:07.886Z"
      }
    };

    const isValidConditionStep = validateClipboard(conditionStep);
    expect(isValidConditionStep).toBe(true);
  });

  test("should handle invalid condition step", () => {
    const conditionStep = {
      id: "4fh7lqhENYM4XZqkUqFQY",
      variant: "condition",
      name: "Condition",
      properties: {},
      next: "",
      metadata: {
        createdAt: "2025-04-03T00:21:53.050Z",
        updatedAt: "2025-04-03T00:21:53.050Z"
      }
    };

    const isValidConditionStep = validateClipboard(conditionStep);
    expect(isValidConditionStep).toBe(false);
  });

  test("should handle valid iterator step", () => {
    const iteratorStep: FlowStep<IteratorStepProperties> = {
      id: "q-LW8zuMfpDoXUcmNJ98S",
      metadata: {
        createdAt: "2025-03-26T03:23:23.053Z",
        updatedAt: "2025-03-26T03:23:23.053Z"
      },
      name: "For each",
      next: "",
      properties: {
        configuration: {
          start: "",
          startingVariables: [
            { identifier: "income_from_stream", type: "json" },
            { identifier: "income_from_stream_index", type: "number" },
            { identifier: "income_from_stream_output", type: "unknown" }
          ],
          steps: {}
        },
        inputs: {
          isReturn: "false",
          itemVariableName: "income_from_stream",
          list: "{{form.QDxGOqShnn.columns.zgse3n9x_a.answer}}",
          resultVariableName: "output__step_q-LW8zuMfpDoXUcmNJ98S",
          transformedValueType: "json"
        },
        typePrimaryIdentifier: "iteratorForEach"
      },
      variant: "iterator"
    };

    const isValidIteratorStep = validateClipboard(iteratorStep);
    expect(isValidIteratorStep).toBe(true);
  });

  test("should handle invalid iterator step", () => {
    const iteratorStep: FlowStep<Partial<IteratorStepProperties>> = {
      id: "YrOMxq93yeGu_I5Nyfxe2",
      variant: "iterator",
      name: "Aggregate",
      properties: {
        typePrimaryIdentifier: "iteratorAggregate",
        inputs: {
          itemVariableName: "item_YrOMxq93yeGu_I5Nyfxe2",
          resultVariableName: "aggregate__step_YrOMxq93yeGu_I5Nyfxe2",
          resultVariableType: "number"
        }
      },
      next: "Zolijizcske8-dv_BB4qS",
      metadata: {
        createdAt: "2025-04-03T00:42:42.162Z",
        updatedAt: "2025-04-03T00:42:42.162Z"
      }
    };

    const isValidConditionStep = validateClipboard(iteratorStep);
    expect(isValidConditionStep).toBe(false);
  });
});

const iteratorFlowStep: FlowStep<IteratorStepProperties> = {
  id: "a22em8G1eSGhXiMKNX5VI",
  variant: "iterator",
  name: "For each",
  properties: {
    typePrimaryIdentifier: "iteratorForEach",
    inputs: {
      itemVariableName: "item_a22em8G1eSGhXiMKNX5VI",
      isReturn: "false",
      transformedValueType: "json",
      resultVariableName: "output__step_a22em8G1eSGhXiMKNX5VI"
    },
    configuration: {
      start: "iteratorStartId",
      steps: {
        iteratorStartId: {
          id: "iteratorStartId",
          variant: "setVariables",
          name: "Set variable(s) - Iterator Start",
          properties: {
            variables: [{ identifier: "variable_2", value: "", type: "text" }]
          },
          next: "secondId",
          metadata: {
            createdAt: "2025-05-30T04:18:36.384Z",
            updatedAt: "2025-05-30T04:18:36.384Z"
          }
        },
        secondId: {
          id: "secondId",
          variant: "setVariables",
          name: "Iterator - Second",
          properties: {
            variables: [{ identifier: "variable_3", value: "", type: "text" }]
          },
          next: "conditionId",
          metadata: {
            createdAt: "2025-05-30T04:18:39.592Z",
            updatedAt: "2025-05-30T04:18:39.592Z"
          }
        },
        conditionId: {
          id: "conditionId",
          variant: "condition",
          name: "Condition",
          properties: {
            branches: [
              {
                name: "If",
                condition: { lhs: "", operator: "=", rhs: "" },
                next: "conditionIfBranchId"
              }
            ]
          },
          next: "conditionElseBranchId",
          metadata: {
            createdAt: "2025-05-30T04:18:42.267Z",
            updatedAt: "2025-05-30T04:18:42.267Z"
          }
        },
        conditionIfBranchId: {
          id: "conditionIfBranchId",
          variant: "setVariables",
          name: "Condition If Branch",
          properties: {
            variables: [{ identifier: "variable_4", value: "", type: "text" }]
          },
          next: "",
          metadata: {
            createdAt: "2025-05-30T04:19:29.726Z",
            updatedAt: "2025-05-30T04:19:29.726Z"
          }
        },
        conditionElseBranchId: {
          id: "conditionElseBranchId",
          variant: "setVariables",
          name: "Condition Else Branch",
          properties: {
            variables: [{ identifier: "variable_5", value: "", type: "text" }]
          },
          next: "",
          metadata: {
            createdAt: "2025-05-30T04:19:42.316Z",
            updatedAt: "2025-05-30T04:19:42.316Z"
          }
        }
      }
    }
  },
  next: "",
  metadata: {
    createdAt: "2025-05-30T04:18:32.919Z",
    updatedAt: "2025-05-30T04:18:32.919Z"
  }
};

describe("getUpdatedIteratorProperties", () => {
  const updatedProperties = getUpdatedIteratorProperties(iteratorFlowStep);

  const getBranchNextIds = (
    conditionStep: FlowStep<ConditionStepProperties>
  ) => {
    return (
      conditionStep.properties.branches
        .filter(branch => branch.next)
        .map(branch => branch.next) ?? []
    );
  };

  test("should maintain typePrimaryIdentifier", () => {
    expect(updatedProperties.typePrimaryIdentifier).toBe(
      iteratorFlowStep.properties.typePrimaryIdentifier
    );
  });

  test("should maintain inputs", () => {
    expect(updatedProperties.inputs).toEqual(
      iteratorFlowStep.properties.inputs
    );
  });

  test("should have the same number of steps ", () => {
    const currentStepIds = Object.keys(
      iteratorFlowStep.properties.configuration.steps
    );
    const updatedStepIds = Object.keys(updatedProperties.configuration.steps);
    expect(updatedStepIds.length).toBe(currentStepIds.length);
  });

  test("should change all of the step ids", () => {
    const currentStepIds = new Set(
      Object.keys(iteratorFlowStep.properties.configuration.steps)
    );
    const updatedStepIds = new Set(
      Object.keys(updatedProperties.configuration.steps)
    );

    const intersection = new Set(
      [...currentStepIds].filter(id => updatedStepIds.has(id))
    );
    expect(intersection.size).toBe(0);
  });

  test("should change all step ids in branches", () => {
    const currentCondition = iteratorFlowStep.properties.configuration.steps[
      "conditionId"
    ] as FlowStep<ConditionStepProperties>;
    const currentBranchNextIds = getBranchNextIds(currentCondition);
    let updatedBranchNextIds: (string | null)[];

    for (const step of Object.values(updatedProperties.configuration.steps)) {
      if ((step as FlowStep).variant !== "condition") {
        continue;
      }

      const conditionStep = step as FlowStep<ConditionStepProperties>;
      const conditionBranchNextIds = getBranchNextIds(conditionStep);
      updatedBranchNextIds = conditionBranchNextIds || [];
    }

    const intersection = currentBranchNextIds.filter(id =>
      updatedBranchNextIds?.includes(id ?? "")
    );
    expect(intersection.length).toBe(0);
  });

  test("should connect start with the first step", () => {
    expect(updatedProperties.configuration.start).toBeDefined();
    const stepIds = Object.keys(updatedProperties.configuration.steps);
    expect(stepIds).toContain(updatedProperties.configuration.start);
  });
});
