import React from "react";

import { WhiteboardTool, WhiteboardToolbar } from "@oneteam/onetheme";

import { ConfigurationFlowMode } from "@pages/configuration/ConfigurationFlowMode";

import { useConfigurationFlowContext } from "../ConfigurationFlowContext";

export const FlowModeSwitcher = () => {
  const { configurationFlowMode, setConfigurationFlowMode } =
    useConfigurationFlowContext();

  return (
    <WhiteboardToolbar>
      <WhiteboardTool
        icon={{ name: "arrow_selector_tool" }}
        isSelected={configurationFlowMode === ConfigurationFlowMode.VIEW}
        onClick={() => setConfigurationFlowMode(ConfigurationFlowMode.VIEW)}
      />
      <WhiteboardTool
        icon={{ name: "edit" }}
        isSelected={configurationFlowMode === ConfigurationFlowMode.EDIT}
        onClick={() => setConfigurationFlowMode(ConfigurationFlowMode.EDIT)}
      />
    </WhiteboardToolbar>
  );
};
