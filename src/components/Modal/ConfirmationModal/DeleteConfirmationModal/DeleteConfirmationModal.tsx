import React, { PropsWithChildren, useState } from "react";

import { TextField } from "../../../TextField";
import {
  ConfirmationModal,
  ConfirmationModalProps
} from "../ConfirmationModal";
import { ConfirmationModalVariant } from "../ConfirmationModalTypes";

export interface DeleteConfirmationModalProps extends ConfirmationModalProps {
  withTextEntry?: boolean;
  textEntry?: {
    label: string;
    expectedValue: string;
  };
}

export const DeleteConfirmationModal = ({
  heading = "Delete",
  variant = ConfirmationModalVariant.DANGER,
  onConfirm,
  withTextEntry = false,
  textEntry = {
    label: "Type 'delete' to confirm",
    expectedValue: "delete"
  },
  ...rest
}: PropsWithChildren<DeleteConfirmationModalProps>) => {
  const [value, setValue] = useState("");
  return (
    <ConfirmationModal
      heading={heading}
      onConfirm={onConfirm}
      variant={variant}
      confirmIsDisabled={
        withTextEntry &&
        value.toLowerCase() !== textEntry.expectedValue.toLowerCase()
      }
      {...rest}
    >
      {withTextEntry && (
        <TextField
          autoFocus
          width="100"
          label={textEntry.label}
          required
          allowClear={false}
          value={value}
          onChange={setValue}
        />
      )}
    </ConfirmationModal>
  );
};

DeleteConfirmationModal.displayName = "DeleteConfirmationModal";
