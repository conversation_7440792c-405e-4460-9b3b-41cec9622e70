import React from "react";

import {
  <PERSON>,
  <PERSON>ack,
  WhiteboardAddButton,
  WhiteboardLine
} from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";

import {
  FlowStep as FlowStepType,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { IteratorStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

import { FlowStep } from "../FlowStep";
import { IteratorFlowStepWrapper } from "./IteratorFlowStepWrapper";

const meta: Meta<typeof IteratorFlowStepWrapper> = {
  component: IteratorFlowStepWrapper,
  title: "configuration/FlowStep/IteratorFlowStepWrapper"
};

export default meta;

type Story = StoryObj<typeof IteratorFlowStepWrapper>;

const mockFlowStep: FlowStepType<IteratorStepProperties> = {
  name: "Untitled",
  variant: FlowStepVariant.ITERATOR,
  properties: {
    typePrimaryIdentifier: "iteratorForEach",
    inputs: {
      list: "List"
    },
    configuration: {
      start: "1",
      steps: {
        "1": {
          id: "1",
          variant: FlowStepVariant.ACTION,
          name: "Untitled",
          next: "2",
          properties: {},
          metadata: {
            createdAt: "",
            updatedAt: ""
          }
        },
        "2": {
          id: "2",
          variant: FlowStepVariant.CONDITION,
          name: "Untitled",
          next: "3",
          properties: {},
          metadata: {
            createdAt: "",
            updatedAt: ""
          }
        },
        "3": {
          id: "3",
          variant: FlowStepVariant.SET_VARIABLES,
          name: "Untitled",
          next: "4",
          properties: {},
          metadata: {
            createdAt: "",
            updatedAt: ""
          }
        }
      }
    }
  },
  id: "",
  metadata: {
    createdAt: "",
    updatedAt: ""
  }
};

const defaultArgs: Story["args"] = {
  flowStep: mockFlowStep,
  flowStepTypeConfiguration: {
    primaryIdentifier: "iteratorForEach",
    name: "For Each",
    id: "",
    type: FlowStepVariant.TRIGGER,
    properties: {
      icon: {
        name: "",
        fillStyle: "filled"
      },
      isLocked: undefined,
      isHidden: undefined,
      configuration: {
        content: [],
        subscribeTo: {},
        variableMappings: []
      }
    }
  }
};

const defaultRender = (args: Story["args"]) => {
  return (
    <Box padding="200">
      <IteratorFlowStepWrapper
        {...args}
        flowStep={args?.flowStep ?? mockFlowStep}
      >
        <Stack gap="000" alignment="center">
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.ACTION
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.CONDITION
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.SET_VARIABLES
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.FLOW
            }}
          />
          <WhiteboardLine
            height={40}
            element={() => <WhiteboardAddButton />}
            elementPosition="bottom"
          />
        </Stack>
      </IteratorFlowStepWrapper>
    </Box>
  );
};

export const Iterator: Story = {
  args: defaultArgs,
  render: defaultRender
};

export const Highlighted: Story = {
  args: { ...defaultArgs, isHighlighted: true },
  render: defaultRender
};

export const Selected: Story = {
  args: { ...defaultArgs, isSelected: true },
  render: defaultRender
};

export const Disabled: Story = {
  args: { ...defaultArgs, isDisabled: true },
  render: defaultRender
};

const iteratorInIteratorRender = (args: Story["args"]) => {
  return (
    <Box padding="200">
      <IteratorFlowStepWrapper
        {...args}
        flowStep={args?.flowStep ?? mockFlowStep}
      >
        <Stack gap="000" alignment="center">
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.ACTION
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.CONDITION
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.SET_VARIABLES
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.FLOW
            }}
          />
          <WhiteboardLine height={30} />
        </Stack>
        <IteratorFlowStepWrapper
          iteratorDepth={2}
          {...args}
          flowStep={args?.flowStep ?? mockFlowStep}
        >
          <Stack gap="000" alignment="center">
            <FlowStep
              flowStep={{
                ...mockFlowStep,
                id: "0",
                name: "Untitled",
                variant: FlowStepVariant.ACTION
              }}
            />
            <WhiteboardLine height={30} />
            <FlowStep
              flowStep={{
                ...mockFlowStep,
                id: "1",
                name: "Untitled",
                variant: FlowStepVariant.CONDITION
              }}
            />
            <WhiteboardLine height={30} />
            <IteratorFlowStepWrapper
              iteratorDepth={3}
              {...args}
              flowStep={args?.flowStep ?? mockFlowStep}
            >
              <Stack gap="000" alignment="center">
                <FlowStep
                  flowStep={{
                    ...mockFlowStep,
                    id: "2",
                    name: "Untitled",
                    variant: FlowStepVariant.ACTION
                  }}
                />
                <WhiteboardLine height={30} />
                <FlowStep
                  flowStep={{
                    ...mockFlowStep,
                    id: "3",
                    name: "Untitled",
                    variant: FlowStepVariant.CONDITION
                  }}
                />
                <WhiteboardLine
                  height={40}
                  element={() => <WhiteboardAddButton />}
                  elementPosition="bottom"
                />
              </Stack>
            </IteratorFlowStepWrapper>
            <WhiteboardLine height={30} />
            <FlowStep
              flowStep={{
                ...mockFlowStep,
                id: "4",
                name: "Untitled",
                variant: FlowStepVariant.SET_VARIABLES
              }}
            />
            <WhiteboardLine height={30} />
            <FlowStep
              flowStep={{
                ...mockFlowStep,
                id: "5",
                name: "Untitled",
                variant: FlowStepVariant.FLOW
              }}
            />
            <WhiteboardLine
              height={40}
              element={() => <WhiteboardAddButton />}
              elementPosition="bottom"
            />
          </Stack>
        </IteratorFlowStepWrapper>
        <WhiteboardLine
          height={40}
          element={() => <WhiteboardAddButton />}
          elementPosition="bottom"
        />
      </IteratorFlowStepWrapper>
    </Box>
  );
};

export const IteratorInIterator: Story = {
  args: defaultArgs,
  render: iteratorInIteratorRender
};

const iteratorInIteratorSelectedRender = (args: Story["args"]) => {
  return (
    <Box padding="200">
      <IteratorFlowStepWrapper
        {...args}
        flowStep={args?.flowStep ?? mockFlowStep}
      >
        <Stack gap="000" alignment="center">
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "0",
              name: "Untitled",
              variant: FlowStepVariant.ACTION
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "1",
              name: "Untitled",
              variant: FlowStepVariant.CONDITION
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "2",
              name: "Untitled",
              variant: FlowStepVariant.SET_VARIABLES
            }}
          />
          <WhiteboardLine height={30} />
          <FlowStep
            flowStep={{
              ...mockFlowStep,
              id: "3",
              name: "Untitled",
              variant: FlowStepVariant.FLOW
            }}
          />
          <WhiteboardLine height={30} />
        </Stack>
        <IteratorFlowStepWrapper
          isSelected
          iteratorDepth={2}
          {...args}
          flowStep={args?.flowStep ?? mockFlowStep}
        >
          <Stack gap="000" alignment="center">
            <FlowStep
              flowStep={{
                ...mockFlowStep,
                id: "0",
                name: "Untitled",
                variant: FlowStepVariant.ACTION
              }}
            />
            <WhiteboardLine height={30} />
            <FlowStep
              flowStep={{
                ...mockFlowStep,
                id: "1",
                name: "Untitled",
                variant: FlowStepVariant.CONDITION
              }}
            />
            <WhiteboardLine height={30} />
            <IteratorFlowStepWrapper
              iteratorDepth={3}
              {...args}
              flowStep={args?.flowStep ?? mockFlowStep}
            >
              <Stack gap="000" alignment="center">
                <FlowStep
                  flowStep={{
                    ...mockFlowStep,
                    id: "2",
                    name: "Untitled",
                    variant: FlowStepVariant.ACTION
                  }}
                />
                <WhiteboardLine height={30} />
                <FlowStep
                  flowStep={{
                    ...mockFlowStep,
                    id: "3",
                    name: "Untitled",
                    variant: FlowStepVariant.CONDITION
                  }}
                />
                <WhiteboardLine
                  height={40}
                  element={() => <WhiteboardAddButton />}
                  elementPosition="bottom"
                />
              </Stack>
            </IteratorFlowStepWrapper>
            <WhiteboardLine height={30} />
            <FlowStep
              flowStep={{
                ...mockFlowStep,
                id: "4",
                name: "Untitled",
                variant: FlowStepVariant.SET_VARIABLES
              }}
            />
            <WhiteboardLine height={30} />
            <FlowStep
              flowStep={{
                ...mockFlowStep,
                id: "5",
                name: "Untitled",
                variant: FlowStepVariant.FLOW
              }}
            />
            <WhiteboardLine
              height={40}
              element={() => <WhiteboardAddButton />}
              elementPosition="bottom"
            />
          </Stack>
        </IteratorFlowStepWrapper>
        <WhiteboardLine
          height={40}
          element={() => <WhiteboardAddButton />}
          elementPosition="bottom"
        />
      </IteratorFlowStepWrapper>
    </Box>
  );
};

export const IteratorInIteratorSelected: Story = {
  args: defaultArgs,
  render: iteratorInIteratorSelectedRender
};
