import { SyntheticEvent, useCallback } from "react";
import { useNavigate } from "react-router-dom";

export const useNavigateOnClick = () => {
  const navigate = useNavigate();

  const navigateOnClick = useCallback(
    (
      path?: string,
      {
        callbackWithoutNewTab,
        callbackWithNewTab
      }: {
        callbackWithoutNewTab?: () => void;
        callbackWithNewTab?: () => void;
      } = {}
    ) =>
      (e: SyntheticEvent) => {
        if ((e as unknown as KeyboardEvent)?.metaKey) {
          if (path) {
            window.open(`/ai${path}`, "_blank", "rel=noopener noreferrer");
          }
          callbackWithNewTab?.();
          return;
        }

        if (path) {
          navigate(path);
        }
        callbackWithoutNewTab?.();
      },
    [navigate]
  );

  return navigateOnClick;
};
