import { useMemo } from "react";

import { BreadcrumbsItemType } from "@oneteam/onetheme";

import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs";
import { FlowExecutionDocument } from "@src/types/FlowConfiguration/FlowExecution";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { useFlowExecutionContext } from "./FlowExecutionContext";
import { getNestedFlowExecution } from "./helpers/ navigationHelper";

export interface FlowExecutionBreadcrumbsProps {
  rootFlowExecution: FlowExecutionDocument;
  workspaceConfiguration: WorkspaceDocument;
}

export const useFlowExecutionBreadcrumbs = ({
  rootFlowExecution,
  workspaceConfiguration
}: FlowExecutionBreadcrumbsProps) => {
  const {
    pathToCurrentExecution,
    setPathToCurrentExecution,
    setSelectedStepId
  } = useFlowExecutionContext();

  const parentExecutionIds = useMemo(() => {
    if (!pathToCurrentExecution) {
      return [];
    }

    // [steps, id1, subFlows, id1, steps, id2, subFlows, id2] --> [id1, id2]
    return pathToCurrentExecution
      .filter((_, index) => index % 2 === 1)
      .filter((_, index) => index % 2 === 1);
  }, [pathToCurrentExecution]);

  const existingBreadcrumbs = useBreadcrumbs();

  const outerExecutionBreadcrumbs = useMemo<BreadcrumbsItemType[]>(() => {
    if (pathToCurrentExecution?.length === 0) {
      return [];
    }

    const outerBreadcrumbs: BreadcrumbsItemType[] = parentExecutionIds.map(
      (_, index) => {
        const reverseIndex = parentExecutionIds.length - index - 1;
        const removeFromEnd = reverseIndex * 4;
        const splicedPathToCurrentExecution = pathToCurrentExecution?.slice(
          0,
          removeFromEnd === 0 ? undefined : -removeFromEnd
        );

        const currentFlowExecution = getNestedFlowExecution(
          rootFlowExecution,
          splicedPathToCurrentExecution ?? []
        );
        const name = currentFlowExecution?.context.global.flowConfigurationName;
        return {
          text: name,
          href: "",
          onClick: () => {
            setSelectedStepId(undefined);
            setPathToCurrentExecution?.(splicedPathToCurrentExecution);
          },

          isActive: index === parentExecutionIds.length - 1
        };
      }
    );

    return outerBreadcrumbs;
  }, [
    pathToCurrentExecution,
    parentExecutionIds,
    rootFlowExecution,
    setPathToCurrentExecution,
    setSelectedStepId
  ]);

  const currentExecutionBreadcrumb = useMemo<BreadcrumbsItemType>(() => {
    return {
      text: workspaceConfiguration.flows?.entities?.[
        rootFlowExecution.context.global.flowConfigurationId
      ].name,
      href: "",
      isActive: pathToCurrentExecution?.length === 0,
      onClick: () => {
        setSelectedStepId(undefined);
        setPathToCurrentExecution?.([]);
      }
    };
  }, [
    rootFlowExecution,
    workspaceConfiguration,
    pathToCurrentExecution,
    setPathToCurrentExecution,
    setSelectedStepId
  ]);

  const dynamicBreadcrumbs: BreadcrumbsItemType[] = useMemo(() => {
    return [
      ...existingBreadcrumbs.map(breadcrumb => ({
        ...breadcrumb,
        isActive: false
      })),
      currentExecutionBreadcrumb,
      ...outerExecutionBreadcrumbs
    ];
  }, [
    existingBreadcrumbs,
    currentExecutionBreadcrumb,
    outerExecutionBreadcrumbs
  ]);

  return dynamicBreadcrumbs;
};
