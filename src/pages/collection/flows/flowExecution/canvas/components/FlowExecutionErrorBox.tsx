import React from "react";

import { Alert, Icon, Inline, Stack, Text } from "@oneteam/onetheme";

import { useDictionary } from "@src/hooks/useDictionary";
import { FlowExecutionDocument } from "@src/types/FlowConfiguration/FlowExecution";
import {
  FlowStep,
  FlowStepId
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { useFlowExecutionContext } from "../../FlowExecutionContext";
import "./ExecutionFailSummary.scss";
import { FlowPathItem } from "./FlowPath";

export type FlowExecutionErrorBoxProps = {
  message: string;
  failedStepConfiguration?: FlowStep;
  executionFlow?: FlowExecutionDocument;
  iteratorParentId?: FlowStepId;
};

export const FlowExecutionErrorBox = ({
  message,
  failedStepConfiguration,
  executionFlow,
  iteratorParentId
}: FlowExecutionErrorBoxProps) => {
  const d = useDictionary();

  const { pathToCurrentExecution } = useFlowExecutionContext();
  return (
    <Alert
      variant="danger"
      className="execution-error-box"
      icon="standard"
      width="fill"
    >
      <Stack gap="100" width="fill">
        <Text
          size="m"
          weight="medium"
          style={{
            wordBreak: "break-word"
          }}
        >
          {message}
        </Text>
        {failedStepConfiguration && executionFlow && (
          <FlowPathItem
            configurationStep={failedStepConfiguration}
            executionFlow={executionFlow}
            iteratorId={iteratorParentId}
            pathToCurrentExecution={pathToCurrentExecution}
            disableExpanding
            rightElementOverride={
              <Inline gap="050">
                <Text
                  size="s"
                  weight="semi-bold"
                  style={{
                    whiteSpace: "nowrap"
                  }}
                >
                  {d("ui.collection.flowExecution.stepModal.flowPath.goToStep")}
                </Text>
                <Icon name="arrow_right_alt" />
              </Inline>
            }
          />
        )}
      </Stack>
    </Alert>
  );
};
