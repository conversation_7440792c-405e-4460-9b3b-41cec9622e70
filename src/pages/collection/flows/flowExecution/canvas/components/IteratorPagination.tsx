import React, { useEffect } from "react";

import { Box, Pagination } from "@oneteam/onetheme";

import { ExecutionStep } from "@src/types/FlowConfiguration/FlowExecution";

import { useFlowExecutionContext } from "../../FlowExecutionContext";

export const IteratorPagination = ({
  iteratorExecutionStep
}: {
  iteratorExecutionStep: ExecutionStep;
}) => {
  const pages = Array.from(
    { length: Object.keys(iteratorExecutionStep.subFlows ?? {}).length },
    (_, index) => index + 1
  );

  const { setExecutionFlowForModal } = useFlowExecutionContext();

  const { iteratorStepPageMap, updateIteratorStepPageMap } =
    useFlowExecutionContext();

  useEffect(() => {
    if (!setExecutionFlowForModal) {
      return;
    }

    const subFlowsAmount = Object.keys(
      iteratorExecutionStep?.subFlows ?? {}
    ).length;

    const selectedIteration =
      iteratorStepPageMap?.[iteratorExecutionStep.id] ?? subFlowsAmount;

    setExecutionFlowForModal(
      iteratorExecutionStep.subFlows?.[
        `${iteratorExecutionStep.id}_${selectedIteration}`
      ]
    );
  }, [iteratorExecutionStep, iteratorStepPageMap, setExecutionFlowForModal]);

  return (
    <Box onClick={e => e.stopPropagation()}>
      <Pagination
        pages={pages}
        defaultPageIndex={
          iteratorStepPageMap?.[iteratorExecutionStep.id]
            ? iteratorStepPageMap?.[iteratorExecutionStep.id] - 1
            : pages.length - 1
        }
        onChange={pageNumber =>
          updateIteratorStepPageMap?.(iteratorExecutionStep.id, pageNumber)
        }
      />
    </Box>
  );
};
