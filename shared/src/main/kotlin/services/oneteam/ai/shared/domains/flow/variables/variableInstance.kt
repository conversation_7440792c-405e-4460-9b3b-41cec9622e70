package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.*
import kotlinx.serialization.builtins.serializer
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.descriptors.element
import kotlinx.serialization.encoding.CompositeDecoder.Companion.DECODE_DONE
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.encoding.decodeStructure
import kotlinx.serialization.encoding.encodeStructure
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import services.oneteam.ai.shared.domains.DataTypeSerializer
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance.SecuredVariable.Companion.SECURED_PREFIX

@Serializable
sealed class VariableInstance : Variable() {

    abstract fun toProperType(): JsonElement

    /**
     * common function to retrieve the value stored within a variable instance.
     * if the variable is a VariableInstance.SecuredVariable, it will return a masked value.
     * if you want to retrieve the actual value, you need to call getRevealed() instead.
     */
    abstract fun get(): JsonElement

    /**
     * this function is used to retrieve the actual value of the variable, regardless of whether it is secured or not.
     * it should be used with caution, as it may expose sensitive information.
     */
    abstract fun getRevealed(): JsonElement

    @Serializable
    @SerialName(VariableType.VARIABLE.NAME)
    data class Variable(
        private val value: JsonElement,
        @Serializable(with = DataTypeSerializer::class) override val type: VariableDataType,
        override val identifier: VariableIdentifier,
        override val properties: VariableProperties? = null,
    ) : VariableInstance() {

        /**
         * Convert the variable to the proper type. We store numbers as strings, but when we want to use it as a number (e.g. in a calculation), we need to convert it to a number so it won't get quoted.
         */
        override fun toProperType(): JsonElement = TypeToJsonElementConverter.convert(type, value)

        override fun get(): JsonElement = value

        override fun getRevealed(): JsonElement = value
    }

    @OptIn(ExperimentalSerializationApi::class)
    @SerialName(VariableType.SECURED_VARIABLE.NAME)
    @Serializable(with = SecuredVariableSerializer::class)
    data class SecuredVariable(
        private val value: JsonElement,
        @Serializable(with = DataTypeSerializer::class) override val type: VariableDataType,
        override val identifier: VariableIdentifier,
        override val properties: VariableProperties? = null,
        val securedRef: String
    ) : VariableInstance() {

        /**
         * Convert the variable to the proper type. We store numbers as strings, but when we want to use it as a number (e.g. in a calculation), we need to convert it to a number so it won't get quoted.
         */
        override fun toProperType(): JsonElement = TypeToJsonElementConverter.convert(type, value)

        override fun get(): JsonElement = JsonPrimitive(SECURED_PREFIX + securedRef)

        override fun getRevealed(): JsonElement = value

        companion object {
            const val SECURED_PREFIX = "secured_"
        }
    }
}


//any index / raw int you see in this object refers to the order of the elements in the descriptor. not any external order.
//eg: 0 == value because inside "override val descriptor" that is the first element defined.
//this could be done via reflection (to map each class property with its name and serializer) so to handle schema changes to the variable classes.
//but honestly, the readability gets hit so hard that it is not worth it.

/**
 * Serializer for [VariableInstance.SecuredVariable].
 * This serializer is used to replace the value of the secured variable with its securedRef during serialisation.
 */
object SecuredVariableSerializer : KSerializer<VariableInstance.SecuredVariable> {

    override val descriptor = buildClassSerialDescriptor(VariableType.SECURED_VARIABLE.NAME) {
        element<JsonElement>("value")
        element<VariableDataType>("type")
        element<VariableIdentifier>("identifier")
        element<VariableProperties?>("properties")
        element<String>("securedRef")
    }

    @OptIn(InternalSerializationApi::class)
    override fun deserialize(decoder: Decoder): VariableInstance.SecuredVariable = decoder.decodeStructure(descriptor) {
        var value: String? = null
        var type: VariableDataType? = null
        var identifier: VariableIdentifier? = null
        var properties: VariableProperties? = null
        var ref: String? = null
        loop@ while (true) {
            when (val index = decodeElementIndex(descriptor)) {
                DECODE_DONE -> break@loop
                0 -> {
                    value = decodeStringElement(descriptor, index = 0)
                }

                1 -> {
                    type = decodeSerializableElement(descriptor, index = 1, DataTypeSerializer)
                }

                2 -> {
                    identifier = decodeSerializableElement(descriptor, index = 2, VariableIdentifier.serializer())
                }

                3 -> {
                    properties = decodeSerializableElement(descriptor, index = 3, VariableProperties.serializer())
                }

                4 -> {
                    ref = decodeStringElement(descriptor, index = 4)
                }

                else -> throw SerializationException("Unexpected index $index")
            }
        }

        if (value == null || ref == null || type == null || identifier == null) throw SerializationException()

        return@decodeStructure VariableInstance.SecuredVariable(
            //value is serialized as ref. so that we can replace via ref in jsonata
            value = JsonPrimitive(ref), type = type, identifier = identifier, properties = properties, securedRef = ref
        )
    }

    override fun serialize(encoder: Encoder, value: VariableInstance.SecuredVariable) {
        return encoder.encodeStructure(descriptor) {

            //important masking
            //set VALUE (index 0 of descriptor) to securedRef, so that we can replace via ref in jsonata
            encodeStringElement(descriptor, 0, SECURED_PREFIX + value.securedRef)

            encodeSerializableElement(descriptor, 1, DataTypeSerializer, value.type)
            encodeSerializableElement(descriptor, 2, VariableIdentifier.serializer(), value.identifier)
            encodeStringElement(descriptor, 4, value.securedRef)


            if (value.properties != null) {
                encodeSerializableElement(
                    descriptor, 3, VariableProperties.serializer(), value.properties
                )
            }
        }
    }
}