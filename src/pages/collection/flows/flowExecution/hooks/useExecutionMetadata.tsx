import { useMemo } from "react";

import { getIteratorParentId } from "@pages/configuration/flows/FlowCanvas/FlowCanvasHelper";

import {
  FlowExecutionDocument,
  FlowStateStepEntity
} from "@src/types/FlowConfiguration/FlowExecution";
import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { useFlowExecutionContext } from "../FlowExecutionContext";

export const useExecutionStepMetadata = (
  stepId: FlowStepId,
  flowExecution: FlowExecutionDocument | undefined,
  path: string[]
): FlowStateStepEntity | undefined => {
  const { iteratorStepPageMap } = useFlowExecutionContext();
  return useMemo(() => {
    if (!stepId || !flowExecution) {
      return undefined;
    }
    if (path.length > 0) {
      const iteratorParentId = getIteratorParentId(path);
      const subFlowsAmount = Object.keys(
        flowExecution?.steps[iteratorParentId]?.subFlows ?? {}
      ).length;
      const selectedIteration =
        iteratorStepPageMap?.[iteratorParentId] ?? subFlowsAmount;
      return flowExecution?.steps[iteratorParentId]?.subFlows?.[
        `${iteratorParentId}_${selectedIteration}`
      ].state.steps.entities[stepId];
    }

    return flowExecution.state.steps.entities[stepId];
  }, [flowExecution, stepId, path, iteratorStepPageMap]);
};
