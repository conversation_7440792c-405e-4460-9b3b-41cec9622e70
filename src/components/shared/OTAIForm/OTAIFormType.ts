import {
  Question,
  QuestionProperties,
  QuestionTypes
} from "@src/types/Question";

export interface OTAIFormFieldCommonProps {
  label?: string;
  description?: string;
  required?: boolean;
  hidden?: boolean;
  autoFocus?: boolean;
  disabled?: boolean;
}

export type OTAIFormFieldPositioning = Array<string | OTAIFormFieldPositioning>;

export type InternalOTAIFormField<P = QuestionProperties> =
  | InternalOTAIFormFieldQuestion<P>
  | InternalOTAIFormFieldSection;

// Alignment with Form Configuration Question
export type InternalOTAIFormFieldQuestion<P = QuestionProperties> = Omit<
  Question<P>,
  "id" | "type"
> & {
  id?: string;
  type: QuestionTypes | string;
  properties?: Question["properties"] & {
    defaultValue?: unknown;
  };
};

export enum OTAISectionVisual {
  NONE = "none",
  DIVIDER = "divider",
  BACKGROUND = "background"
}
export type OTAISectionUIControls = {
  visual?: `${OTAISectionVisual}`;
  columns?: number;
};

// Alignment with Form Configuration Section
export type InternalOTAIFormFieldSection = {
  id?: string;
  type: "section";
  identifier: string;
  description?: string;
  name: string;
  content: InternalOTAIFormField[];
  level?: number;
  properties?: {
    uiControls?: OTAISectionUIControls;
  };
};
