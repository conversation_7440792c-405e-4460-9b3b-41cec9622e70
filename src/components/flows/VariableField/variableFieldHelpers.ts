import { get } from "lodash";

import { variableCodeEditorConstants } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowStepModal/setVariables/VariableCodeEditor/variableCodeEditorConstants";

import { VariableTypeDefinition } from "@src/types/FlowConfiguration/Variables";
import { ExtendedQuestionTypes } from "@src/types/Question";

import {
  VariableFieldPart,
  VariableFieldPartVariant
} from "./VariableFieldTypes";

export function getVariableFieldParts(
  value?: string,
  prevPart?: VariableFieldPart
) {
  const parts: VariableFieldPart[] = [];

  const rawParts = String(value ?? "")?.split(
    variableCodeEditorConstants.regexToFindVariables
  );
  rawParts.forEach((rawPart, rawPartIndex) => {
    const isVariable = rawPartIndex % 2 !== 0;
    if (isVariable) {
      // If prev part is also variable or this part is the first part, add some text before
      const prevRawPart = parts[parts.length - 1];
      if (
        (!prevRawPart &&
          (prevPart?.variant === VariableFieldPartVariant.VARIABLE ||
            prevPart === undefined)) ||
        prevRawPart?.variant === VariableFieldPartVariant.VARIABLE
      ) {
        parts.push({
          value: "",
          variant: VariableFieldPartVariant.TEXT
        });
      }
      parts.push({
        value: rawPart,
        variant: isVariable
          ? VariableFieldPartVariant.VARIABLE
          : VariableFieldPartVariant.TEXT
      });
    } else if (rawPart.length > 0) {
      parts.push({
        value: rawPart,
        variant: VariableFieldPartVariant.TEXT
      });
    }
  });

  if (parts[parts.length - 1]?.variant !== VariableFieldPartVariant.TEXT) {
    parts.push({
      value: "",
      variant: VariableFieldPartVariant.TEXT
    });
  }

  return parts;
}

export function extractValueForExternal(
  variableFields: VariableFieldPart[],
  {
    variablesByPath,
    variablesByName
  }: {
    variablesByPath: { [path: string]: VariableTypeDefinition };
    variablesByName: { [path: string]: VariableTypeDefinition };
  }
) {
  return (
    variableFields
      ?.map(part => {
        if (part.variant === VariableFieldPartVariant.VARIABLE) {
          let variable = variablesByName?.[part.value];
          if (!variable) {
            variable = get(variablesByPath, part.value);
          }

          return `{{${variable?.__path ?? part.value}}}`;
        }
        return `${part.value ?? ""}`;
      })
      ?.join("")
      // remove empty variables
      .replaceAll("{{}}", "") ?? ""
  );
}

function getCursorLocation({
  nextValue,
  existingValue
}: {
  nextValue: string;
  existingValue: string;
}): number {
  if (existingValue.length > nextValue.length) {
    return nextValue.length;
  }

  let startingDiffLocation = 0;

  const prevValueChars = existingValue.split("");
  const nextValueChars = nextValue.split("");

  // Find the first difference
  for (const [index, nextValueChar] of nextValueChars.entries()) {
    const correspondingPrevValueChar = prevValueChars[index];
    const isMatch = correspondingPrevValueChar === nextValueChar;
    if (!isMatch) {
      startingDiffLocation = index;
      break;
    }
  }

  const reversedPrevValueChars = existingValue
    .slice(startingDiffLocation)
    .split("")
    .reverse();
  const reversedNextValueChars = nextValue
    .slice(startingDiffLocation)
    .split("")
    .reverse();

  // Find the last difference
  let endingDiffLocation = nextValue.length;
  for (const [
    reversedIndex,
    nextValueChar
  ] of reversedNextValueChars.entries()) {
    const correspondingPrevValueChar = reversedPrevValueChars[reversedIndex];
    const isMatch = correspondingPrevValueChar === nextValueChar;
    if (!isMatch) {
      endingDiffLocation = nextValue.length - reversedIndex;
      break;
    }
  }

  const difference = nextValue.slice(startingDiffLocation, endingDiffLocation);
  return startingDiffLocation + difference.length;
}

export function getCursorLocationWithinParts({
  nextValue,
  existingValue,
  nextParts
}: {
  nextValue: string;
  existingValue: string;
  nextParts: VariableFieldPart[];
}): {
  focusIndexWithinParts: number;
  cursorIndex: number;
} {
  const cursorLocation = getCursorLocation({
    nextValue,
    existingValue
  });
  // within the updatedParts, find the part that contains the cursor location
  let totalCursorIndex = 0;
  let focusIndexWithinParts = 0;
  let cursorIndexWithinPart = 0;
  while (cursorLocation > totalCursorIndex) {
    for (const [partIndex, part] of nextParts.entries()) {
      cursorIndexWithinPart = 0;
      focusIndexWithinParts = partIndex;
      if (part.variant === VariableFieldPartVariant.VARIABLE) {
        totalCursorIndex += 2; // {{
      }
      for (let j = 0; j < part.value.length; j++) {
        if (cursorLocation > totalCursorIndex) {
          cursorIndexWithinPart += 1;
        }
        totalCursorIndex += 1;
      }
      if (part.variant === VariableFieldPartVariant.VARIABLE) {
        totalCursorIndex += 2; // }}
      }
    }
  }

  return {
    focusIndexWithinParts,
    cursorIndex: cursorIndexWithinPart
  };
}

export function getMatchingVariable({
  value,
  variablesByName,
  variablesByPath
}: {
  value: string;
  variablesByName?: { [name: string]: VariableTypeDefinition };
  variablesByPath?: { [path: string]: VariableTypeDefinition };
}) {
  const variableParts = value.split(".");
  let pathToMatchingVariable;
  let matchingVariable;

  for (let i = variableParts.length; i > 0 && !matchingVariable; i--) {
    const variablePath = variableParts.slice(0, i).join(".");
    const currentVariable =
      variablesByName?.[variablePath] ?? variablesByPath?.[variablePath];

    const currentVariableType = getQuestionTypeDetail(
      currentVariable?.__type
    ).type;
    if (
      currentVariable &&
      Object.values(ExtendedQuestionTypes).includes(
        currentVariableType as ExtendedQuestionTypes
      )
    ) {
      matchingVariable = currentVariable;
      pathToMatchingVariable = variableParts.slice(0, i).join(".");
    }
  }

  return {
    matchingVariable,
    pathToMatchingVariable
  };
}

export function getQuestionTypeDetail(type?: string): {
  type: string;
  configurationId?: string;
} {
  if (!type) {
    return { type: "unknown" };
  }
  const [typeName, configurationId] = type.split(".");
  return {
    type: typeName,
    configurationId:
      configurationId && configurationId !== "minimal"
        ? configurationId
        : undefined
  };
}
