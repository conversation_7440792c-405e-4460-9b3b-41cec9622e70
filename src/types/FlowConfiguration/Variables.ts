import { InternalOTAIFormFieldQuestion } from "@components/shared/OTAIForm/OTAIFormType";

import {
  ExtendedQuestionTypes,
  QuestionProperties,
  QuestionTypes
} from "../Question";
import { FlowStepVariant } from "./FlowStep/FlowStep";

export enum TableVariableColumn {
  ADD = "ADD",
  DELETE = "DELETE",
  UPDATE = "UPDATE",
  REORDER = "REORDER"
}

export type VariableValue = string | number | boolean | unknown | null;
export type VariableIdentifier = string;
// Almost same as our question configuration
export type VariableConfiguration<P = QuestionProperties> = Omit<
  InternalOTAIFormFieldQuestion<P>,
  "text" | "properties"
> & {
  text?: InternalOTAIFormFieldQuestion["text"];
  properties?: InternalOTAIFormFieldQuestion<P>["properties"] &
    Record<string, unknown>;
};

export type Variable<P = QuestionProperties> = VariableConfiguration<P> & {
  value?: VariableValue;
};

/**
 * User editable fields
 * This type allows you to dynamically access and assign values
 * to the type, identifier, and value properties using a key of type {@link VariableFieldKeys}.
 * see handleSaveSetVariablesField in {@link ConfigurationFlowStepFields}
 */
export type VariableFieldKeys = keyof typeof VARIABLE_FIELD_KEYS;

export enum VARIABLE_FIELD_KEYS {
  type = "type",
  identifier = "identifier",
  value = "value",
  "properties.operation" = "properties.operation",
  "properties.columns" = "properties.columns",
  "properties.columnIdentifier" = "properties.columnIdentifier",
  "properties.rowIdentifier" = "properties.rowIdentifier",
  "properties.rowIndex" = "properties.rowIndex",
  "properties.listOperation" = "properties.listOperation",
  "properties.itemIndex" = "properties.itemIndex",
  "properties.fileOperation" = "properties.fileOperation",
  "properties.fileIndex" = "properties.fileIndex"
}

export const setVariablesTypeOptions = [
  QuestionTypes.TEXT,
  QuestionTypes.NUMBER,
  QuestionTypes.BOOLEAN,
  QuestionTypes.DATE,
  QuestionTypes.TABLE,
  QuestionTypes.JSON,
  QuestionTypes.LIST,
  QuestionTypes.FILES
];

// Used for the mock variables in the FE -> never saved to document / backend
export type VariableTypeDefinition = {
  // Path that is copied (defaults to the current path to item)
  __path?: string;
  // Type of variable
  __type?: QuestionTypes | ExtendedQuestionTypes | string;
  // Display a description for the user
  __description?: string;
  // "Name" of the variable
  __identifier?: string;
  // Configuration -> for more detailed typing
  __configuration?: VariableConfiguration;
  __sourceStepId?: string;
  __availableFromStepId?: string;
  __sourceStepVariant?: `${FlowStepVariant}`;
  // For iterator starting variables
  __iteratorParentId?: string;
  __isAggregateOutput?: boolean;
} & { [key: string]: string | VariableTypeDefinition | unknown | undefined };
