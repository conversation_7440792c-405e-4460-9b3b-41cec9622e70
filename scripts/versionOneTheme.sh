#!/bin/sh

# Get the current version from package.json
CURRENT_VERSION="$(node -p "require('./package.json').version")"
export CURRENT_VERSION

IFS='.' read -r MAJOR MINOR PATCH <<EOF
$CURRENT_VERSION
EOF

tag_exists() {
  git ls-remote --tags origin | grep -q "refs/tags/$1"
}

VERSION="$CURRENT_VERSION"

# Loop to find the next available version
while true; do
  
  if ! tag_exists "$VERSION"; then
    break
  fi

  PATCH=$((PATCH + 1))
  VERSION="$MAJOR.$MINOR.$PATCH"

done

export VERSION

echo "Current version: $CURRENT_VERSION"
echo "Next available version: $VERSION"

if [ "$VERSION" = "$CURRENT_VERSION" ]; then
  echo "No need to bump the version. Only creating a new tag"
else
  echo "Bumping version to $VERSION"
  npm version "$VERSION" --no-git-tag-version
  access_token=$(curl -s -X POST -u "${BITBUCKET_CLIENT_ID}:${BITBUCKET_CLIENT_SECRET}" \
                https://bitbucket.org/site/oauth2/access_token \
                -d grant_type=client_credentials -d scopes="repository"| jq --raw-output '.access_token')

  git remote set-url origin "https://x-token-auth:${access_token}@bitbucket.org/${BITBUCKET_REPO_FULL_NAME}"

  git add package.json package-lock.json
  
  git commit -m "OA-758 Bump version to $VERSION"
  git push origin HEAD
fi

git tag "$VERSION"
git push origin "$VERSION"
