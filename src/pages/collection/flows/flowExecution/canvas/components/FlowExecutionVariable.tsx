import React, { useMemo, useRef, useState } from "react";

import {
  Accordion,
  Box,
  Button,
  Inline,
  OpenCloseIcon,
  Pill,
  Text
} from "@oneteam/onetheme";

import { VariableField } from "@components/flows/VariableField";

import { ConfigurationFlowVariable } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowVariable/ConfigurationFlowVariable";

import { useDictionary } from "@src/hooks/useDictionary";
import { MockVariable } from "@src/types/FlowConfiguration/FlowConfiguration";
import { VariableValue } from "@src/types/FlowConfiguration/Variables";
import { QuestionTypes } from "@src/types/Question";

import "./FlowExecutionVariable.scss";
import {
  FlowExecutionVariableModal,
  FlowExecutionVariableModalRef
} from "./FlowExecutionVariableModal";

export interface FlowExecutionVariableProps {
  configurationVariable: Pick<MockVariable, "type" | "identifier">;
  outputValue: VariableValue | undefined;
  // Renders under the accordion
  variableFieldValue?: string;
  index?: number;
}

export const FlowExecutionVariable = ({
  configurationVariable,
  outputValue,
  variableFieldValue,
  index
}: FlowExecutionVariableProps) => {
  const variableModalRef = useRef<FlowExecutionVariableModalRef>(null);
  const [isOpen, setIsOpen] = useState(false);
  const d = useDictionary();
  const variablePreview = useMemo(() => {
    if (outputValue === undefined) {
      return (
        <Pill
          icon="standard"
          variant="warning"
          label={d("ui.collection.flowExecution.variables.unresolved")}
        />
      );
    }
    if (
      configurationVariable.type.toLowerCase() !== QuestionTypes.LIST &&
      configurationVariable.type.toLowerCase() !== QuestionTypes.TABLE &&
      configurationVariable.type.toLowerCase() !== QuestionTypes.JSON
    ) {
      return (
        <Box
          className="flow-execution-variable"
          onClick={e => {
            e.stopPropagation();
            variableModalRef?.current?.openModal();
          }}
        >
          <Pill label={outputValue as string} />
        </Box>
      );
    }
    return (
      <Button
        label={d("ui.collection.flowExecution.variables.viewOutput")}
        variant="text"
        size="small"
        rightIcon={{ name: "open_in_new" }}
        onClick={() => {
          variableModalRef?.current?.openModal();
        }}
      />
    );
  }, [configurationVariable, outputValue, d]);
  return (
    <>
      <Accordion
        trigger={({ onClick }) => (
          <Inline
            alignment="center"
            spaceBetween
            gap="900"
            key={configurationVariable.identifier}
            onClick={e => onClick?.(e)}
          >
            <Inline gap="050" alignment="left">
              {variableFieldValue !== undefined && (
                <OpenCloseIcon isOpen={isOpen} withTransition />
              )}
              {variableFieldValue !== undefined && index !== undefined && (
                <Text color="text-secondary" size="s" weight="semi-bold">
                  {index}
                </Text>
              )}
              <ConfigurationFlowVariable
                canBeCopied={false}
                variable={configurationVariable}
              />
            </Inline>
            <Inline
              alignment="left"
              spaceBetween
              key={configurationVariable.identifier}
              onClick={e => onClick?.(e)}
            >
              {variablePreview}
            </Inline>
          </Inline>
        )}
        isOpen={isOpen}
        onOpenChange={() => setIsOpen(!isOpen)}
      >
        {variableFieldValue !== undefined && (
          <Box padding="100" contentEditable={false}>
            <VariableField
              label={d("ui.common.value")}
              value={variableFieldValue}
            />
          </Box>
        )}
      </Accordion>
      <FlowExecutionVariableModal
        ref={variableModalRef}
        configurationVariable={configurationVariable}
        variableValue={outputValue}
      />
    </>
  );
};

FlowExecutionVariable.displayName = "FlowExecutionVariable";
