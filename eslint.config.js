// Migrated from .eslintrc.cjs to Flat Config format for ESLint v9+
// See: https://eslint.org/docs/latest/use/configure/migration-guide
import js from "@eslint/js";
import tseslint from "@typescript-eslint/eslint-plugin";
import tsParser from "@typescript-eslint/parser";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import storybook from "eslint-plugin-storybook";
import globals from "globals";

const common = {
  languageOptions: {
    parser: tsParser,
    parserOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      project: "./tsconfig.json"
    },
    globals: {
      NodeJS: true,
      ...globals.browser,
      ...globals.node
    }
  },
  plugins: {
    "@typescript-eslint": tseslint,
    "react-hooks": reactHooks,
    storybook: storybook,
    "react-refresh": reactRefresh
  },
  rules: {
    ...tseslint.configs.recommended.rules,
    ...reactHooks.configs.recommended.rules,
    ...storybook.configs.recommended.rules,
    "react-refresh/only-export-components": [
      "warn",
      { allowConstantExport: true }
    ],
    curly: "error",
    "no-restricted-imports": [
      "error",
      {
        paths: [
          {
            name: "nanoid",
            message:
              "Please use customNanoId from customNanoIdHelper instead of importing nanoid directly."
          }
        ]
      }
    ]
  }
};

/** @type {import("eslint").Linter.FlatConfig[]} */
export default [
  js.configs.recommended,
  {
    files: ["**/*.test.ts", "**/*.test.tsx"],
    ...common,
    languageOptions: {
      ...common.languageOptions,
      parserOptions: {
        ...common.languageOptions.parserOptions,
        project: "./tsconfig.tests.json"
      }
    }
  },
  {
    files: ["**/*.ts", "**/*.tsx"],
    ignores: ["**/*.test.ts", "**/*.test.tsx"],
    ...common
  },
  {
    files: ["src/helpers/customNanoIdHelper.ts"],
    rules: {
      "no-restricted-imports": "off"
    }
  },
  {
    ignores: ["dist", ".storybook/main.ts", ".storybook/preview.ts"]
  }
];
