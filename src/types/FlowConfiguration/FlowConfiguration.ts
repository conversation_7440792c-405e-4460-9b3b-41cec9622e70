import { StatusCircleVariant } from "@oneteam/onetheme";
import { z } from "zod";

import {
  MAX_NAME_LENGTH,
  MIN_NAME_LENGTH,
  commonErrors
} from "@src/constants/errorMessages";
import { Dictionary } from "@src/hooks/useDictionary";

import { QuestionProperties } from "../Question";
import { FlowContext } from "./FlowExecution";
import { FlowStep, FlowStepId, FlowStepVariant } from "./FlowStep/FlowStep";
import {
  Variable,
  VariableConfiguration,
  VariableIdentifier
} from "./Variables";

export type FlowPath = Array<string | number>;

export enum FlowConfigurationStatusType {
  ACTIVE = "active",
  INACTIVE = "inactive"
}

export interface FlowConfiguration {
  id: string;
  name: string;
  description?: string;
  labels?: string[];
  triggers?: { [stepId: FlowStepId]: FlowStep };
  start?: FlowStepId;
  steps: { [stepId: FlowStepId]: FlowStep };
  status?: `${FlowConfigurationStatusType}`;
  metadata: {
    createdAt: string;
    updatedAt: string;
  };
  hasErrors?: boolean;
  hasWarning?: { variant: StatusCircleVariant; message: string } | null;
  properties?: {
    hidden?: boolean;
  };
  // This is the configuration of the variables - The thing starting the flow has to provide these (user or system)
  // Doesn't need to be in the config doc for now - maybe for iterators
  // these variables are available inside the flow (or iterator loop) when it begins
  startingVariables?: VariableConfiguration[]; // for now we default them to the trigger's variableMappings

  // What gets outputted at the end of the flow
  endingVariables?: VariableConfiguration[]; // we can access the type already on the context so we don't need to store it here

  // later - WIP
  renames?: {
    [identifier: VariableIdentifier]: string;
  };
}

export type Flow = z.infer<ReturnType<typeof flowConfigurationSchema>>;

export const flowConfigurationSchema = (d: Dictionary) =>
  z.object({
    name: z
      .string()
      .min(2, {
        message: d(commonErrors.length, {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      })
      .max(100, {
        message: d(commonErrors.length, {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      }),
    description: z.string().optional(),
    labels: z.array(z.string()).optional()
  });

export type MockVariable<P = QuestionProperties> = Omit<
  Variable<P>,
  "value" | "properties"
> & {
  sourceStepId?: FlowStepId;
  availableFromStepId?: FlowStepId | null;
  sourceStepVariant?: `${FlowStepVariant}`;
  isStartingVariable?: boolean;
  isEndingVariable?: boolean;
  stepIds?: FlowStepId[];
  description?: string;
  iteratorParentId?: FlowStepId | null;
  isAggregateOutput?: boolean;
  configuration?: VariableConfiguration<P>;
  properties?: Variable<P>["properties"] & Record<string, unknown>;
};

export type MockFlowContext = Omit<FlowContext, "variables"> & {
  variables: {
    [identifier: VariableIdentifier]: MockVariable;
  };
  stepOutputVariables: VariableIdentifier[];
};

export type MockFlowContextWithLocalStep = MockFlowContext & {
  thisStep: Record<string, unknown>;
  thisIndex?: number;
};
