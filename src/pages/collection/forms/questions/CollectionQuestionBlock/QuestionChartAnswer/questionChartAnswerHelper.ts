import { ChartType } from "@oneteam/onetheme";

import { Question, QuestionTypes } from "@src/types/Question";
import {
  ChartConfig,
  SelectQuestionProperties
} from "@src/types/QuestionProperties";

export const generateMockData = (
  columns: Question[],
  rowCount: number
): Record<string, string>[] => {
  const mockRows: Record<string, string>[] = [];

  for (let i = 1; i <= rowCount; i++) {
    const row: Record<string, string> = {};
    columns.forEach(col => {
      switch (col.type) {
        case QuestionTypes.TEXT:
          row[col.text] = `${col.text}_${i}`;
          break;
        case QuestionTypes.NUMBER:
          // Generate a random number between 1 and 10000 as a string
          row[col.text] = (Math.floor(Math.random() * 10000) + 1).toString();
          break;
        case QuestionTypes.DATE:
          {
            const date = new Date();
            date.setDate(date.getDate() + i);
            row[col.text] = date.toISOString().split("T")[0];
          }
          break;
        case QuestionTypes.SELECT:
          {
            // Use the first option value if available, else empty string
            const options = (col.properties as SelectQuestionProperties)
              ?.options;
            if (options && options.length > 0) {
              const randomIndex = Math.floor(Math.random() * options.length);
              row[col.text] = options[randomIndex].value as string;
            } else {
              row[col.text] = "";
            }
          }
          break;
        case QuestionTypes.BOOLEAN:
          row[col.text] = i % 2 === 0 ? "true" : "false";
          break;
        default:
          row[col.text] = "";
      }
    });
    mockRows.push(row);
  }
  return mockRows;
};

export function mapIdsToNames(
  ids: string[] | undefined,
  columns: Question[] = []
): string[] {
  if (!ids) {
    return [];
  }
  return ids.map(id => {
    const col = columns.find(c => c.id === id);
    return col?.text || col?.identifier || id;
  });
}

export function mapConfigIdsToNames(
  chartConfig: ChartConfig,
  columns: Question[] = []
): ChartConfig {
  if (
    chartConfig.type === ChartType.LINE ||
    chartConfig.type === ChartType.BAR
  ) {
    return {
      ...chartConfig,
      xAxis: mapIdsToNames(chartConfig.xAxis, columns),
      series: mapIdsToNames(chartConfig.series, columns)
    };
  }

  if (chartConfig.type === ChartType.PIE) {
    return {
      ...chartConfig,
      groupBy: mapIdsToNames(chartConfig.groupBy, columns),
      series: mapIdsToNames(chartConfig.series, columns)
    };
  }

  return chartConfig;
}
