import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef } from "react";

import { Box, Stack, WhiteboardLine } from "@oneteam/onetheme";
import {
  Handle,
  NodeProps,
  Position,
  ReactFlowProvider,
  useNodeConnections
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";

import { useFlowExecutionContext } from "@pages/collection/flows/flowExecution/FlowExecutionContext";
import { useExecutionStepMetadata } from "@pages/collection/flows/flowExecution/hooks/useExecutionMetadata";
import { useFlowExecutionNavigationHelper } from "@pages/collection/flows/flowExecution/hooks/useFlowExecutionNavigationHelper";
import { FlowStep } from "@pages/configuration/flows/FlowCanvas/FlowStep/FlowStep";
import { IteratorFlowStep } from "@pages/configuration/flows/FlowCanvas/FlowStep/IteratorFlowStep/IteratorFlowStep";
import { useFlowStepHandlers } from "@pages/configuration/flows/hooks/useFlowStepHandlers";

import {
  FlowStepId,
  FlowStep as FlowStepType,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { IteratorStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

import { useConfigurationFlowContext } from "../../ConfigurationFlowContext";
import { useConfigurationFlowStep } from "../../hooks/useConfigurationFlowStep";
import { FlowAddButton } from "../FlowAddButton/FlowAddButton";
import { useFlowCanvas } from "../FlowCanvasContext";
import { getIteratorParentId } from "../FlowCanvasHelper";
import { FlowCanvasProvider } from "../FlowCanvasProvider";
import {
  ConfigurationStepNodeFactory,
  ExecutionStepNodeFactory
} from "../FlowNodeFactory";
import "./FlowNodes.scss";
import { FlowNode } from "./FlowStepNodeProps";

export const ConfigurationStepNode = (props: NodeProps<FlowNode>) => {
  const { stepId } = props.data;
  const {
    selectedStepId,
    setSelectedStepId,
    setSelectedStepIdWithUnfocus,
    setPath
  } = useConfigurationFlowContext();
  const { path } = useFlowCanvas();

  const step = useConfigurationFlowStep(stepId ?? "");

  const {
    handleAddFlowStep,
    handlePasteFlowStep,
    handleDeleteFlowStep,
    handleUpdateFlowStepName,
    handleDuplicateFlowStep
  } = useFlowStepHandlers();

  const outgoingConnections = useNodeConnections({
    id: props.id,
    handleType: "source"
  });

  const onClick = useCallback(
    (stepId: FlowStepId) => {
      if (!stepId) {
        return;
      }

      if (stepId === selectedStepId) {
        setSelectedStepIdWithUnfocus();
        return;
      }
      setSelectedStepIdWithUnfocus(stepId);
      setSelectedStepId(undefined);
      setTimeout(() => setSelectedStepId(stepId), 0);
    },
    [selectedStepId, setSelectedStepId, setSelectedStepIdWithUnfocus]
  );

  const handleDelete = useCallback(
    (stepId: FlowStepId, variant: FlowStepVariant) => {
      if (!stepId) {
        return;
      }
      handleDeleteFlowStep({
        variant: variant as `${FlowStepVariant}`,
        stepId: stepId,
        path
      });
    },
    [handleDeleteFlowStep, path]
  );

  useEffect(() => {
    if (selectedStepId === step?.id) {
      setPath(path);
    }
  });

  const bottomAddButton = useMemo(() => {
    return (
      <Stack alignment="center">
        <WhiteboardLine className="flow-step-last-edge" />
        <FlowAddButton
          addStep={variant => {
            handleAddFlowStep({
              variant,
              parentStepId: step?.id,
              branchIndex: 0,
              path
            });
          }}
          pasteStep={async clipboardStep => {
            handlePasteFlowStep({
              parentStepId: step?.id,
              branchIndex: 0,
              skipTypeSelect: true,
              clipboardStep,
              path
            });
          }}
        />
      </Stack>
    );
  }, [handleAddFlowStep, handlePasteFlowStep, path, step?.id]);

  if (!step) {
    return <></>;
  }

  return (
    <Box padding="000" className="flow-step-container" alignment="center">
      <Handle type="target" position={Position.Top} style={{ opacity: 0 }} />
      {step?.variant === FlowStepVariant.ITERATOR ? (
        <FlowCanvasProvider accessor={step.id}>
          <ReactFlowProvider>
            <IteratorFlowStep
              flowFactory={new ConfigurationStepNodeFactory()}
              canvasType={props.data.canvasType}
              flowStep={step as FlowStepType<IteratorStepProperties>}
              onClick={onClick}
              handleDelete={handleDelete}
              handleDuplicate={() =>
                step?.id && handleDuplicateFlowStep({ stepId: step?.id, path })
              }
              isSelected={step?.id === selectedStepId}
              style={props.data.hidden ? { opacity: 0 } : {}}
              className={"nodrag nopan"}
              hasConnections={outgoingConnections.length > 0}
            />
          </ReactFlowProvider>
        </FlowCanvasProvider>
      ) : (
        <FlowStep
          flowStep={step as FlowStepType}
          onClick={() => onClick(String(step?.id))}
          handleDelete={() =>
            handleDelete(String(step?.id), step?.variant as FlowStepVariant)
          }
          handleDuplicate={() =>
            handleDuplicateFlowStep({ stepId: String(step?.id), path })
          }
          isSelected={step?.id === selectedStepId}
          style={props.data.hidden ? { opacity: 0 } : {}}
          className={"nodrag nopan"}
          onNameChange={name =>
            handleUpdateFlowStepName({
              stepId: String(step?.id),
              name,
              path
            })
          }
        />
      )}
      <Handle type="source" position={Position.Bottom} style={{ opacity: 0 }} />
      {outgoingConnections.length === 0 && bottomAddButton}
    </Box>
  );
};

// TODO: OA-1110 allow supporting nested iterator steps
export const ExecutionStepNode = ({ ...props }: NodeProps<FlowNode>) => {
  const outgoingConnections = useNodeConnections({
    id: props.id,
    handleType: "source"
  });

  const { stepId } = props.data;

  const ref = useRef(null);

  const {
    selectedStepId,
    setSelectedStepId,
    executionFlow,
    setExecutionFlowForModal,
    iteratorStepPageMap,
    setSummaryModalOpen,
    pathToCurrentExecution
  } = useFlowExecutionContext();

  const { path } = useFlowCanvas();
  const { setPath } = useConfigurationFlowContext();

  const step = useConfigurationFlowStep(stepId ?? "");
  const metadata = useExecutionStepMetadata(
    step?.id ?? "",
    executionFlow,
    path
  );

  const { navigateToSubFlow } = useFlowExecutionNavigationHelper();

  const onClick = useCallback(
    (stepId: FlowStepId) => {
      if (!stepId) {
        return;
      }

      if (stepId === selectedStepId) {
        setSelectedStepId(undefined);
        setSummaryModalOpen?.(false);
        return;
      }

      if (path.length > 0 && setExecutionFlowForModal && executionFlow) {
        const iteratorParentId = getIteratorParentId(path);
        const subFlowsAmount = Object.keys(
          executionFlow?.steps[iteratorParentId]?.subFlows ?? {}
        ).length;
        const selectedIteration =
          iteratorStepPageMap?.[iteratorParentId] ?? subFlowsAmount;
        setExecutionFlowForModal(
          executionFlow?.steps[iteratorParentId]?.subFlows?.[
            `${iteratorParentId}_${selectedIteration}`
          ]
        );
      } else if (setExecutionFlowForModal && executionFlow) {
        setExecutionFlowForModal(executionFlow);
      }
      setPath(path);
      setSelectedStepId(stepId);
      setSummaryModalOpen?.(true);
    },
    [
      selectedStepId,
      setSelectedStepId,
      path,
      executionFlow,
      setExecutionFlowForModal,
      setPath,
      iteratorStepPageMap,
      setSummaryModalOpen
    ]
  );

  // TODO: OA-2230 base off fed.state.steps.order instead
  const stepExecuted: boolean = useMemo(() => {
    if (!metadata && step?.variant === FlowStepVariant.TRIGGER) {
      return true;
    }
    return !!metadata;
  }, [metadata, step?.variant]);

  if (!step) {
    return <></>;
  }

  return (
    <Box
      ref={ref}
      padding="000"
      className="flow-step-container"
      alignment="center"
      style={{ opacity: stepExecuted ? 1 : 0.6 }} // TODO: OA-2230 create proper classes
      onDoubleClick={
        step?.variant === FlowStepVariant.FLOW
          ? () => navigateToSubFlow(step.id, pathToCurrentExecution)
          : undefined
      }
    >
      <Handle type="target" position={Position.Top} style={{ opacity: 0 }} />
      {step?.variant === FlowStepVariant.ITERATOR ? (
        <FlowCanvasProvider accessor={step.id}>
          <ReactFlowProvider>
            <IteratorFlowStep
              flowFactory={new ExecutionStepNodeFactory()}
              flowStep={step as FlowStepType<IteratorStepProperties>}
              canvasType={props.data.canvasType}
              onClick={onClick}
              isSelected={step?.id === selectedStepId}
              style={props.data.hidden ? { opacity: 0 } : {}}
              className={"nodrag nopan"}
              hasConnections={outgoingConnections.length > 0}
            />
          </ReactFlowProvider>
        </FlowCanvasProvider>
      ) : (
        <FlowStep
          flowStep={step as FlowStepType}
          onClick={() => onClick(String(step?.id))}
          isSelected={step?.id == selectedStepId}
          style={props.data.hidden ? { opacity: 0 } : {}}
          className={"nodrag nopan"}
        />
      )}
      <Handle type="source" position={Position.Bottom} style={{ opacity: 0 }} />
    </Box>
  );
};
