import React, { useEffect, useRef } from "react";

import { Box, FermionProps, Inline } from "../../../fermions";
import { composeComponentClassNames } from "../../../helpers";
import { ColorText } from "../../../tokens/IonsInterface.ts";
import { CloseIconButton } from "../../Button";
import { IconSize } from "../../Icon";
import { Label, LabelTextPosition } from "../../Label";
import { Radio } from "../Radio.tsx";
import "./RadioGroup.scss";

export type RadioGroupOptionValue = string | boolean | number;

export interface RadioGroupOption {
  name?: string;
  value?: RadioGroupOptionValue;
  label?: string;
  disabled?: boolean;
}

export interface RadioGroupProps {
  id?: string;
  name?: string;
  value?: RadioGroupOptionValue;
  required?: boolean;
  options: RadioGroupOption[];
  onChange?: (value?: RadioGroupOptionValue) => void;
  onPaste?: React.ClipboardEventHandler;
  onKeyDown?: React.KeyboardEventHandler;
  controlFocus?: boolean;
  defaultValue?: RadioGroupOptionValue;
  description?: string;
  tooltip?: string;
  error?: string;
  disabled?: boolean;
  label?: string;
  direction?: "row" | "column";
  labelTextPosition?: LabelTextPosition;
  width?: FermionProps["width"];
  allowClear?: boolean;
  skipFocus?: boolean;
}

export const RadioGroup = ({
  id,
  name,
  value,
  options,
  required,
  onChange,
  onPaste,
  onKeyDown,
  controlFocus,
  description,
  tooltip,
  error,
  disabled,
  label = "",
  direction = "row",
  labelTextPosition = LabelTextPosition.TOP,
  width = "fit",
  allowClear = !required,
  skipFocus = false
}: RadioGroupProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (controlFocus === true && document.activeElement !== inputRef.current) {
      inputRef.current?.focus();
    } else if (
      controlFocus === false &&
      document.activeElement === inputRef.current
    ) {
      inputRef.current?.blur();
    }
  }, [controlFocus, inputRef]);

  return (
    <Box className="radio-group" width={width}>
      <Label
        label={label}
        htmlFor={id}
        textPosition={labelTextPosition}
        disabled={disabled}
        description={description}
        tooltip={tooltip}
        error={error}
        required={required}
      >
        <Inline gap="150">
          <fieldset
            onPaste={onPaste}
            onKeyDown={onKeyDown}
            id={id}
            className={composeComponentClassNames("radio-group__items", {
              direction
            })}
            tabIndex={-1}
            role="radiogroup"
            name={name}
          >
            {options.map(option => (
              <Radio
                name={option.name}
                id={String(option.value)}
                key={String(option.value)}
                value={String(option.value)}
                label={option.label}
                disabled={disabled || option.disabled}
                isChecked={value === option.value}
                onChange={() => {
                  onChange?.(option.value);
                }}
                onPaste={onPaste}
                onKeyDown={onKeyDown}
                controlFocus={controlFocus}
                skipFocus={skipFocus}
                ref={inputRef}
              />
            ))}
          </fieldset>
          {allowClear && !disabled && value !== undefined && (
            <CloseIconButton
              className="radio-group__clear"
              onClick={e => {
                onChange?.(undefined);
                e.stopPropagation();
                e.preventDefault();
              }}
              skipFocus
              color={ColorText.PRIMARY}
              size={IconSize.SMALL}
            />
          )}
        </Inline>
      </Label>
    </Box>
  );
};
