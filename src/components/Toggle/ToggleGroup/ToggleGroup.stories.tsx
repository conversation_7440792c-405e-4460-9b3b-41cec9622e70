import React, { useState } from "react";

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ToggleGroup, ToggleGroupOptionValue } from "./ToggleGroup.tsx";

const meta: Meta<typeof ToggleGroup> = {
  component: ToggleGroup,
  title: "components/Toggle/ToggleGroup"
};

export default meta;

type Story = StoryObj<typeof ToggleGroup>;

const defaultArgs: Story["args"] = {
  options: [
    { value: "1", label: "Option 1" },
    { value: "2", label: "Option 2" },
    { value: "3", label: "Option 3" }
  ],
  value: ["1"],
  label: "Label"
};

const defaultRender = (args: Story["args"]) => {
  const [value, setValue] = useState<ToggleGroupOptionValue | undefined>(
    args?.value ?? []
  );
  return (
    <ToggleGroup
      {...args}
      id="test"
      value={value}
      onChange={value => setValue(value)}
      options={args?.options ?? []}
    />
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRender
};

export const Column: Story = {
  args: { ...defaultArgs, direction: "column" },
  render: defaultRender
};

export const Disabled: Story = {
  args: { ...defaultArgs, disabled: true },
  render: defaultRender
};

export const WithoutLabel: Story = {
  args: { ...defaultArgs, label: undefined },
  render: defaultRender
};
