import React from "react";

import { Form } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { z } from "zod";

import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";
import { QuestionTypes, getQuestionSchema } from "@src/types/Question.ts";

import { ConfigurationQuestionBlock } from "./ConfigurationQuestionBlock.tsx";

const meta: Meta<typeof ConfigurationQuestionBlock> = {
  component: ConfigurationQuestionBlock,
  title: "configuration/ConfigurationQuestionBlock"
};

export default meta;

type Story = StoryObj<typeof ConfigurationQuestionBlock>;

const defaultArgs: Story["args"] = {
  onClick: () => {},
  isSelected: false,
  isHighlighted: false
};

const defaultRenderer = (args: Story["args"]) => {
  return (
    <Form
      schema={z.object({ question: getQuestionSchema() })}
      d={() => ""}
      handleSubmit={() => console.log("Submit")}
      defaultValues={{
        question: {
          id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
          identifier: "WhatIsYourName",
          type: QuestionTypes.TEXT,
          text: "What is your name?",
          properties: {
            required: true
          }
        }
      }}
      hideFormButtons
    >
      <ConfigurationQuestionBlock
        {...args}
        path={["question"]}
        question={{
          id: "1",
          identifier: "q",
          type: QuestionTypes.TEXT,
          text: "What is your name?",
          properties: { required: true }
        }}
        onClick={() => {}}
        isSelected={args?.isSelected ?? false}
        isHighlighted={args?.isHighlighted ?? false}
        mode={ConfigurationFormMode.EDIT}
        handleDelete={() => {}}
        handleDuplicate={() => {}}
        handleEdit={() => {}}
        showReorder
      />
    </Form>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRenderer
};

export const Highlighted: Story = {
  args: { ...defaultArgs, isHighlighted: true },
  render: defaultRenderer
};

export const Selected: Story = {
  args: { ...defaultArgs, isSelected: true },
  render: defaultRenderer
};
