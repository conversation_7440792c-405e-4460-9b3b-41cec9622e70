import { type Monaco } from "@monaco-editor/react";
import { <PERSON><PERSON><PERSON>, type editor } from "monaco-editor";

import { VariableTypeDefinition } from "@src/types/FlowConfiguration/Variables";

import { variableCodeEditorConstants } from "./variableCodeEditorConstants";

const getHoverProvider =
  ({
    monaco,
    variablesByPath,
    variablesByName,
    displayQuestionType
  }: {
    monaco: Monaco;
    variablesByPath: { [path: string]: VariableTypeDefinition } | undefined;
    variablesByName: { [path: string]: VariableTypeDefinition } | undefined;
    displayQuestionType: (type?: string) => {
      label: string;
      description?: string;
    };
  }) =>
  (model: editor.ITextModel, position: Position) => {
    const lineContent = model.getLineContent(position.lineNumber);
    let match;
    while (
      (match =
        variableCodeEditorConstants.regexToFindVariables.exec(lineContent)) !==
      null
    ) {
      const varContent = match[1].trim();
      const curlyStart = match.index + 3; // after {{
      const curlyEnd = match.index + 2 + varContent.length;

      // Only proceed if hover is inside the {{...}}
      if (position.column >= curlyStart && position.column <= curlyEnd + 1) {
        const parts = varContent.split(".");
        let currentPath = "";
        const variables: {
          type: string;
          path: string;
        }[] = [];
        parts.forEach((varPath, index) => {
          currentPath += index === 0 ? varPath : `.${varPath}`;
          const variable =
            variablesByPath?.[currentPath] ?? variablesByName?.[currentPath];
          variables.push({
            path: varPath,
            type: variable?.__type ?? "unknown"
          });
        });

        if (!variables.length) {
          const totalVariable =
            variablesByPath?.[varContent] ?? variablesByName?.[varContent];
          if (totalVariable) {
            variables.push({
              path: varContent,
              type: totalVariable?.__type ?? "unknown"
            });
          }
        }

        if (variables.length) {
          const getVariableType = (type: string) => {
            if (!type) {
              return "";
            }
            const variableType = displayQuestionType(type);
            const description = variableType.description
              ? ` - ${variableType.description}`
              : "";
            return `\`${variableType.label}${description}\``;
          };
          return {
            range: new monaco.Range(
              position.lineNumber,
              curlyStart,
              position.lineNumber,
              curlyEnd + 1
            ),
            contents: variables.flatMap(item =>
              [
                {
                  value: `**${item?.path}** ${getVariableType(item?.type)}`
                }
              ].filter(Boolean)
            )
          };
        }
      }
    }
    return undefined;
  };

const getMarkers = ({
  model,
  monaco,
  variablesByPath,
  variablesByName
}: {
  model: editor.ITextModel;
  monaco: Monaco;
  variablesByPath: { [path: string]: VariableTypeDefinition } | undefined;
  variablesByName: { [path: string]: VariableTypeDefinition } | undefined;
}) => {
  const value = model.getValue();
  const markers: editor.IMarkerData[] = [];
  let match;

  while (
    (match = variableCodeEditorConstants.regexToFindVariables.exec(value)) !==
    null
  ) {
    const varName = match[1].trim();

    const variable = variablesByPath?.[varName] ?? variablesByName?.[varName];
    let prevVariable;

    // Variable should only contain _ . alphanumeric chars
    if (!/^[a-zA-Z0-9_.]+$/.test(varName)) {
      const start = model.getPositionAt(match.index + 2);
      const end = model.getPositionAt(match.index + 2 + varName.length);
      markers.push({
        severity: monaco.MarkerSeverity.Error,
        message: `Variable contains invalid characters: ${varName}`,
        startLineNumber: start.lineNumber,
        startColumn: start.column,
        endLineNumber: end.lineNumber,
        endColumn: end.column
      });
      continue;
    }

    if (!variable) {
      const variableParts = varName.split(".");
      if (variableParts.length > 1) {
        for (let i = variableParts.length - 1; i > 0 && !prevVariable; i--) {
          const variablePath = variableParts.slice(0, i).join(".");
          prevVariable =
            variablesByName?.[variablePath] ?? variablesByPath?.[variablePath];
        }
      }
    }

    if (!variable && (!prevVariable || prevVariable?.__type !== "json")) {
      const start = model.getPositionAt(match.index + 2);
      const end = model.getPositionAt(match.index + 2 + varName.length);
      markers.push({
        severity: monaco.MarkerSeverity.Error,
        message: `Unknown variable: ${varName}`,
        startLineNumber: start.lineNumber,
        startColumn: start.column,
        endLineNumber: end.lineNumber,
        endColumn: end.column
      });
    }
  }
  return markers;
};

export const variableCodeEditorHelpers = {
  getHoverProvider,
  getMarkers
};
