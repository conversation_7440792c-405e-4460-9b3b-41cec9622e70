import React, { useMemo } from "react";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { FlowResult } from "@src/types/FlowConfiguration/FlowExecution";

import { useFlowExecutionContext } from "../../FlowExecutionContext";
import { FlowExecutionErrorBox } from "../components/FlowExecutionErrorBox";
import { FlowPath } from "../components/FlowPath";

export const SummaryFields = () => {
  const { executionFlow, pathToCurrentExecution } = useFlowExecutionContext();
  const { configurationFlow } = useConfigurationFlowContext();

  const failedStepConfiguration = useMemo(() => {
    const failedStepId = executionFlow?.state.steps.order?.at(-1);
    const failedStep = configurationFlow?.steps[failedStepId ?? ""];
    return failedStep;
  }, [configurationFlow?.steps, executionFlow?.state.steps.order]);

  if (!configurationFlow || !executionFlow) {
    return <></>;
  }
  return (
    <>
      {executionFlow?.state?.result === FlowResult.FAILED && (
        <FlowExecutionErrorBox
          message={executionFlow?.state.message ?? ""}
          failedStepConfiguration={failedStepConfiguration}
          executionFlow={executionFlow}
        />
      )}

      <FlowPath
        configurationFlow={configurationFlow}
        flowExecution={executionFlow}
        pathToCurrentExecution={pathToCurrentExecution}
        showBreadcrumb
      />
    </>
  );
};
