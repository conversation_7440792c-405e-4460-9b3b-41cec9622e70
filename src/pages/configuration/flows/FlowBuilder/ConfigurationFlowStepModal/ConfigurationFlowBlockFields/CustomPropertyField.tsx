import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";
import { useOutletContext } from "react-router-dom";

import {
  Accordion,
  Box,
  Button,
  ColorText,
  CustomAccordionTrigger,
  Divider,
  Heading,
  HeadingSize,
  IconButton,
  Inline,
  Label,
  OpenCloseIcon,
  SelectOptionType,
  Stack,
  TableField,
  TableFieldCellType,
  TableFieldCellValue,
  TableFieldColumnId,
  TableFieldColumns,
  TableFieldRemoveRow,
  TableFieldSetRow
} from "@oneteam/onetheme";

import { runBooleanConditionCheck } from "@helpers/conditionHelper";
import { getByPath } from "@helpers/configurationFormHelper";
import {
  getLocalStepContext,
  populateRealValuesUsingMockFlowContext
} from "@helpers/flows/flowHelpers";

import { VariableField } from "@components/flows/VariableField";
import { OTAIFormFieldQuestion } from "@components/shared/OTAIForm/OTAIFormFieldQuestion";
import { OTAIFormFieldSelect } from "@components/shared/OTAIForm/OTAIFormFieldQuestion/OTAIFormFieldSelect";
import { OTAIFormFieldSection } from "@components/shared/OTAIForm/OTAIFormFieldSection/OTAIFormFieldSection";
import {
  InternalOTAIFormField,
  InternalOTAIFormFieldQuestion,
  InternalOTAIFormFieldSection
} from "@components/shared/OTAIForm/OTAIFormType";

import { ConfigurationFlowMode } from "@pages/configuration/ConfigurationFlowMode";
import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { useConfigurationFlowError } from "@pages/configuration/flows/hooks/useConfigurationFlowError";
import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { commonIcons } from "@src/constants/iconConstants";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import { Condition } from "@src/types/FlowConfiguration/Condition.ts";
import {
  FlowPath,
  MockFlowContext
} from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  ActionStepProperties,
  CommonTypedStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import {
  VariableIdentifier,
  VariableValue
} from "@src/types/FlowConfiguration/Variables";
import { Question, QuestionTypes } from "@src/types/Question";
import {
  CommonQuestionProperties,
  TableQuestionDisplayAs,
  TableQuestionProperties
} from "@src/types/QuestionProperties";

import "./ConfigurationFlowStepFields.scss";

type FlowInputQuestion = CommonQuestionProperties & {
  regex?: string;
  type?: string;
  properties?: {
    regex?: string;
    required?: boolean | Condition;
    hidden?: boolean | Condition;
    type?: string;
  };
  onlyTriggerChangeWhenBlur?: boolean;
};

export const CustomPropertyField = ({
  question,
  selectedStep,
  handleSaveInput,
  disabled,
  mockFlowContext,
  debugMode,
  pathInDocument,
  isListOfInputs,
  index,
  parentIdentifier,
  onlyTriggerChangeWhenBlur = false
}: {
  question: InternalOTAIFormField;
  selectedStep: FlowStep;
  handleSaveInput: (value: string, identifier: string) => void;
  disabled?: boolean;
  mockFlowContext?: MockFlowContext;
  debugMode?: boolean;
  pathInDocument: FlowPath;
  isListOfInputs?: boolean;
  index?: number;
  parentIdentifier?: VariableIdentifier;
  onlyTriggerChangeWhenBlur?: boolean;
}) => {
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();

  const [resolvedDisplayValue, setResolvedDisplayValue] = useState<
    string | undefined
  >(undefined);
  const [dynamicOptions, setDynamicOptions] = useState<SelectOptionType[]>([]);

  const variableType = useMemo(
    () => (question as FlowInputQuestion).properties?.type,
    [question]
  );

  const stepProperties = useMemo(
    () => selectedStep?.properties as CommonTypedStepProperties,
    [selectedStep]
  );
  const answer = useMemo(() => {
    if (
      isListOfInputs &&
      index !== undefined &&
      parentIdentifier &&
      stepProperties?.inputs?.[parentIdentifier]
    ) {
      return (
        (
          stepProperties.inputs[parentIdentifier] as Array<{
            [identifier: string]: string;
          }>
        )[index][question.identifier] ?? ""
      );
    }
    const rawAnswer = stepProperties?.inputs?.[question.identifier] ?? "";
    if (question.type === QuestionTypes.MULTISELECT) {
      if (!rawAnswer) {
        return [];
      }
      return JSON.parse(rawAnswer as string) ?? [];
    }
    return rawAnswer as string;
  }, [
    stepProperties,
    question.identifier,
    question.type,
    isListOfInputs,
    index,
    parentIdentifier
  ]);

  const resolvedQuestion = useMemo(() => {
    let resolvedQuestion = question;
    if (mockFlowContext && JSON.stringify(question).includes("{{")) {
      resolvedQuestion = populateRealValuesUsingMockFlowContext(
        question,
        getLocalStepContext({
          flowContext: mockFlowContext,
          step: selectedStep,
          index
        }),
        true
      );

      if (index !== undefined) {
        resolvedQuestion = populateRealValuesUsingMockFlowContext(
          resolvedQuestion,
          getLocalStepContext({
            flowContext: mockFlowContext,
            step: selectedStep,
            index
          }),
          true
        );
      }

      // Temporary fix until we have conditional logic
      // E.g. a question select needs formConfigurationId to be set, but before formConfigurationId is selected, select will throw an error
      if (JSON.stringify(resolvedQuestion).includes("{{")) {
        resolvedQuestion.type = "variable" as QuestionTypes;
      }
    }
    return resolvedQuestion;
  }, [mockFlowContext, question, selectedStep, index]);

  const isRecognizedType = useMemo(
    () =>
      Object.values(QuestionTypes).includes(
        resolvedQuestion.type as QuestionTypes
      ),
    [resolvedQuestion.type]
  );

  const resolvedQuestionProperties = useMemo(
    () => (resolvedQuestion?.properties as FlowInputQuestion) ?? {},
    [resolvedQuestion?.properties]
  );

  const regex = useMemo(
    () =>
      resolvedQuestionProperties?.regex ??
      resolvedQuestionProperties?.properties?.regex ??
      undefined,
    [resolvedQuestionProperties]
  );

  const { getServerError } = useConfigurationFlowError();
  const error = getServerError(
    [...pathInDocument, question.identifier],
    (question as InternalOTAIFormFieldQuestion)?.text
  )?.message;

  const required = useMemo(
    () =>
      runBooleanConditionCheck(
        resolvedQuestionProperties?.required ??
          resolvedQuestionProperties?.properties?.required
      ),
    [
      resolvedQuestionProperties?.properties?.required,
      resolvedQuestionProperties?.required
    ]
  );
  const hidden = useMemo(
    () =>
      runBooleanConditionCheck(
        resolvedQuestionProperties?.hidden ??
          resolvedQuestionProperties?.properties?.hidden
      ),
    [
      resolvedQuestionProperties?.hidden,
      resolvedQuestionProperties?.properties?.hidden
    ]
  );

  // Seed default value
  useEffect(() => {
    if (answer || hidden) {
      return;
    }
    const configurationProperties = resolvedQuestion.properties as {
      defaultValue?: string;
      properties?: { defaultValue?: string };
    };
    const defaultValue =
      configurationProperties?.defaultValue ??
      configurationProperties?.properties?.defaultValue;
    if (defaultValue !== undefined && defaultValue !== answer) {
      handleSaveInput(defaultValue ?? "", resolvedQuestion.identifier);
    }
  }, [answer, handleSaveInput, resolvedQuestion, hidden]);

  if (hidden) {
    if (answer) {
      handleSaveInput("", resolvedQuestion.identifier);
    }
    return null;
  }

  const labelDescription = () => {
    if (debugMode) {
      const labelItems = [
        resolvedQuestion.identifier,
        resolvedQuestion.type,
        variableType,
        resolvedQuestion?.description
      ];
      return labelItems.filter(Boolean).join(" - ");
    }

    return resolvedQuestion?.description;
  };

  if (resolvedQuestion.type === "section") {
    const section = resolvedQuestion as InternalOTAIFormFieldSection;
    return (
      <OTAIFormFieldSection
        key={resolvedQuestion.identifier}
        name={section.name}
        uiControls={section.properties?.uiControls}
      >
        {(resolvedQuestion as InternalOTAIFormFieldSection).content?.map(
          (question, questionIndex) => (
            <CustomPropertyField
              key={`question-${question.identifier ?? question.id}-${questionIndex}`}
              question={question}
              selectedStep={selectedStep}
              handleSaveInput={handleSaveInput}
              disabled={disabled}
              mockFlowContext={mockFlowContext}
              debugMode={debugMode}
              pathInDocument={pathInDocument}
              isListOfInputs={isListOfInputs}
              index={index}
              parentIdentifier={parentIdentifier}
            />
          )
        )}
      </OTAIFormFieldSection>
    );
  } else if (resolvedQuestion.type === QuestionTypes.TABLE) {
    if (
      (question.properties as TableQuestionProperties).uiControls?.displayAs ===
      TableQuestionDisplayAs.LIST_OF_INPUTS
    ) {
      return (
        <TableListOfInputsField
          key={resolvedQuestion.identifier}
          question={question as Question<TableQuestionProperties>}
          selectedStep={selectedStep as FlowStep<ActionStepProperties>}
          disabled={disabled}
          mockFlowContext={mockFlowContext}
          debugMode={debugMode}
          pathInDocument={pathInDocument}
          docChange={docChange}
        />
      );
    } else {
      return (
        <Stack width="100" key={resolvedQuestion.identifier}>
          <Label
            label={resolvedQuestion.text}
            description={labelDescription()}
            tooltip={resolvedQuestion?.tooltip}
            required={required}
            hideRequiredIndicator={!resolvedQuestion.text}
          />
          <TableListField
            question={question as Question<TableQuestionProperties>}
            selectedStep={selectedStep as FlowStep<ActionStepProperties>}
            pathInDocument={pathInDocument}
            docChange={docChange}
          />
        </Stack>
      );
    }
  } else if (resolvedQuestion.type === "variable") {
    return (
      <Stack width="100" key={resolvedQuestion.identifier}>
        <Label
          label={resolvedQuestion.text}
          description={labelDescription()}
          tooltip={resolvedQuestion?.tooltip}
          required={required}
          hideRequiredIndicator={!resolvedQuestion.text}
        />
        <Inline className="flow-step-field">
          <Box width="100">
            <VariableField
              value={debugMode ? answer : (resolvedDisplayValue ?? answer)}
              onChange={value => {
                handleSaveInput(value, resolvedQuestion.identifier);
              }}
              disabled={disabled}
              regex={regex ? RegExp(regex) : undefined}
              error={error}
              schema={{
                type: (question as FlowInputQuestion).properties?.type
              }}
            />
          </Box>
          {resolvedQuestionProperties?.type === "select" ? (
            <OTAIFormFieldSelect
              className="flow-step-field__variable-select"
              width="fit"
              key={`${resolvedQuestion.identifier}-select`}
              id={`${resolvedQuestion.identifier}-select`}
              disabled={disabled}
              question={{
                ...resolvedQuestion,
                id: resolvedQuestion.id ?? resolvedQuestion.identifier,
                type: QuestionTypes.SELECT,
                properties: {
                  ...resolvedQuestionProperties,
                  ...resolvedQuestionProperties?.properties,
                  required: undefined,
                  hidden
                },
                text: "",
                description: ""
              }}
              answer={answer}
              clearIfOptionMissing={false}
              onChange={value => {
                handleSaveInput(value as string, resolvedQuestion.identifier);
                setResolvedDisplayValue(
                  dynamicOptions.find(option => option.value === value)?.label
                );
              }}
              onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
              dynamicOptionsCallback={options => {
                // if options haven't changed, don't update
                if (
                  JSON.stringify(options) === JSON.stringify(dynamicOptions)
                ) {
                  return;
                }

                setDynamicOptions(options);
                setResolvedDisplayValue(
                  options.find(option => option.value === answer)?.label
                );
              }}
            />
          ) : null}
        </Inline>
      </Stack>
    );
  }

  return (
    <OTAIFormFieldQuestion
      className="flow-step-field__input"
      key={resolvedQuestion.identifier}
      id={resolvedQuestion.identifier}
      question={{
        ...resolvedQuestion,
        text: (question as InternalOTAIFormFieldQuestion).text,
        id: resolvedQuestion.id ?? resolvedQuestion.identifier,
        type: isRecognizedType
          ? (resolvedQuestion.type as QuestionTypes)
          : QuestionTypes.TEXT,
        properties: {
          ...resolvedQuestionProperties,
          regex: regex ? RegExp(regex) : undefined,
          required: resolvedQuestionProperties?.required,
          hidden: undefined
        }
      }}
      disabled={disabled}
      answer={answer}
      onChange={value => {
        handleSaveInput(value as string, resolvedQuestion.identifier);
      }}
      onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
      // TODO: add error if there is an invalid use of variable in here
      error={error}
    />
  );
};

const TableListOfInputsField = ({
  question,
  selectedStep,
  disabled,
  mockFlowContext,
  debugMode,
  pathInDocument,
  docChange
}: {
  question: Question<TableQuestionProperties>;
  selectedStep: FlowStep<ActionStepProperties>;
  disabled?: boolean;
  mockFlowContext?: MockFlowContext;
  debugMode?: boolean;
  pathInDocument: FlowPath;
  docChange: DocChange;
}) => {
  const d = useDictionary();
  const { configurationFlowMode } = useConfigurationFlowContext();

  const initAnswer = useMemo(() => {
    const columns = question.properties?.columns;
    const emptyValues: { [identifier: VariableIdentifier]: VariableValue } = {};

    // Create empty values object with all columns
    columns?.forEach(column => {
      if (column.properties?.required === true) {
        emptyValues[column.identifier] = "";
      }
    });
    return emptyValues;
  }, [question?.properties?.columns]);

  const answers: Array<{
    [identifier: VariableIdentifier]: VariableValue;
  }> = useMemo(() => {
    const answersFromProps =
      (selectedStep?.properties?.inputs?.[question.identifier] as Array<{
        [identifier: VariableIdentifier]: VariableValue;
      }>) ?? [];

    if (
      answersFromProps.length === 0 &&
      question?.properties?.columns &&
      question.properties.columns.length > 0
    ) {
      // Add first empty answer
      answersFromProps.push(initAnswer);
    }

    return answersFromProps;
  }, [
    selectedStep?.properties?.inputs,
    question?.properties?.columns,
    initAnswer,
    question.identifier
  ]);

  // State to track expanded answers
  const [expandedAnswer, setExpandedAnswer] = useState(
    answers.reduce((acc, _, index) => ({ ...acc, [index]: true }), {})
  );

  const handleAddAnswer = useCallback(() => {
    if (!docChange) {
      return;
    }
    docChange(d => {
      const inputs = getByPath<{
        [identifier: VariableIdentifier]: VariableValue;
      }>(d, pathInDocument as string[]);
      if (inputs[question.identifier]) {
        (
          inputs[question.identifier] as Array<{
            [identifier: VariableIdentifier]: VariableValue;
          }>
        ).push(initAnswer);
      }
    });

    setExpandedAnswer({
      ...expandedAnswer,
      [answers.length]: true
    });
  }, [
    docChange,
    pathInDocument,
    initAnswer,
    answers.length,
    expandedAnswer,
    question.identifier
  ]);

  const handleRemoveAnswer = useCallback(
    (index: number) => {
      if (!docChange) {
        return;
      }

      docChange(doc => {
        const inputs = getByPath<{
          [identifier: VariableIdentifier]: VariableValue;
        }>(doc, pathInDocument as string[]);
        const answers = inputs[question.identifier] as Array<{
          [identifier: VariableIdentifier]: VariableValue;
        }>;
        if (answers.length === 1) {
          alert(
            d(
              "ui.configuration.flows.step.variants.action.propertiesModal.delete.lastAnswer"
            )
          );
          return;
        }
        answers.splice(index, 1);
      });
    },
    [docChange, pathInDocument, d, question.identifier]
  );

  const handleSaveAnswers = useCallback(
    (index: number, value: VariableValue, identifier: string) => {
      docChange(d => {
        const inputs =
          getByPath<{
            [identifier: VariableIdentifier]: VariableValue;
          }>(d, pathInDocument as string[]) ?? {};
        // Initialize answers if it doesn't exist
        inputs[question.identifier] ??= [{}];

        const answers = inputs[question.identifier] as Array<{
          [identifier: VariableIdentifier]: VariableValue;
        }>;
        answers[index] ??= {};
        if (answers[index][identifier] !== value) {
          answers[index][identifier] = value;
        }
      });
    },
    [docChange, pathInDocument, question.identifier]
  );

  const handleAccordionToggle = useCallback((answerIndex: number) => {
    setExpandedAnswer(prev => ({
      ...prev,
      [answerIndex]: !prev[answerIndex]
    }));
  }, []);

  const handleExpandAll = useCallback(() => {
    setExpandedAnswer(
      answers.reduce((acc, _, index) => ({ ...acc, [index]: true }), {})
    );
  }, [answers]);

  const handleCollapseAll = useCallback(() => {
    setExpandedAnswer({});
  }, []);

  // Render a single answer item
  const renderAnswerItem = useCallback(
    (answerIndex: number) => {
      const columns = question.properties?.columns || [];
      return (
        <>
          {answerIndex === 0 && (
            <Divider key={`answer-start-divider-${answerIndex}`} size="small" />
          )}
          <Stack
            className="custom-property-field__answer-item"
            height="fit"
            key={`answer-${answerIndex}`}
          >
            <Accordion
              contentOverflow="visible"
              trigger={AnswerRenderTrigger({
                index: answerIndex,
                handleRemoveAnswer: handleRemoveAnswer,
                d,
                parentText: question.text,
                disableDelete:
                  configurationFlowMode !== ConfigurationFlowMode.EDIT
              })}
              isOpen={expandedAnswer[answerIndex] as boolean}
              onOpenChange={() => handleAccordionToggle(answerIndex)}
              style={{
                paddingTop: "var(--spacing-100)",
                paddingBottom: "var(--spacing-100)"
              }}
            >
              <Stack
                gap="150"
                style={{
                  paddingLeft: "var(--spacing-050)",
                  paddingTop: "var(--spacing-100, 2px)",
                  marginBottom: "var(--spacing-100, 2px)"
                }}
              >
                {columns.map((column, index) => (
                  <CustomPropertyField
                    key={`column-${column.identifier ?? column.id}-${index}`}
                    question={column}
                    selectedStep={selectedStep}
                    handleSaveInput={(value, identifier) => {
                      handleSaveAnswers(answerIndex, value, identifier);
                    }}
                    disabled={disabled}
                    mockFlowContext={mockFlowContext}
                    debugMode={debugMode}
                    pathInDocument={pathInDocument}
                    isListOfInputs={true}
                    index={answerIndex}
                    parentIdentifier={question.identifier}
                  />
                ))}
              </Stack>
            </Accordion>
            {answerIndex < answers.length - 1 && (
              <Divider key={`answer-end-divider-${answerIndex}`} size="small" />
            )}
          </Stack>
        </>
      );
    },
    [
      question.properties?.columns,
      expandedAnswer,
      handleRemoveAnswer,
      handleAccordionToggle,
      selectedStep,
      handleSaveAnswers,
      disabled,
      mockFlowContext,
      debugMode,
      pathInDocument,
      answers.length,
      d,
      question.identifier,
      question.text,
      configurationFlowMode
    ]
  );

  // Check if any answers are expanded
  const hasExpandedAnswers = useMemo(() => {
    return Object.values(expandedAnswer)?.some(item => item === true);
  }, [expandedAnswer]);

  return (
    <Stack
      gap="000"
      height="100"
      style={{ maxHeight: "100%" }}
      className="custom-property-field"
    >
      <Stack
        gap="100"
        style={{ maxHeight: "100%" }}
        className="custom-property-field__answers"
      >
        {answers.length > 0 && (
          <Box height="fit" style={{ maxHeight: "100%" }}>
            {answers.map((_, index) => renderAnswerItem(index))}
          </Box>
        )}
      </Stack>
      <Stack
        gap="100"
        alignment="left"
        className="custom-property-field__actions"
      >
        {answers.length > 0 && <Divider size="small" />}
        <Inline spaceBetween width="100">
          {configurationFlowMode === ConfigurationFlowMode.EDIT && (
            <Button
              label={`${d(
                "ui.configuration.flows.step.variants.action.propertiesModal.addButton"
              )} ${question.text}`}
              variant="secondary"
              onClick={handleAddAnswer}
              leftIcon={{ name: "add" }}
            />
          )}

          {answers.length > 0 && (
            <Box alignment="right">
              {!hasExpandedAnswers ? (
                <Button
                  variant="text"
                  label={d(
                    "ui.configuration.flows.step.variants.common.propertiesModal.expandAll"
                  )}
                  onClick={handleExpandAll}
                />
              ) : (
                <Button
                  variant="text"
                  label={d(
                    "ui.configuration.flows.step.variants.common.propertiesModal.collapseAll"
                  )}
                  onClick={handleCollapseAll}
                />
              )}
            </Box>
          )}
        </Inline>
      </Stack>
    </Stack>
  );
};

const AnswerRenderTrigger = ({
  index,
  handleRemoveAnswer,
  parentText,
  disableDelete
}: {
  index: number;
  handleRemoveAnswer: (index: number) => void;
  d: Dictionary;
  parentText?: VariableIdentifier;
  disableDelete?: boolean;
}): CustomAccordionTrigger => {
  return ({ isOpen, onClick }) => (
    <Inline width="100" alignment="left" spaceBetween>
      <Inline
        width="100"
        onClick={onClick}
        style={{ cursor: "pointer" }}
        overflow="hidden"
        alignment="left"
        gap="050"
      >
        <Inline alignment="left" gap="000">
          <OpenCloseIcon isOpen={isOpen} />
          <Heading size={HeadingSize.XXS} color={ColorText.SECONDARY}>
            {parentText} {index + 1}
          </Heading>
        </Inline>
      </Inline>
      {!disableDelete && (
        <IconButton
          {...commonIcons.delete}
          onClick={() => handleRemoveAnswer(index)}
          color="text-secondary"
        />
      )}
    </Inline>
  );
};

const TableListField = ({
  question,
  selectedStep,
  pathInDocument,
  docChange
}: {
  question: Question<TableQuestionProperties>;
  selectedStep: FlowStep<ActionStepProperties>;
  pathInDocument: FlowPath;
  docChange: DocChange;
}) => {
  const initAnswer = useMemo(() => {
    const columns = question.properties?.columns;
    const emptyValues: { [identifier: VariableIdentifier]: VariableValue } = {};

    // Create empty values object with all columns
    columns?.forEach(column => {
      emptyValues[column.identifier] = "";
    });
    return emptyValues;
  }, [question?.properties?.columns]);

  const answers: Array<{
    [identifier: VariableIdentifier]: VariableValue;
  }> = useMemo(() => {
    docChange(d => {
      const properties = getByPath<CommonTypedStepProperties>(
        d,
        pathInDocument.slice(0, -1)
      );
      properties.inputs ??= {};
    });
    const answersFromProps =
      (selectedStep?.properties?.inputs?.[question.identifier] as Array<{
        [identifier: VariableIdentifier]: VariableValue;
      }>) || [];

    if (
      answersFromProps.length === 0 &&
      question?.properties?.columns &&
      question.properties.columns.length > 0
    ) {
      // Add first empty answer
      answersFromProps.push(initAnswer);
    }

    return answersFromProps;
  }, [
    selectedStep?.properties?.inputs,
    question?.properties?.columns,
    initAnswer,
    question.identifier,
    docChange,
    pathInDocument
  ]);

  const setRow: TableFieldSetRow = useCallback(
    (rowValue = {}, rowIndex = answers.length, options = {}) => {
      docChange(doc => {
        const inputs = getByPath<{
          [identifier: VariableIdentifier]: VariableValue;
        }>(doc, pathInDocument as string[]);

        inputs[question.identifier] ??= [{}];
        const answers = inputs[question.identifier] as Array<{
          [identifier: VariableIdentifier]: VariableValue;
        }>;

        const index = rowIndex ?? answers.length;
        const toDelete =
          !options?.newRow || rowIndex === (answers.length ?? 0) ? 1 : 0;

        const existingRow = answers[index];
        if (options?.keepOtherRowValues && existingRow && toDelete === 1) {
          answers[index] = { ...existingRow, ...rowValue };
        } else if (existingRow) {
          answers.splice(index, toDelete, rowValue);
        } else {
          answers.push(rowValue);
        }
        return answers;
      });
    },
    [docChange, pathInDocument, question.identifier, answers.length]
  );

  const removeRow: TableFieldRemoveRow = useCallback(
    (rowIndex: number) => {
      docChange(doc => {
        const inputs = getByPath<{
          [identifier: VariableIdentifier]: VariableValue;
        }>(doc, pathInDocument as string[]);

        const answers = inputs[question.identifier] as Array<{
          [identifier: VariableIdentifier]: VariableValue;
        }>;

        answers.splice(rowIndex, 1);
        return answers;
      });
    },
    [docChange, pathInDocument, question.identifier]
  );

  const setCell = useCallback(
    (
      cellValue: TableFieldCellValue | undefined,
      rowIndex: number,
      columnId: TableFieldColumnId
    ) => {
      docChange(doc => {
        const inputs = getByPath<{
          [identifier: VariableIdentifier]: VariableValue;
        }>(doc, pathInDocument as string[]);
        inputs[question.identifier] ??= [{}];
        const answers = inputs[question.identifier] as Array<{
          [identifier: VariableIdentifier]: VariableValue;
        }>;
        if (answers[rowIndex][columnId] !== cellValue) {
          answers[rowIndex][columnId] = cellValue;
        }
      });
    },
    [docChange, pathInDocument, question.identifier]
  );

  const columns: TableFieldColumns = useMemo(() => {
    return (question.properties as TableQuestionProperties)?.columns?.map(
      (column: InternalOTAIFormFieldQuestion) => {
        return {
          label: column.text,
          id: column.identifier,
          name: column.text,
          type: column.type as TableFieldCellType,
          properties: {
            required: column.properties?.required,
            disabled: column.properties?.disabled,
            hidden: column.properties?.hidden
          },
          customCellRenderer: ({ value, onChange, disabled }) => {
            if (column.type === "variable") {
              return (
                <VariableField
                  label=""
                  value={value as string}
                  onChange={onChange}
                  disabled={disabled}
                  required={column.properties?.required}
                  schema={{
                    type:
                      (column as FlowInputQuestion).properties?.type ??
                      "unknown"
                  }}
                />
              );
            }
            return null;
          }
        };
      }
    );
  }, [question.properties]);

  return (
    <TableField
      variant="list"
      id="tableListField"
      name="table-list-field"
      value={answers}
      setRow={setRow}
      removeRow={removeRow}
      setCell={setCell}
      columns={columns ?? []}
    />
  );
};
