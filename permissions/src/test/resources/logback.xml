<configuration>
    <!--    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">-->
    <!--        <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">-->
    <!--            <jsonFormatter-->
    <!--                    class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter">-->
    <!--                <prettyPrint>true</prettyPrint>-->
    <!--            </jsonFormatter>-->
    <!--            <timestampFormat>yyyy-MM-dd' 'HH:mm:ss.SSS</timestampFormat>-->
    <!--        </layout>-->
    <!--    </appender>-->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%X{tenant}] [%X{requestId}] [%X{transaction}] [%X{connection}]
                [%X{flowExecutionId}] [%thread] %-5level %logger{36} - %msg%n
            </pattern>
        </encoder>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- File name pattern for rolled-over logs -->
            <fileNamePattern>logs/application.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- Keep 30 days of logs -->
            <maxHistory>3</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%X{tenant}] [%X{principal}] [%thread] %-5level %logger{36} - %msg%n
            </pattern>
        </encoder>
    </appender>
    <root level="WARN">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>
    <logger name="org.eclipse.jetty" level="INFO"/>
    <!--    <logger name="com.zaxxer.hikari" level="DEBUG"/>-->
    <!--    <logger name="io.netty" level="INFO"/>-->
    <!--    <logger name="reactor" level="INFO"/>-->
    <logger name="io" level="WARN"/>
    <logger name="com" level="INFO"/>
    <logger name="org" level="INFO"/>
    <!--    <logger name="EXPOSED" level="${LOGGING_EXPOSED:-INFO}"/>-->
    <logger name="Exposed" level="TRACE"/>
    <!--    <logger name="services.oneteam.ai.shared.database" level="INFO"/>-->
    <!--    <logger name="services.oneteam.ai.shared.domains.AuditService" level="WARN"/>-->
    <!--    <logger name="services.oneteam.ai.shared" level="TRACE"/>-->
    <!--    <logger name="services.oneteam.ai.shared.domains.workspace.document" level="WARN"/>-->
    <logger name="services.oneteam.ai" level="TRACE"/>
    <logger name="org.flywaydb" level="${LOGGING_FLYWAY:-INFO}"/>

</configuration>
