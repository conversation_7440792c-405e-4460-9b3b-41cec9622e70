import { Prop } from "@automerge/automerge-repo";
import { ChartType } from "@oneteam/onetheme";

import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import {
  MAX_FILE_SIZE_MB,
  MAX_NUMBER_OF_FILES
} from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Files/FilesHelper";

import {
  ConfigurationFormType,
  Section
} from "@src/types/FormConfiguration.ts";
import {
  JSONQuestionTypes,
  ListQuestionTypes,
  MultiLevelQuestion,
  Question,
  QuestionTypes,
  TableQuestionTypes
} from "@src/types/Question.ts";
import {
  ChartConfig,
  ChartQuestionProperties,
  SelectQuestionProperties
} from "@src/types/QuestionProperties";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

export function createSection(
  level: number,
  name: string,
  content: Section[] | Question[]
): Section {
  return { id: customNanoId(10), level, name, content };
}

export const initialSetupName = "Untitled";

// payload structure actually depends on the form type so might need a Factory for this. then we can get proper types
export function createQuestion(
  type: QuestionTypes = QuestionTypes.TEXT
): Question {
  switch (type) {
    case QuestionTypes.NUMBER:
      return {
        identifier: initialSetupName,
        id: customNanoId(10),
        type,
        text: initialSetupName,
        description: "",
        properties: {
          required: false,
          allowReuseAcrossForms: false,
          placeholder: ""
        }
      };
    case QuestionTypes.BOOLEAN:
      return {
        identifier: initialSetupName,
        id: customNanoId(10),
        type,
        text: initialSetupName,
        description: "",
        properties: {
          required: false,
          allowReuseAcrossForms: false
        }
      };
    case QuestionTypes.FILES:
      return {
        identifier: initialSetupName,
        id: customNanoId(10),
        type,
        text: initialSetupName,
        description: "",
        properties: {
          required: false,
          allowReuseAcrossForms: false,
          max: MAX_NUMBER_OF_FILES,
          maxFileSizeMB: MAX_FILE_SIZE_MB
        }
      };
    case QuestionTypes.SELECT:
      return {
        identifier: initialSetupName,
        id: customNanoId(10),
        type,
        text: initialSetupName,
        description: "",
        properties: {
          required: false,
          isMultiSelect: false,
          allowReuseAcrossForms: false,
          placeholder: "",
          options: [
            {
              label: "",
              value: ""
            }
          ],
          defaultValue: ""
        }
      } as Question<SelectQuestionProperties>;
    case QuestionTypes.MULTISELECT:
      return {
        identifier: "Untitled",
        id: customNanoId(10),
        type,
        text: "Untitled",
        description: "",
        properties: {
          required: false,
          isMultiSelect: true,
          allowReuseAcrossForms: false,
          placeholder: "",
          options: [
            {
              label: "",
              value: ""
            }
          ],
          defaultValue: []
        }
      } as Question<SelectQuestionProperties>;
    case QuestionTypes.TABLE:
      return {
        identifier: initialSetupName,
        id: customNanoId(10),
        type,
        text: initialSetupName,
        description: "",
        properties: {
          required: false,
          allowReuseAcrossForms: false,
          columns: []
        }
      };
    case QuestionTypes.LIST:
    case QuestionTypes.JSON:
      return {
        identifier: initialSetupName,
        id: customNanoId(10),
        type,
        text: initialSetupName,
        description: "",
        properties: {
          required: false,
          allowReuseAcrossForms: false,
          items: []
        }
      };
    case QuestionTypes.DATE:
      return {
        identifier: initialSetupName,
        id: customNanoId(10),
        type,
        text: initialSetupName,
        description: "",
        properties: {
          required: false,
          allowReuseAcrossForms: false,
          defaultValue: ""
        }
      };
    case QuestionTypes.TEXT:
    default:
      return {
        identifier: initialSetupName,
        id: customNanoId(10),
        type,
        text: initialSetupName,
        description: "",
        properties: {
          required: false,
          allowReuseAcrossForms: false,
          placeholder: "",
          defaultValue: ""
        }
      };
  }
}

export function createChartQuestion(
  chartType: ChartType
): Question<ChartQuestionProperties> {
  let chartConfig: ChartConfig;

  switch (chartType) {
    case ChartType.PIE:
      chartConfig = {
        type: chartType,
        groupBy: [],
        series: []
      };
      break;
    case ChartType.LINE:
    case ChartType.BAR:
    default:
      chartConfig = {
        type: chartType,
        xAxis: [],
        series: [],
        swapColumns: false
      };
  }

  return {
    identifier: initialSetupName,
    id: customNanoId(10),
    type: QuestionTypes.CHART,
    text: initialSetupName,
    description: "",
    properties: {
      hidden: false,
      chartConfig
    }
  };
}

export function createFirstQuestion<T extends Section | Question>(
  level: number,
  type: QuestionTypes = QuestionTypes.TEXT
): T {
  const question = createQuestion(type);
  if (level > 2) {
    return question as T;
  }
  const subSection = createSection(2, "General", [question]);
  if (level === 1) {
    return subSection as T;
  }
  return createSection(1, initialSetupName, [subSection]) as T;
}

export function findFirstQuestion(section: Section): Question {
  if (isQuestionArray(section.content)) {
    return section.content[0];
  }
  return findFirstQuestion(section.content[0]);
}

export type DocNav = DocNav[] | { [key: Prop]: DocNav };

export function getElementByPath(obj: DocNav, path: Prop[]) {
  return path.reduce((acc: DocNav, key: Prop) => {
    // @ts-expect-error - TS doesn't like this, but it's fine
    const result = acc?.[key];
    if (result === undefined) {
      // @ts-expect-error - TS doesn't like this, but it's fine
      return acc?.find?.(item => item?.id === key);
    }
    return result;
  }, obj);
}

export function getByPath<T>(obj: WorkspaceDocument, path: Prop[]) {
  return getElementByPath(obj as unknown as DocNav, path) as T;
}

export function isMultiLevelQuestionPath(path: string) {
  return (
    path.includes("properties.columns") ||
    path.includes("properties.items") ||
    path.includes("properties.charts")
  );
}

export function isMultiLevelQuestion(questionType: `${QuestionTypes}`) {
  return [QuestionTypes.TABLE, QuestionTypes.JSON, QuestionTypes.LIST]
    .map(String)
    .includes(questionType);
}

export const getChildQuestionsAccessor = (
  questionType: string,
  childType?: string
) => {
  if (questionType === QuestionTypes.TABLE) {
    if (childType === QuestionTypes.CHART) {
      return "charts";
    }
    return "columns";
  }
  return "items";
};

export const getQuestionTypeOptions = () => {
  return Object.values(QuestionTypes).filter(
    type => ![QuestionTypes.MULTISELECT].includes(type)
  ) as string[];
};

export const getQuestionTypeOptionsForForms = () => {
  return Object.values(QuestionTypes).filter(
    type => type !== QuestionTypes.MULTISELECT && type !== QuestionTypes.SCHEMA
  ) as QuestionTypes[];
};

export const getChildQuestionTypeOptions = (
  questionType: string,
  options?: { forUserSelection: boolean }
) => {
  let types: QuestionTypes[] = [];
  if (questionType === QuestionTypes.TABLE) {
    types = TableQuestionTypes;
  } else if (questionType === QuestionTypes.JSON) {
    types = JSONQuestionTypes;
  } else if (questionType === QuestionTypes.LIST) {
    types = ListQuestionTypes;
  }

  if (options?.forUserSelection) {
    types = types.filter(type => type !== QuestionTypes.MULTISELECT);
  }
  return types;
};

export function lastContent(path: Prop[]): Prop[] {
  if (path.length === 0) {
    return [];
  }
  return path.slice(0, path.lastIndexOf("content") + 1);
}

export function isSectionArray(
  content: Section[] | Question[]
): content is Section[] {
  return (content as Section[])?.[0]?.content !== undefined;
}

export function isQuestionArray(
  content: Section[] | Question[]
): content is Question[] {
  return !isSectionArray(content);
}

export const isSection = (
  sectionOrQuestion: Section | Question
): sectionOrQuestion is Section => {
  return (sectionOrQuestion as Section)?.content !== undefined;
};

export const getQuestionsInSection = (
  sectionOrQuestion: Section | Question
): Question[] => {
  const isQuestion = !isSection(sectionOrQuestion);
  if (isQuestion) {
    return [sectionOrQuestion];
  }

  return sectionOrQuestion.content?.flatMap(getQuestionsInSection);
};

export const getQuestions = (
  content: Section | MultiLevelQuestion
): Question[] => {
  return (
    isSection(content)
      ? content.content
      : content?.properties[getChildQuestionsAccessor(content.type)]
  ) as Question[];
};

export const getAllFormQuestionsWithoutMultiLevel = (
  item: ConfigurationFormType | Section
): Question[] => {
  return item.content.flatMap(sectionOrQuestion => {
    if (isSection(sectionOrQuestion)) {
      return getAllFormQuestionsWithoutMultiLevel(sectionOrQuestion);
    }
    return sectionOrQuestion;
  });
};

export const getAllFormQuestions = (
  item: ConfigurationFormType | Section
): Question[] => {
  return item.content.flatMap(sectionOrQuestion => {
    if (isSection(sectionOrQuestion)) {
      return getAllFormQuestions(sectionOrQuestion);
    } else if (isMultiLevelQuestion(sectionOrQuestion.type)) {
      return [
        sectionOrQuestion,
        ...getQuestions(sectionOrQuestion as MultiLevelQuestion)
      ];
    }
    return sectionOrQuestion;
  });
};
