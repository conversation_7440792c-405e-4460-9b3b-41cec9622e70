.radio-group {
  & .label--textPosition-top {
    gap: var(--spacing-050);
  }

  .radio {
    .label__text {
      font-size: var(--font-size-body-m, 14px);
      color: var(--color-text-primary);
      font-weight: var(--font-weight-regular, 400);
    }
  }

  &__items {
    display: flex;
    gap: var(--spacing-075) var(--spacing-150);
    border: 0;
    padding: 0;
    margin: 0;
    flex-flow: wrap;

    &--direction-column {
      flex-direction: column;
      gap: var(--spacing-100);
    }
  }
  &__clear {
    opacity: 0;
  }

  &:hover,
  &:focus-within {
    & .radio-group__clear {
      opacity: 1;
    }
  }
}
