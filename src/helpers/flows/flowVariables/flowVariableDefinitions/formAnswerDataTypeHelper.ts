import { VariableConfiguration } from "@src/types/FlowConfiguration/Variables";
import { Question, QuestionTypes } from "@src/types/Question";
import {
  ListQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";

export const getFormAnswerDataTypeConfiguration = (
  question: Question
): VariableConfiguration => {
  const identifier = question.identifier;
  switch (question.type) {
    case QuestionTypes.NUMBER:
      return {
        identifier,
        type: QuestionTypes.NUMBER
      };
    case QuestionTypes.TEXT:
    case QuestionTypes.SELECT:
      return { identifier, type: QuestionTypes.TEXT };
    case QuestionTypes.MULTISELECT:
      return {
        identifier,
        type: QuestionTypes.LIST,
        properties: {
          items: [
            {
              identifier: `${identifier}.listOf`,
              type: QuestionTypes.TEXT
            }
          ]
        }
      };
    case QuestionTypes.BOOLEAN:
      return { identifier, type: QuestionTypes.BOOLEAN };
    case QuestionTypes.DATE:
      return { identifier, type: QuestionTypes.DATE };
    case QuestionTypes.LIST:
      return {
        identifier,
        type: QuestionTypes.LIST,
        properties: {
          items: [
            getFormAnswerDataTypeConfiguration(
              (question.properties as ListQuestionProperties)?.items?.[0] ?? {
                type: QuestionTypes.TEXT,
                identifier: `${identifier}.listOf`
              }
            )
          ]
        }
      };
    case QuestionTypes.TABLE:
      return {
        identifier,
        type: QuestionTypes.LIST,
        properties: {
          items: [
            {
              type: QuestionTypes.JSON,
              identifier: `${identifier}.row`,
              properties: {
                items: [
                  {
                    id: "_rowId",
                    identifier: "RowId",
                    text: "Row ID",
                    type: QuestionTypes.TEXT
                  },
                  {
                    id: "_rowIndex",
                    identifier: "RowIndex",
                    text: "Row Index",
                    type: QuestionTypes.NUMBER
                  },
                  ...((
                    question.properties as TableQuestionProperties
                  )?.columns?.map(getFormAnswerDataTypeConfiguration) ?? [])
                ]
              }
            }
          ]
        }
      };
    case QuestionTypes.JSON:
      return { identifier, type: QuestionTypes.JSON };
    case QuestionTypes.FILES:
      return {
        identifier,
        type: QuestionTypes.LIST,
        properties: {
          items: [
            {
              identifier: `${identifier}.listOf`,
              type: QuestionTypes.JSON,
              properties: {
                items: [
                  {
                    identifier: "path",
                    type: QuestionTypes.TEXT
                  },
                  {
                    identifier: "name",
                    type: QuestionTypes.TEXT
                  }
                ]
              }
            }
          ]
        }
      };
    default:
      return {
        identifier,
        type: question.type ?? "unknown"
      };
  }
};
