import { MultiSelectValue } from "@oneteam/onetheme";
import { describe, expect, test } from "vitest";

import { MockFormQuestion } from "../../tests/MockFormQuestion";
import { Question, QuestionTypes } from "../../types/Question";
import {
  FormAnswer,
  ListAnswer,
  TableAnswer
} from "../../types/collection/CollectionForm";
import { isQuestionAnswered } from "./formHelper";

describe("isQuestionAnswered", () => {
  const commonQuestionData = {
    id: "1",
    identifier: "WhatIsYourName",
    text: "What is your name?",
    description: "Please enter your full name.",
    required: true
  };

  test("should return true for answered text question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.TEXT
    };
    const answer: FormAnswer<string> = {
      type: QuestionTypes.TEXT,
      value: "John Doe"
    };
    expect(isQuestionAnswered(question, answer)).toBe(true);
  });

  test("should return false for unanswered text question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.TEXT
    };
    const answer: FormAnswer<string> = {
      type: QuestionTypes.TEXT,
      value: ""
    };
    expect(isQuestionAnswered(question, answer)).toBe(false);
  });

  test("should return true for answered number question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.NUMBER
    };
    const answer: FormAnswer<number> = {
      type: QuestionTypes.NUMBER,
      value: 42
    };
    expect(isQuestionAnswered(question, answer)).toBe(true);
  });

  test("should return false for unanswered number question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.NUMBER
    };
    const answer: FormAnswer<number> = {
      type: QuestionTypes.NUMBER,
      value: undefined
    };
    expect(isQuestionAnswered(question, answer)).toBe(false);
  });

  test("should return true for answered single select question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.SELECT,
      properties: {
        options: [{ value: "Option 3" }]
      }
    };
    const answer: FormAnswer<string> = {
      type: QuestionTypes.SELECT,
      value: "Option 1"
    };
    expect(isQuestionAnswered(question, answer)).toBe(true);
  });

  test("should return false for single select unanswered select question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.SELECT,
      properties: {
        isMultiSelect: true,
        options: [{ value: "Option 3" }]
      }
    };
    const answer: FormAnswer<string> = {
      type: QuestionTypes.SELECT,
      value: ""
    };
    expect(isQuestionAnswered(question, answer)).toBe(false);
  });

  test("should return true for answered multi select question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.SELECT,
      properties: {
        isMultiSelect: true,
        options: [
          { label: "Option 1", value: "Option 1" },
          { label: "Option 2", value: "Option 2" },
          { label: "Option 3", value: "Option 3" }
        ]
      }
    };
    const answer: FormAnswer<string> = {
      type: QuestionTypes.SELECT,
      value: "Option 1"
    };
    expect(isQuestionAnswered(question, answer)).toBe(true);
  });

  test("should return false for multiSelect unanswered select question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.SELECT,
      properties: {
        isMultiSelect: true,
        options: [
          { label: "Option 1", value: "Option 1" },
          { label: "Option 2", value: "Option 2" },
          { label: "Option 3", value: "Option 3" }
        ]
      }
    };
    const answer: FormAnswer<MultiSelectValue> = {
      type: QuestionTypes.MULTISELECT,
      value: []
    };
    expect(isQuestionAnswered(question, answer)).toBe(false);
  });

  test("should return true for answered date question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.DATE
    };
    const answer: FormAnswer<string> = {
      type: "text",
      value: "2023-01-01"
    };
    expect(isQuestionAnswered(question, answer)).toBe(true);
  });

  test("should return false for unanswered date question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.DATE
    };
    const answer: FormAnswer<string> = {
      type: "text",
      value: ""
    };
    expect(isQuestionAnswered(question, answer)).toBe(false);
  });

  test("should return true for answered boolean question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.BOOLEAN
    };
    const answer: FormAnswer<boolean> = {
      type: "boolean",
      value: true
    };
    expect(isQuestionAnswered(question, answer)).toBe(true);
  });

  test("should return false for unanswered boolean question", () => {
    const question: Question = {
      ...commonQuestionData,
      type: QuestionTypes.BOOLEAN
    };
    const answer: FormAnswer<boolean> = {
      type: "boolean",
      value: undefined
    };
    expect(isQuestionAnswered(question, answer)).toBe(false);
  });
});

describe("isQuestionAnswered - Table", () => {
  describe("Table with all questions types", () => {
    const question = MockFormQuestion.TABLE_WITH_ALL_QUESTION_TYPES;
    test("all required questions are answered", () => {
      const answer: FormAnswer<TableAnswer> = {
        questionId: "q1",
        type: QuestionTypes.TABLE,
        value: {
          order: ["row1"],
          entities: {
            row1: {
              id: "row1",
              columns: {
                q1: {
                  questionId: "q1",
                  type: QuestionTypes.TEXT,
                  value: "answer"
                },
                q2: {
                  questionId: "q2",
                  type: QuestionTypes.NUMBER,
                  value: 2
                },
                q3: {
                  questionId: "q3",
                  type: QuestionTypes.DATE,
                  value: "2024-01-01"
                },
                q4: {
                  questionId: "q4",
                  type: QuestionTypes.SELECT,
                  value: "RED"
                },
                q5: {
                  questionId: "q5",
                  type: QuestionTypes.BOOLEAN,
                  value: true
                }
              }
            }
          }
        }
      };

      const result = isQuestionAnswered(question, answer);
      expect(result).toBe(true);
    });

    test("not all required questions are answered", () => {
      const answer = {
        questionId: "q1",
        type: QuestionTypes.TABLE,
        value: {
          order: ["row1"],
          entities: {
            row1: {
              id: "row1",
              columns: {
                q1: {
                  questionId: "q1",
                  type: QuestionTypes.TEXT,
                  value: ""
                },
                q2: {
                  questionId: "q2",
                  type: QuestionTypes.NUMBER,
                  value: 0
                },
                q3: {
                  questionId: "q3",
                  type: QuestionTypes.DATE,
                  value: "single value string"
                },
                q4: {
                  questionId: "q4",
                  type: QuestionTypes.SELECT,
                  value: "SINGLESELECT"
                },
                q5: {
                  questionId: "q5",
                  type: QuestionTypes.BOOLEAN,
                  value: true
                }
              }
            }
          }
        }
      } as FormAnswer<TableAnswer>;

      const result = isQuestionAnswered(question, answer);
      expect(result).toBe(false);
    });
  });

  describe("Answered", () => {
    const questionWithRequired =
      MockFormQuestion.TABLE_QUESTION_WITH_REQUIRED_QUESTIONS; // q1 is required, q2 is not
    const questionWithoutRequired =
      MockFormQuestion.TABLE_QUESTION_WITH_NO_REQUIRED_QUESTIONS;
    test("with required question - all rows has q1(the required question) answered", () => {
      const answer = {
        questionId: "q1",
        type: "text",
        value: {
          order: ["row1", "row2"],
          entities: {
            row1: {
              id: "row1",
              columns: {
                q1: {
                  questionId: "q1",
                  type: "text",
                  value: "answer"
                },
                q2: {
                  questionId: "q2",
                  type: "text",
                  value: ""
                }
              }
            },
            row2: {
              id: "row2",
              columns: {
                q1: {
                  questionId: "q1",
                  type: "text",
                  value: "answer"
                },
                q2: {
                  questionId: "q2",
                  type: "text",
                  value: ""
                }
              }
            }
          }
        }
      } as FormAnswer<TableAnswer>;

      const result = isQuestionAnswered(questionWithRequired, answer);
      expect(result).toBe(true);
    });

    test("no required question - all rows has at least 1 question answered", () => {
      const answer = {
        questionId: "q1",
        type: "text",
        value: {
          order: ["row1", "row2"],
          entities: {
            row1: {
              id: "row1",
              columns: {
                q1: {
                  questionId: "q1",
                  type: "text",
                  value: ""
                },
                q2: {
                  questionId: "q2",
                  type: "text",
                  value: "answer"
                }
              }
            },
            row2: {
              id: "row2",
              columns: {
                q1: {
                  questionId: "q1",
                  type: "text",
                  value: "answer"
                },
                q2: {
                  questionId: "q2",
                  type: "text",
                  value: ""
                }
              }
            }
          }
        }
      } as FormAnswer<TableAnswer>;

      const result = isQuestionAnswered(questionWithoutRequired, answer);
      expect(result).toBe(true);
    });
  });

  describe("Unanswered", () => {
    const questionWithRequired =
      MockFormQuestion.TABLE_QUESTION_WITH_REQUIRED_QUESTIONS;
    const questionWithoutRequired =
      MockFormQuestion.TABLE_QUESTION_WITH_NO_REQUIRED_QUESTIONS;
    test("with required question - row 1 does not have q1 (required question) answered", () => {
      const answer = {
        questionId: "q1",
        type: QuestionTypes.TABLE,
        value: {
          order: ["row1", "row2"],
          entities: {
            // row 1 is a required question that is NOT answered
            row1: {
              id: "row1",
              columns: {
                q1: {
                  questionId: "q1",
                  type: "text",
                  value: ""
                },
                q2: {
                  questionId: "q2",
                  type: "text",
                  value: "answer"
                }
              }
            },
            // row 1 is a NOT required question that is answered
            row2: {
              id: "row2",
              columns: {
                q1: {
                  questionId: "q2",
                  type: "text",
                  value: "answer"
                },
                q2: {
                  questionId: "q2",
                  type: "text",
                  value: "answer"
                }
              }
            }
          }
        }
      } as FormAnswer<TableAnswer>;

      const result = isQuestionAnswered(questionWithRequired, answer);
      expect(result).toBe(false);
    });

    test("no required question - row1 doesn't have answer in any column", () => {
      const answer = {
        questionId: "q1",
        type: QuestionTypes.TABLE,
        value: {
          order: ["row1", "row2"],
          entities: {
            // row 1 is a required question that is NOT answered
            row1: {
              id: "row1",
              columns: {
                q1: {
                  questionId: "q1",
                  type: "text",
                  value: ""
                },
                q2: {
                  questionId: "q2",
                  type: "text",
                  value: ""
                }
              }
            },
            // row 1 is a NOT required question that is answered
            row2: {
              id: "row2",
              columns: {
                q1: {
                  questionId: "q2",
                  type: "text",
                  value: "answer"
                },
                q2: {
                  questionId: "q2",
                  type: "text",
                  value: "answer"
                }
              }
            }
          }
        }
      } as FormAnswer<TableAnswer>;

      const result = isQuestionAnswered(questionWithoutRequired, answer);
      expect(result).toBe(false);
    });
  });
});

describe("isQuestionAnswered - List", () => {
  const question = MockFormQuestion.LIST_WITH_TEXT_QUESTION;
  test("should return false if list has no items", () => {
    const answer = {
      questionId: "L1",
      type: QuestionTypes.LIST,
      value: {
        entities: {},
        order: []
      }
    } as FormAnswer<ListAnswer>;

    const result = isQuestionAnswered(question, answer);
    expect(result).toBe(false);
  });

  test("should return false list has items but no actual answers", () => {
    const answer: FormAnswer<ListAnswer> = {
      questionId: "L1",
      type: QuestionTypes.LIST,
      value: {
        entities: {
          li1: {
            id: "li1",
            item: {
              q1: {
                questionId: "q1",
                type: QuestionTypes.TEXT,
                value: undefined
              }
            }
          },
          li2: {
            id: "li2",
            item: {
              q1: {
                questionId: "q1",
                type: QuestionTypes.TEXT,
                value: undefined
              }
            }
          },
          li3: {
            id: "li3",
            item: {
              q1: {
                questionId: "q1",
                type: QuestionTypes.TEXT,
                value: undefined
              }
            }
          }
        },
        order: []
      }
    };

    const result = isQuestionAnswered(question, answer);
    expect(result).toBe(false);
  });

  test("should return false number of items supplied is less than minLength", () => {
    const answer: FormAnswer<ListAnswer> = {
      questionId: "L1",
      type: QuestionTypes.LIST,
      value: {
        entities: {
          li1: {
            id: "li1",
            item: {
              q1: {
                questionId: "q1",
                type: QuestionTypes.TEXT,
                value: "answer"
              }
            }
          }
        },
        order: []
      }
    };

    const result = isQuestionAnswered(question, answer);
    expect(result).toBe(false);
  });

  test("should return false if there it at least one item in the list without an answer", () => {
    const answer: FormAnswer<ListAnswer> = {
      questionId: "L1",
      type: QuestionTypes.LIST,
      value: {
        entities: {
          li1: {
            id: "li1",
            item: {
              q1: {
                questionId: "q1",
                type: QuestionTypes.TEXT,
                value: "answer"
              }
            }
          },
          li2: {
            id: "li2",
            item: {
              q1: {
                questionId: "q1",
                type: QuestionTypes.TEXT,
                value: undefined
              }
            }
          }
        },
        order: []
      }
    };

    const result = isQuestionAnswered(question, answer);
    expect(result).toBe(false);
  });
  test("should return true if every item in the list has an answer", () => {
    const answer = {
      questionId: "L1",
      type: QuestionTypes.LIST,
      value: {
        entities: {
          li1: {
            id: "li1",
            item: {
              q1: {
                questionId: "q1",
                type: QuestionTypes.TEXT,
                value: "answer"
              }
            }
          },
          li2: {
            id: "li2",
            item: {
              q1: {
                questionId: "q1",
                type: QuestionTypes.TEXT,
                value: "answer"
              }
            }
          }
        },
        order: []
      }
    } as FormAnswer<ListAnswer>;

    const result = isQuestionAnswered(question, answer);
    expect(result).toBe(true);
  });
});
