import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

import { useAuth } from "@src/hooks/useAuth.js";

const maxTimeKey = "AutoLogoutMaxTime";

export function useLogout() {
  const interval = useRef<number | NodeJS.Timeout | null>(null);
  const [triggerLogout, setTriggerLogout] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  const cancelInterval = useCallback(() => {
    if (interval.current) {
      window.clearInterval(interval.current);
      interval.current = null;
    }
  }, [interval]);

  const logout = useCallback(() => {
    cancelInterval();
    localStorage.removeItem(maxTimeKey);
    localStorage.removeItem("token");
    window.location.reload();
  }, [cancelInterval]);

  useEffect(() => {
    if (triggerLogout) {
      logout();
    }
  }, [triggerLogout, logout, navigate]);

  useEffect(() => {
    const autoLogoutIdleTimer = user?.autoLogoutIdleTimer;
    if (!autoLogoutIdleTimer?.enabled) {
      return;
    }
    const newMaxTime = () => Date.now() + autoLogoutIdleTimer.value * 60 * 1000;
    const updateMaxTime = () =>
      localStorage.setItem(maxTimeKey, String(newMaxTime()));
    const getMaxTime = () =>
      parseInt(localStorage.getItem(maxTimeKey) ?? "0", 10);
    const checkMaxTime = () => {
      if (getMaxTime() < Date.now()) {
        setTriggerLogout(true);
      }
    };

    cancelInterval();
    updateMaxTime();
    interval.current = setInterval(checkMaxTime, 10 * 1000);
    document.onmousemove = updateMaxTime;
    document.onmousedown = updateMaxTime;
    document.ontouchstart = updateMaxTime;
    document.onclick = updateMaxTime;
    document.onkeydown = updateMaxTime;
    return () => {
      cancelInterval();
      document.onmousemove = null;
      document.onmousedown = null;
      document.ontouchstart = null;
      document.onclick = null;
      document.onkeydown = null;
      localStorage.removeItem(maxTimeKey);
    };
  }, [cancelInterval, user]);

  return { logout };
}
