import React, { useEffect } from "react";

import { Floating } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";

import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { useConfigurationFlowContext } from "../../ConfigurationFlowContext";
import { ConfigurationFlowProvider } from "../../ConfigurationFlowProvider";
import { MockFlowForStory } from "../ConfigurationFlowStepModal/storyConstants";
import {
  ConfigurationFlowVariablesPanel,
  VariableForPanel
} from "./ConfigurationFlowVariablesPanel";

const meta: Meta<typeof ConfigurationFlowVariablesPanel> = {
  component: ConfigurationFlowVariablesPanel,
  title: "configuration/ConfigurationFlowVariablesPanel"
};

export default meta;

type Story = StoryObj<typeof ConfigurationFlowVariablesPanel>;

const variablesSample: VariableForPanel[] = [];

const defaultArgs: Story["args"] = {
  variables: variablesSample
};

const Panel = ({ args, stepId }: { args: unknown; stepId?: FlowStepId }) => {
  const { setConfigurationFlow, setSelectedStepId } =
    useConfigurationFlowContext();

  useEffect(() => {
    setConfigurationFlow(MockFlowForStory);
  }, [setConfigurationFlow, setSelectedStepId, stepId]);

  return (
    <Floating position="bottom-left">
      <ConfigurationFlowVariablesPanel {...(args ?? {})} />
    </Floating>
  );
};
const defaultRender = (args?: unknown) => {
  return (
    <ConfigurationFlowProvider>
      <Panel args={args} />
    </ConfigurationFlowProvider>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRender
};
