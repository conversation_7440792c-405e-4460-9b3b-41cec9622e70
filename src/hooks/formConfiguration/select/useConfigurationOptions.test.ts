import { Mock, beforeEach, describe, expect, test, vi } from "vitest";

import { DynamicOptionTags } from "@src/types/QuestionProperties";
import {
  WorkspaceDocument,
  emptyWorkspaceDocument
} from "@src/types/documentTypes";

import { useDictionary } from "../../useDictionary";
import { useConfigurationOptions } from "./useConfigurationOptions";

vi.mock("@src/hooks/useDictionary.tsx", () => ({
  useDictionary: vi.fn()
}));

describe("ConfigurationProvider", () => {
  beforeEach(() => {
    // Provide a mock implementation for useDictionary
    (useDictionary as Mock).mockReturnValue(() => ({}));
  });

  test("should return foundation options for FOUNDATION_CONFIGURATION_ID tag", () => {
    const options = useConfigurationOptions(
      {
        tag: DynamicOptionTags.FOUNDATION_CONFIGURATION_ID
      },
      sampleWorkspaceDocument
    );
    expect(options).toBeDefined();
    expect(options?.options).toEqual([
      { value: "foundation-1", label: "Client" },
      { value: "foundation-2", label: "Partner" }
    ]);
  });

  test("should return form options for FORM_CONFIGURATION_ID tag", () => {
    const options = useConfigurationOptions(
      {
        tag: DynamicOptionTags.FORM_CONFIGURATION_ID,
        body: { parentFoundationConfigurationId: "foundation-1" }
      },
      sampleWorkspaceDocument
    );
    expect(options).toBeDefined();
    expect(options?.options).toEqual([
      { value: "form-1", label: "General" },
      { value: "form-2", label: "Specific" }
    ]);
  });

  test("should return form options with keys for FORM_CONFIGURATION_KEY tag", () => {
    const options = useConfigurationOptions(
      {
        tag: DynamicOptionTags.FORM_CONFIGURATION_KEY,
        body: { parentFoundationConfigurationId: "foundation-1" }
      },
      sampleWorkspaceDocument
    );
    expect(options).toBeDefined();
    expect(options?.options).toEqual([
      { value: "general", label: "General" },
      { value: "specific", label: "Specific" }
    ]);
  });

  test("should return form question options for FORM_CONFIGURATION_QUESTION_ID tag", () => {
    const options = useConfigurationOptions(
      {
        tag: DynamicOptionTags.FORM_CONFIGURATION_QUESTION_ID,
        body: { formConfigurationId: "form-1" }
      },
      sampleWorkspaceDocument
    );
    expect(options?.options).toHaveLength(2);
    expect(options?.options).toContainEqual({
      value: "F1-Q1",
      label: "f1 q1 text",
      description: "Untitled - text"
    });
    expect(options?.options).toContainEqual({
      value: "F1-Q2",
      label: "f1 q2 text",
      description: "Untitled - text"
    });
  });

  test("should return series options for SERIES_CONFIGURATION_ID tag", () => {
    const options = useConfigurationOptions(
      {
        tag: DynamicOptionTags.SERIES_CONFIGURATION_ID
      },
      sampleWorkspaceDocument
    );
    expect(options).toBeDefined();
    expect(options?.options).toEqual([
      { value: "series-1", label: "Series 1" },
      { value: "series-2", label: "Series 2" }
    ]);
  });

  test("should return interval options for INTERVAL_CONFIGURATION_ID tag", () => {
    const options = useConfigurationOptions(
      {
        tag: DynamicOptionTags.FORM_CONFIGURATION_SERIES_INTERVAL_ID,
        body: { formConfigurationId: "form-1" }
      },
      sampleWorkspaceDocument
    );
    expect(options).toBeDefined();
    expect(options?.options).toEqual([
      { value: "s1-interval-1", label: "Interval 1" },
      { value: "s1-interval-2", label: "Interval 2" }
    ]);
  });

  test("should return flow options for FLOW_CONFIGURATION_ID tag", () => {
    const options = useConfigurationOptions(
      {
        tag: DynamicOptionTags.FLOW_CONFIGURATION_ID
      },
      sampleWorkspaceDocument
    );
    expect(options).toBeDefined();
    expect(options?.options).toEqual([
      { value: "flow-1", label: "Flow 1", description: "" },
      { value: "flow-2", label: "Flow 2", description: "" }
    ]);
  });

  test("should return empty array for unsupported tags", () => {
    const options = useConfigurationOptions(
      {
        tag: "UNSUPPORTED_TAG" as DynamicOptionTags
      },
      sampleWorkspaceDocument
    );
    expect(options?.options?.length).toEqual(0);
  });

  test("should return empty array for dynamicOptions", () => {
    const options = useConfigurationOptions(undefined, sampleWorkspaceDocument);
    expect(options?.options?.length).toEqual(0);
  });
});

export const sampleWorkspaceDocument: WorkspaceDocument = {
  ...emptyWorkspaceDocument,
  id: 1,
  name: "Sample Workspace",
  key: "SAMPLEWORKSPACE",
  metadata: { createdAt: "", updatedAt: "" },
  forms: {
    "form-1": {
      id: "form-1",
      name: "General",
      level: 0,
      seriesId: "series-1",
      foundationId: "foundation-1",
      key: "general",
      content: [
        {
          content: [
            {
              content: [
                {
                  description: "",
                  id: "F1-Q1",
                  identifier: "Untitled",
                  properties: {
                    required: false
                  },
                  text: "f1 q1 text",
                  type: "text"
                }
              ],
              id: "F1-S1",
              level: 2,
              name: "General"
            },
            {
              content: [
                {
                  description: "",
                  id: "F1-Q2",
                  identifier: "Untitled",
                  properties: {
                    required: false
                  },
                  text: "f1 q2 text",
                  type: "text"
                }
              ],
              id: "F1-S1",
              level: 2,
              name: "General"
            }
          ],
          id: "section-id",
          level: 1,
          name: "Untitled"
        }
      ],
      metadata: { createdAt: "", updatedAt: "" }
    },
    "form-2": {
      id: "form-2",
      name: "Specific",
      level: 0,
      foundationId: "foundation-1",
      key: "specific",
      content: [
        {
          content: [
            {
              content: [
                {
                  description: "",
                  id: "F2-Q1",
                  identifier: "Untitled",
                  properties: {
                    required: false
                  },
                  text: "f2 q1 text",
                  type: "text"
                }
              ],
              id: "F2-S1",
              level: 2,
              name: "General"
            },
            {
              content: [
                {
                  description: "",
                  id: "F2-Q2",
                  identifier: "Untitled",
                  properties: {
                    required: false
                  },
                  text: "f2 q2 text",
                  type: "text"
                }
              ],
              id: "F2-S1",
              level: 2,
              name: "General"
            }
          ],
          id: "section-id",
          level: 1,
          name: "Untitled"
        }
      ],
      metadata: { createdAt: "", updatedAt: "" }
    }
  },
  foundations: {
    entities: {
      "foundation-1": {
        id: "foundation-1",
        name: "Client",
        identifier: "Client"
      },
      "foundation-2": {
        id: "foundation-2",
        name: "Partner",
        identifier: "Partner"
      }
    },
    order: ["foundation-1", "foundation-2"]
  },
  series: {
    "series-1": {
      id: "series-1",
      name: "Series 1",
      intervals: {
        entities: {
          "s1-interval-1": { id: "s1-interval-1", name: "Interval 1" },
          "s1-interval-2": { id: "s1-interval-2", name: "Interval 2" }
        },
        order: ["s1-interval-1", "s1-interval-2"]
      },
      metadata: { createdAt: "", updatedAt: "" }
    },
    "series-2": {
      id: "series-2",
      name: "Series 2",
      intervals: {
        entities: {
          "s2-interval-1": { id: "s2-interval-1", name: "Series2 Interval 1" },
          "s2-interval-2": { id: "s2-interval-2", name: "Series2 Interval 2" }
        },
        order: ["s2-interval-1", "s2-interval-2"]
      },
      metadata: { createdAt: "", updatedAt: "" }
    }
  },
  flows: {
    entities: {
      "flow-1": {
        id: "flow-1",
        name: "Flow 1",
        description: "",
        metadata: {
          createdAt: "",
          updatedAt: ""
        },
        triggers: {},
        steps: {}
      },
      "flow-2": {
        id: "flow-2",
        name: "Flow 2",
        description: "",
        metadata: {
          createdAt: "",
          updatedAt: ""
        },
        triggers: {},
        steps: {}
      }
    },
    order: ["flow-1", "flow-2"]
  }
};
