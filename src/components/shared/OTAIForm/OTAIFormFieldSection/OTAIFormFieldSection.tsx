import React, { PropsWithChildren } from "react";

import { ColorText, Heading, Stack } from "@oneteam/onetheme";

import { OTAISectionUIControls } from "../OTAIFormType";
import "./OTAIFormFieldSection.scss";

export const OTAIFormFieldSection = ({
  name,
  level,
  uiControls = {},
  children
}: PropsWithChildren<{
  name?: string;
  level?: number;
  uiControls?: OTAISectionUIControls;
}>) => {
  return (
    <Stack
      classNames={[
        "otai-form-field-section",
        `otai-form-field-section--visual-${uiControls.visual || "none"}`
      ]}
    >
      {name && (
        <Heading
          size={getHeadingSize({
            level
          })}
          color={getHeadingColor({
            level
          })}
        >
          {name}
        </Heading>
      )}
      <Stack
        className="otai-form-field-section__content"
        style={
          uiControls.columns && uiControls.columns > 1
            ? {
                display: "grid",
                gridTemplateColumns: `repeat(${uiControls.columns}, 1fr)`
              }
            : {}
        }
      >
        {children}
      </Stack>
    </Stack>
  );
};

function getHeadingSize({ level }: { level?: number }) {
  switch (level) {
    case 0:
      return "m";
    case 1:
      return "s";
    case 2:
      return "xs";
    default:
      return "xxs";
  }
}

function getHeadingColor({ level }: { level?: number }) {
  switch (level) {
    case 0:
    case 1:
      return ColorText.PRIMARY;
    default:
      return ColorText.SECONDARY;
  }
}
