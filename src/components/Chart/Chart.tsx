import React from "react";

import ReactECharts from "echarts-for-react";
import { merge } from "lodash";

import { Box } from "../../fermions";
import {
  getBaseOptions,
  getOptionsForSubType,
  getOptionsForType
} from "./ChartOptions";
import { ChartProps, defaultSubType } from "./ChartTypes";
import { useChartData } from "./useChart";

export const Chart = ({
  title,
  data,
  config,
  advancedOptions,
  notMerge = true,
  lazyUpdate = true,
  ...rest
}: ChartProps) => {
  const baseOptions = getBaseOptions({
    title,
    type: config.type,
    showToolbox: config.showToolbox
  });
  const typeOptions = getOptionsForType[config.type]();
  const subTypeOptions = getOptionsForSubType[config.type]({
    subType: config.subType ?? defaultSubType[config.type]
  });
  const chartOptions = merge(
    {},
    baseOptions,
    typeOptions,
    subTypeOptions,
    advancedOptions
  );
  const chartData = useChartData(config, chartOptions, data);
  const optionWithData = merge({}, chartOptions, chartData);

  return (
    <Box className="chart" {...rest}>
      <ReactECharts
        option={optionWithData}
        style={{ width: "100%", height: "100%" }}
        notMerge={notMerge}
        lazyUpdate={lazyUpdate}
      />
    </Box>
  );
};
