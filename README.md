# OneTheme

This project was build with React + TypeScript + Vite

## Local development

### Initial Setup

```sh
npm install
```

### Running locally

```sh
npm run storybook
```

Note: If using a special theme e.g. `otai`, run `npm run storybook:{theme}`

## Releasing a OneTheme Version

Merging into main triggers a OneTheme Release.

### Details

- Merging to main with a version `A.B.C`, checks if the tag `A.B.C` exists:
  - If yes, Patch Version `C` is bumped up until a version is reached for which bitbucket tag does not exist
    - Tag `A.B.[C+x]` is created.
    - Version in `package.json` file in `#main` branch is changed to `A.B.[C+x]`.
  - If not, Tag `A.B.C` is created.

## Installation in a project

### Initial Setup

1. Copy the script `scripts/installOneTheme.mjs` from this repository into the project (where you want to use OneTheme) `scripts` folder.
2. (optional) Update localPathToOneThemeFolder per project structure
3. (optional) IF you are installing with a base theme that is not "oneteam". Update `installOneTheme.mjs`:
   - Change the baseThemeCode
   - Change the themeFileLocation to where you want the theme file to sit in your project codebase
   - Import `${themeFileLocation}/${baseThemeCode}.css` css file into your App/main to ensure the theme gets applied
4. Run `scripts/installOneTheme.mjs --help`
   - Example usage:
     - `./scripts/installOneTheme.mjs -v main` To install OneTheme from a branch
     - `./scripts/installOneTheme.mjs 1.0.0` To install a version of OneTheme
     - `./scripts/installOneTheme.mjs local` To install OneTheme from path: `<path-to-your-project>/../onetheme`
     - `./scripts/installOneTheme.mjs latest` To install latest OneTheme version

#### Additions to the Project

Add to `vite.config.ts`

```ts
  build: {
    ...
    rollupOptions: {
      output: {
        manualChunks: {
          onetheme: ["@oneteam/onetheme"],
          ...
        }
      }
    }
  },

  ....

  optimizeDeps: {
    exclude: ["@oneteam/onetheme"]
  },
```

Add to App.tsx

```ts
import "@oneteam/onetheme/index.css";
```

Storybook setup (optional), add to preview.ts

```
import "@oneteam/onetheme/index.css";
```

#### (Alternative instead of recommended install script) Installing OneTheme from Bitbucket

Add a dependency `"@oneteam/onetheme": "bitbucket:devops-martinit/onetheme#<branch>"` in `package.json` file.

Example:

- `bitbucket:devops-martinit/onetheme#main`
- `bitbucket:devops-martinit/onetheme#0.0.5`

To use `oneteam` theme:

- Run `export VITE_APP_THEME=oneteam` in the terminal before running `npm install`.

> When installing onetheme like this, every time you run `npm install`, VS Code will ask for Bitbucket credentials. To avoid this:
>
> - Search for `Git: Terminal Authentication` in VS Code and disable this setting.
> - OR
> - Use Mac Terminal (instead of VS Code Terminal) to run `npm install`.

### Keeping up to date with the latest

- Run `scripts/installOneTheme.mjs -v latest` on the project branch and commit the change
- Or run `scripts/installOneTheme.mjs --help`
  - Example usage:
    - `./scripts/installOneTheme.mjs -v main` To install OneTheme from a branch
    - `./scripts/installOneTheme.mjs 1.0.0` To install a version of OneTheme
    - `./scripts/installOneTheme.mjs local` To install OneTheme from path: `<path-to-your-project>/../onetheme`
    - `./scripts/installOneTheme.mjs latest` To install latest OneTheme version

## Deploy OneTheme Storybook App

To deploy OneTheme Storybook app, head to the `Pipelines` section in Bitbucket. Click `Run Pipeline`, select a branch, and choose the `custom:deploy-storybook-app` pipeline.

## Available Scripts

In the project directory, you can run:

> The default theme for the app is `oneteam`

### `npm run storybook`

Launches storybook to oversee component library. Default Theme.

### `npm run storybook:otai`

Launches storybook with the `otai` theme.

### `npm run storybook:oneteam`

Launches storybook with the `oneteam` theme.

### `npm run build-storybook`

Builds the storybook for production. Default Theme.

### `npm run build-storybook:otai`

Builds the storybook for production with the `otai` theme.

### `npm run build-storybook:oneteam`

Builds the storybook for production with the `oneteam` theme.

### `npm run build:otai`

Builds the app for production with the `otai` theme.

### `npm run build:oneteam`

Builds the app for production with the `oneteam` theme.

### `npm run pack:otai`

Packs the app with the `otai` theme.

### `npm run pack:oneteam`

Packs the app with the `oneteam` theme.

### `npm run test-storybook`

Runs storybook tests.

### `npm run dev`

Runs the app in the development mode.<br>
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.<br>
You will also see any lint errors in the console.

### `npm run test`

Launches the test runner.

### `npm run build`

Builds the app for production. Default Theme.
