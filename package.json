{"name": "oneteam-ai-fe", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=22.12.0 <23", "npm": ">=10.9.2"}, "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest watch", "test:ci": "vitest run --coverage", "postinstall": "npm outdated || :", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook", "prepare": "husky"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.4.0", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.0.3", "@automerge/automerge-repo": "^2.1.0", "@automerge/automerge-repo-network-websocket": "^2.1.0", "@automerge/automerge-repo-react-hooks": "^2.1.0", "@automerge/automerge-repo-storage-indexeddb": "^2.1.0", "@dagrejs/dagre": "^1.1.4", "@handsontable/react-wrapper": "^16.0.1", "@hookform/resolvers": "^5.2.1", "@oneteam/onetheme": "bitbucket:devops-martinit/onetheme#main", "@tanstack/react-query": "^5.54.1", "@tanstack/react-query-devtools": "^5.59.16", "@vitest/coverage-v8": "^3.0.0", "@xyflow/react": "^12.3.6", "handsontable": "^16.0.1", "lodash": "^4.17.21", "nanoid": "^5.0.8", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-hook-form": "^7.62.0", "react-router-dom": "7.8.2", "react-zoom-pan-pinch": "^3.6.1", "use-onclickoutside": "^0.4.1", "vite-plugin-wasm": "^3.3.0", "zod": "^4.0.17", "zustand": "^5.0.6"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@eslint/js": "^9.33.0", "@storybook/addon-coverage": "^2.0.0", "@storybook/addon-docs": "^9.1.2", "@storybook/addon-links": "^9.1.2", "@storybook/builder-vite": "^9.1.2", "@storybook/react-vite": "^9.1.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.1.0", "@trivago/prettier-plugin-sort-imports": "^5.0.0", "@types/lodash": "^4.17.13", "@types/node": "^22.8.6", "@types/react": "^18.3.5", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.0", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.4.0", "@vitejs/plugin-react-swc": "^4.0.0", "commander": "^14.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-storybook": "^9.1.2", "globals": "^16.3.0", "googleapis": "^148.0.0", "husky": "^9.1.1", "jsdom": "^26.0.0", "lint-staged": "^16.0.0", "msw": "^2.7.0", "msw-storybook-addon": "^2.0.4", "prettier": "^3.3.3", "sass": "^1.90.0", "storybook": "^9.1.2", "typescript": "^5.9.2", "typescript-eslint": "^8.39.1", "vite": "^6.0.2", "vite-tsconfig-paths": "^5.0.1", "vitest": "^3.0.0", "zx": "^8.3.0"}, "lint-staged": {"*.{js,ts,tsx,jsx,json,html}": ["prettier --write"], "*.{js,ts,tsx,jsx,}": ["eslint --fix --max-warnings 0"]}, "msw": {"workerDirectory": ["public"]}}