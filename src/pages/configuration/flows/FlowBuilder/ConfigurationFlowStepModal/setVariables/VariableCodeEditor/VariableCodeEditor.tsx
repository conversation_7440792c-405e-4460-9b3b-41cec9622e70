import React, { useCallback, useEffect, useRef, useState } from "react";

import Editor, { useMonaco } from "@monaco-editor/react";
import { Box, Loading } from "@oneteam/onetheme";
import { type editor } from "monaco-editor";
import useOnClickOutside from "use-onclickoutside";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { useQuestionTypeDisplay } from "@src/hooks/useQuestionTypeDisplay";
import { VariableTypeDefinition } from "@src/types/FlowConfiguration/Variables";

import { variableCodeEditorConstants } from "./variableCodeEditorConstants";
import { variableCodeEditorHelpers } from "./variableCodeEditorHelpers";

const VariableCodeEditor = ({
  value,
  onChange,
  height = "200px"
}: {
  readonly value: string;
  readonly onChange: (newValue?: string) => void;
  readonly height?: string;
}) => {
  const { variablesByName, variablesByPath, settings } =
    useConfigurationFlowContext();

  const extractUserDisplayValue = useCallback(
    (rawValue: string) => {
      if (settings?.debugMode || (!variablesByPath && !variablesByName)) {
        return rawValue;
      }

      return rawValue.replace(
        variableCodeEditorConstants.regexToFindVariables,
        (_, varPath) => {
          return `{{${
            variablesByPath?.[varPath]?.__name ||
            variablesByName?.[varPath]?.__name ||
            varPath
          }}}`;
        }
      );
    },
    [settings?.debugMode, variablesByPath, variablesByName]
  );

  const extractValueToSave = useCallback(
    (userDisplayedValue?: string) => {
      if (!variablesByPath && !variablesByName) {
        return userDisplayedValue ?? "";
      }

      const value = (userDisplayedValue || "").replace(
        variableCodeEditorConstants.regexToFindVariables,
        (_, varPath) => {
          return `{{${
            variablesByName?.[varPath]?.__path ||
            variablesByPath?.[varPath]?.__path ||
            varPath
          }}}`;
        }
      );

      return value;
    },
    [variablesByPath, variablesByName]
  );

  const [tempValue, setTempValue] = useState(
    extractUserDisplayValue(value ?? "")
  );

  const handleEditorSave = useCallback(
    (newValue?: string) => {
      const valueToSave = extractValueToSave(newValue);
      onChange(valueToSave);
      setTempValue(extractUserDisplayValue(valueToSave));
    },
    [extractValueToSave, onChange, extractUserDisplayValue]
  );

  return (
    <VariableCodeEditorContent
      value={tempValue}
      onChange={newValue => {
        setTempValue(newValue ?? "");
      }}
      onBlur={handleEditorSave}
      height={height}
      variablesByName={variablesByName}
      variablesByPath={variablesByPath}
    />
  );
};

const VariableCodeEditorContent = ({
  value,
  onChange,
  onBlur,
  height,
  variablesByPath,
  variablesByName
}: {
  readonly value: string;
  readonly onChange: (newValue?: string) => void;
  readonly onBlur: (newValue?: string) => void;
  readonly height: string;
  readonly variablesByPath:
    | {
        [path: string]: VariableTypeDefinition;
      }
    | undefined;
  readonly variablesByName:
    | {
        [path: string]: VariableTypeDefinition;
      }
    | undefined;
}) => {
  const { displayQuestionType } = useQuestionTypeDisplay();

  const monaco = useMonaco();
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const editorContainerRef = useRef<HTMLDivElement | null>(null);

  // Register language and configuration
  useEffect(() => {
    if (monaco) {
      monaco.languages.register({
        id: variableCodeEditorConstants.LANGUAGE_NAME
      });
      monaco.languages.setLanguageConfiguration(
        variableCodeEditorConstants.LANGUAGE_NAME,
        {
          surroundingPairs: variableCodeEditorConstants.characterPairs,
          colorizedBracketPairs: variableCodeEditorConstants.characterPairs.map(
            pair => [pair.open, pair.close]
          ),
          autoClosingPairs: variableCodeEditorConstants.characterPairs,
          onEnterRules: variableCodeEditorConstants.getOnEnterRules(monaco)
        }
      );

      monaco.languages.setMonarchTokensProvider(
        variableCodeEditorConstants.LANGUAGE_NAME,
        variableCodeEditorConstants.languageDef
      );
      monaco.editor.defineTheme(
        variableCodeEditorConstants.THEME_NAME,
        variableCodeEditorConstants.themeData
      );

      // Intellisense - TODO
      // monaco.languages.registerCompletionItemProvider(variableCodeEditorConstants.LANGUAGE_NAME, {
      //   triggerCharacters: ["$"],
      //   provideCompletionItems: (model, position) => {
      //     // Logic to determine and return completion items based on 'model' and 'position'
      //     const suggestions = [];
      //     return { suggestions: suggestions };
      //   }
      // });
    }
  }, [monaco]);

  // Validation (invalid variables) display
  const updateMarkers = React.useCallback(() => {
    if (!monaco || !editorRef.current) {
      return;
    }
    const model = editorRef.current.getModel();
    if (!model) {
      return;
    }
    const markers: editor.IMarkerData[] = variableCodeEditorHelpers.getMarkers({
      model,
      monaco,
      variablesByName,
      variablesByPath
    });
    monaco.editor.setModelMarkers(model, "custom", markers);
  }, [monaco, variablesByPath, variablesByName]);

  useEffect(() => {
    if (!monaco) {
      return;
    }

    // Display variable type on hover
    const hoverProvider = monaco.languages.registerHoverProvider(
      variableCodeEditorConstants.LANGUAGE_NAME,
      {
        provideHover: variableCodeEditorHelpers.getHoverProvider({
          monaco,
          variablesByPath,
          variablesByName,
          displayQuestionType
        })
      }
    );

    return () => {
      hoverProvider.dispose();
    };
  }, [monaco, variablesByPath, variablesByName, displayQuestionType]);

  // Saving the value on blur
  useEffect(() => {
    if (!editorRef.current || !onBlur) {
      return;
    }

    const blurTextDisposable = editorRef.current.onDidBlurEditorText(() => {
      onBlur(editorRef.current?.getValue());
    });

    return () => {
      blurTextDisposable.dispose();
    };
  }, [onBlur]);

  useOnClickOutside(editorContainerRef, () => {
    if (!onBlur || !editorRef.current) {
      return;
    }
    onBlur(editorRef.current.getValue());
  });

  return (
    <Box
      ref={editorContainerRef}
      style={{
        height,
        borderRadius: "6px",
        border: "1px solid var(--color-border)"
      }}
      overflow="hidden"
      alignment="center"
    >
      {monaco ? (
        <Editor
          loading={<Loading size={22} />}
          height="100%"
          theme={variableCodeEditorConstants.THEME_NAME}
          language={variableCodeEditorConstants.LANGUAGE_NAME}
          value={value}
          onChange={newValue => {
            onChange(newValue);
            updateMarkers();
          }}
          options={variableCodeEditorConstants.editorOptions}
          onMount={editor => {
            editorRef.current = editor;
            updateMarkers();
          }}
        />
      ) : (
        <Loading size={22} />
      )}
    </Box>
  );
};

export default VariableCodeEditor;
