import React, { useEffect, useMemo, useState } from "react";
import { useOutletContext } from "react-router-dom";

import { Inline, useUrlHash } from "@oneteam/onetheme";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { FlowStepVariantPill } from "@components/flows/FlowStepVariantPill/FlowStepVariantPill.tsx";
import { FloatingModal } from "@components/shared/FloatingModal/FloatingModal.tsx";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext.tsx";
import { useFlowStepHandlers } from "@pages/configuration/flows/hooks/useFlowStepHandlers.tsx";
import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration.ts";
import {
  FlowStep,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep.ts";
import { CommonTypedStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties.ts";
import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { ConfigurationFlowStepFields } from "./ConfigurationFlowBlockFields/ConfigurationFlowStepFields.tsx";

export interface ConfigurationFlowStepModalProps {
  docChange: DocChange;
}

export const ConfigurationFlowStepModal = ({
  docChange
}: ConfigurationFlowStepModalProps) => {
  const [width, setWidth] = useState<string | undefined>("385px");
  const { urlHashDetail } = useUrlHash();

  const { handleUpdateFlowStepName } = useFlowStepHandlers();
  const {
    selectedStepId,
    setSelectedStepId,
    configurationFlow,
    setSelectingStepType,
    path
  } = useConfigurationFlowContext();

  const { document: d } = useOutletContext<{
    document: WorkspaceDocument;
  }>();

  const configurationFlowToUse = useMemo(() => {
    if (!configurationFlow) {
      return;
    }
    if (path) {
      return getByPath<FlowConfiguration>(d, [
        "flows",
        "entities",
        configurationFlow.id,
        ...path
      ]);
    }

    return configurationFlow;
  }, [configurationFlow, path, d]);

  const variant = useMemo(() => {
    if (!configurationFlowToUse || !selectedStepId) {
      return null;
    }
    return (configurationFlowToUse.steps?.[selectedStepId]?.variant ??
      FlowStepVariant.TRIGGER) as FlowStepVariant;
  }, [configurationFlowToUse, selectedStepId]);

  const mode = useMemo(
    () => urlHashDetail?.get("mode") as ConfigurationFormMode,
    [urlHashDetail]
  );
  const selectedStep = useMemo(() => {
    if (
      !selectedStepId ||
      !configurationFlowToUse ||
      selectedStepId === "none"
    ) {
      return undefined;
    }

    const isTriggerStep = configurationFlowToUse.triggers?.[selectedStepId];
    if (isTriggerStep) {
      return {
        ...(configurationFlowToUse.triggers?.[selectedStepId] ?? {})
      };
    } else {
      return {
        ...configurationFlowToUse.steps?.[selectedStepId]
      };
    }
  }, [configurationFlowToUse, selectedStepId]);

  useEffect(() => {
    document.addEventListener("keydown", e => {
      if (e.key === "Escape") {
        setSelectedStepId();
      }
    });
  }, [setSelectedStepId]);

  if (
    !selectedStep ||
    !configurationFlowToUse ||
    !selectedStepId ||
    !configurationFlow
  ) {
    return <></>;
  }

  return (
    <FloatingModal
      className="flow-configuration-modal"
      headingOverride={
        <Inline width="100">
          <FlowStepVariantPill variant={variant as FlowStepVariant} />
        </Inline>
      }
      onClose={() => setSelectedStepId()}
      width={width}
      setWidth={setWidth}
      minWidth="250px"
    >
      {selectedStep && (
        <ConfigurationFlowStepFields
          disabled={mode !== ConfigurationFormMode.EDIT}
          selectedStep={selectedStep as FlowStep}
          docChange={docChange}
          onNameChange={(value: string) => {
            handleUpdateFlowStepName({
              stepId: selectedStepId,
              name: value,
              path
            });
          }}
          openFlowStepTypeModal={() =>
            setSelectingStepType({
              type: selectedStep.variant as FlowStepVariant,
              stepId: selectedStepId,
              currentFlowStepTypePrimaryIdentifier: (
                selectedStep?.properties as CommonTypedStepProperties
              )?.typePrimaryIdentifier
            })
          }
        />
      )}
    </FloatingModal>
  );
};
