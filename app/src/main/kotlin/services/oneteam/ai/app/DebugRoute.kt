package services.oneteam.ai.app

import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.workspace.WorkspaceService
import services.oneteam.ai.shared.hasCurrentTransaction
import services.oneteam.ai.shared.withTenantScope
import services.oneteam.ai.shared.withTenantTransactionScope

fun Route.debugEndpoints(workspaceService: WorkspaceService) {

    val logger = LoggerFactory.getLogger("services.oneteam.ai.app.debugEndpoints")

    get("/tools/debug/exception/1") {
        try {
            throw IllegalStateException("This is a test exception")
        } catch (e: IllegalStateException) {
            logger.error("This is a test exception", e)
        }
    }

    get("/tools/debug/transactions/1") {
        require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
        withContext(Dispatchers.IO) {
            newSuspendedTransaction(currentCoroutineContext()) {
                require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
                delay(2000) // Simulate some processing time - if I remove this delay things will work as I expect
            }
        }
        // this next line fails
        require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
        call.respondText("OK")
    }

    get("/tools/debug/transactions/2") {
        require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
        withContext(Dispatchers.IO) {
            newSuspendedTransaction(Dispatchers.IO) {
                require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
            }
        }
        // this next line fails
        require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
        call.respondText("OK")
    }


    get("/tools/debug/transactions/3") {
        require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
        withContext(Dispatchers.IO) {
            newSuspendedTransaction {
                require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
                delay(2000) // Simulate some processing time - if I remove this delay things will work as I expect
            }
        }
        // this next line fails
        require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
        call.respondText("OK")
    }

    get("/tools/debug/transactions/4") {
        require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
        withContext(Dispatchers.IO) {
            newSuspendedTransaction {
                require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
            }
        }
        // this next line fails
        require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
        call.respondText("OK")
    }


    get("/tools/debug/transactions/5") {
        require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
        withTenantScope {
            newSuspendedTransaction {
                require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
                workspaceService.findAll()
                delay(2000)
            }
        }
        // this next line fails
        require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
        call.respondText("OK")
    }

    get("/tools/debug/transactions/6") {
        require(!hasCurrentTransaction()) { "1 - transaction should not exist at the start" }
        withTenantTransactionScope {
            require(hasCurrentTransaction()) { "2 - transaction should exist now" }
            workspaceService.findAll()
            delay(2000)
            workspaceService.findAll()
        }

        try {
            throw IllegalStateException("This is a test exception")
        } catch (e: Exception) {
            logger.error("This is a test exception", e)
            throw e
        }

        // this next line fails
        require(!hasCurrentTransaction()) { "3 - transaction should not exist at the end" }
        call.respondText("OK")
    }

}
