import { Prop } from "@automerge/automerge-repo";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { Question } from "@src/types/Question.ts";
import {
  ChartConfig,
  ChartQuestionProperties,
  TableDefaultView,
  TableQuestionProperties
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

export const chartOnChange = <C extends ChartConfig>({
  field,
  path,
  docChange
}: {
  field: keyof C;
  path: Prop[];
  docChange: (cb: (d: WorkspaceDocument) => void) => void;
}) => {
  return (value: C[keyof C]) => {
    docChange((d: WorkspaceDocument): void => {
      const q = getByPath<Question<ChartQuestionProperties>>(d, path);
      if (!q?.properties) {
        console.error("Question not found", path);
        return;
      }

      const chartConfig = q.properties.chartConfig as C;

      if (value === undefined || (typeof value === "string" && value === "")) {
        delete chartConfig[field];
      } else if (Array.isArray(value)) {
        chartConfig[field] = [...value] as C[keyof C];
      } else {
        chartConfig[field] = value;
      }
    });
  };
};

export const tableOnChange = ({
  field,
  path,
  docChange
}: {
  field: keyof TableQuestionProperties;
  path: Prop[];
  docChange: (cb: (d: WorkspaceDocument) => void) => void;
}) => {
  return (value: TableDefaultView | string[]) => {
    docChange((d: WorkspaceDocument): void => {
      const q = getByPath<Question<TableQuestionProperties>>(d, path);
      if (!q?.properties) {
        console.error("Question not found", path);
        return;
      }
      if (value === undefined) {
        delete q.properties[field];
        return;
      }

      if (field === "defaultView") {
        q.properties[field] = value as TableDefaultView;
      }
    });
  };
};
