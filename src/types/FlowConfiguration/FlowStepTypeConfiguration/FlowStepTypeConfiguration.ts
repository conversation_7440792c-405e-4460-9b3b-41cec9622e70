// For the DB table FlowStepTypeConfiguration
import { IconType } from "@oneteam/onetheme";

import { InternalOTAIFormField } from "@components/shared/OTAIForm/OTAIFormType";

import { Condition } from "../Condition";
import { FlowStepVariant } from "../FlowStep/FlowStep";
import { Variable } from "../Variables";
import { TriggerEventKey, TriggerSubscription } from "./TriggerEvent";

export type FlowStepTypeConfigurationPrimaryIdentifier = string;

// Trigger
export type TriggerFlowStepTypeConfigurationPropertiesConfiguration = {
  // STEP 1 (in flow configuration setup): collect answers from user in the step setup
  content: InternalOTAIFormField[];
  // STEP 2 (in flow execution): subscribe to trigger events - check to see if the trigger should be picked up
  subscribeTo: { [key: TriggerEventKey]: TriggerSubscription };
  // STEP 3 (in flow execution): after trigger has been picked up - map back certain values (from the subscribeTo variableMappings) from the response to the context tree
  documentation?: TriggerDocumentation[];
  userConfiguredStartingVariables?: boolean;
};

export enum RichTextNodeType {
  PARAGRAPH = "paragraph",
  CODE_BLOCK = "codeBlock"
}

export type RichTextNode = {
  type: RichTextNodeType;
  text: string; // this may contain curlys
  hidden: boolean | Condition; // this may contain curlys
};

export type TriggerDocumentation = {
  title: string;
  richText: RichTextNode[];
};

// Action
export type APIClientService = {
  url: string;
  method: "GET" | "POST" | "PUT" | "DELETE";
  body: Record<string, unknown>;
} & unknown; // extra items in here for api client

export type APIClientServiceResponse = unknown;

export type ActionFlowStepTypeConfigurationPropertiesConfiguration = {
  // STEP 1 (in flow configuration setup): collect answers from user in the step setup
  content: InternalOTAIFormField[]; // answers to these questions get saved in the action step
  // STEP 2 (in flow execution): make an api call to get the configuration for the action
  apiCall: APIClientService & {
    response: InternalOTAIFormField[]; // the response from the api call - JSON export type question configuration which contains items
  };
  // STEP 3 (in flow execution): after api call has been made - map back certain values from the response to the context tree
  variableMappings: Variable[];
  documentation: TriggerDocumentation[];
};

export type IteratorFlowStepTypeConfigurationPropertiesConfiguration = {
  // STEP 1 (in flow configuration setup): collect answers from user in the step setup
  content: InternalOTAIFormField[]; // answers to these questions get saved in the action step
  // After iterator runs, at the end we get these - map back certain values from the response to the context tree
  variableMappings: Variable[];
};

export type FlowStepTypeCategory = {
  name: string;
  icon?: IconType;
  order?: number; // used to order the categories in the UI
};

export type FlowStepTypeConfiguration = {
  // this is stored in DB
  id: string;
  type:
    | FlowStepVariant.ACTION
    | FlowStepVariant.TRIGGER
    | FlowStepVariant.ITERATOR; // matches a FlowStepVariant
  primaryIdentifier: FlowStepTypeConfigurationPrimaryIdentifier;
  name: string;
  description?: string;
  properties: {
    icon: IconType;
    category?: FlowStepTypeCategory;
    isLocked?: boolean;
    isHidden?: boolean;
    deprecated?: {
      replacement?: FlowStepTypeConfigurationPrimaryIdentifier;
      deprecatedAt: string;
    };
    configuration: // depending on if its an action or trigger
    | TriggerFlowStepTypeConfigurationPropertiesConfiguration
      | ActionFlowStepTypeConfigurationPropertiesConfiguration;
  };
};
