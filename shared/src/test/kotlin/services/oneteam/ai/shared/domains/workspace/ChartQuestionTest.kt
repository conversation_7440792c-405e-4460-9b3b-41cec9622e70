package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse

class ChartQuestionTest {

    private val question = BaseSection.ChartQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.CHART,
        text = "Line Chart",
        description = "description",
        identifier = "LineChart",
        properties = CommonQuestionProperties.ChartQuestionProperties(
            chartConfig = CommonQuestionProperties.LineChartConfig(
                xAxis = listOf("x1", "x2"),
                series = listOf("s1", "s2")
            ),
            hidden = false
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }
}