package services.oneteam.ai.flow.expression.functions

import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.assertions.throwables.shouldThrow
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import java.math.BigDecimal

class MaxPairwiseTest {

    private val maxPairwise = Pairwise(PairwiseOperation.MAX, BigDecimal.ZERO)

    companion object {
        private const val PAIRWISE_MAX_EXCEPTION_MSG = "Unsupported input types for pairwise MAX"
    }

    /*
    => MAX(["2023-01-01", "2023-03-15"], ["2023-02-01"])
    => MAX(["2023-01-01", "2023-03-15"], ["2023-02-01", "-999999999-01-01"])
    => ["2023-02-01", "2023-03-15"]
     */
    @Test
    fun `max should handle date lists of different lengths`() {
        val result = maxPairwise.perform(
            listOf(
                listOf("2023-01-01", "2023-03-15"),
                listOf("2023-02-01")
            )
        )
        result shouldBe listOf("2023-02-01", "2023-03-15")
    }

    @Test
    fun `max should handle empty lists`() {
        val result = maxPairwise.perform(listOf(emptyList<Int>(), emptyList<Int>()))
        result shouldBe emptyList<Int>()
    }

    @Test
    fun `max should throw on one empty and one non-empty list`() {
        val exception = shouldThrow<IllegalArgumentException> {
            maxPairwise.perform(listOf(emptyList<Int>(), listOf(5)))
        }
        exception.message shouldContain PAIRWISE_MAX_EXCEPTION_MSG
    }

    @Test
    fun `max should throw on all nil or empty values`() {
        val exception = shouldThrow<IllegalArgumentException> {
            maxPairwise.perform(listOf(listOf(null, ""), listOf(null, "")))
        }
        exception.message shouldContain PAIRWISE_MAX_EXCEPTION_MSG
    }

    @Test
    fun `max should throw on invalid date strings`() {
        val exception = shouldThrow<IllegalArgumentException> {
            maxPairwise.perform(listOf(listOf("2023-01-01", "not-a-date"), listOf("2022-01-01", "2024-01-01")))
        }
        exception.message shouldContain PAIRWISE_MAX_EXCEPTION_MSG
    }

    @Test
    fun `max should throw on date-time strings`() {
        val exception = shouldThrow<IllegalArgumentException> {
            maxPairwise.perform(
                listOf(
                    listOf("2023-01-01T12:00:00", "2023-01-01T13:00:00"),
                    listOf("2023-01-01T11:00:00", "2023-01-01T14:00:00")
                )
            )
        }
        exception.message shouldContain PAIRWISE_MAX_EXCEPTION_MSG
    }

    @Test
    fun `max should handle mixed date and number types`() {
        val result = maxPairwise.perform(listOf(listOf(1, "2023-01-01"), listOf(2, "2022-01-01")))
        result shouldBe listOf(BigDecimal(2), "2023-01-01")
    }

    @Test
    fun `max should handle scalar and list of dates`() {
        val result = maxPairwise.perform(listOf("2023-01-01", listOf("2022-01-01", "2024-01-01")))
        result shouldBe listOf("2023-01-01", "2024-01-01")
    }

    @Test
    fun `max should handle nested arrays and dates`() {
        val result = maxPairwise.perform(
            listOf(
                listOf(listOf("2023-01-01", "2023-02-01")),
                listOf(listOf("2023-03-01", "2023-01-05"))
            )
        )
        result shouldBe listOf(listOf("2023-03-01", "2023-02-01"))
    }

    @Test
    fun `max should handle deeply nested arrays`() {
        val result = maxPairwise.perform(
            listOf(
                listOf(listOf(listOf(1, 2), listOf(3, 4))),
                listOf(listOf(listOf(4, 1), listOf(2, 5)))
            )
        )
        result shouldBe listOf(listOf(listOf(BigDecimal(4), BigDecimal(2)), listOf(BigDecimal(3), BigDecimal(5))))
    }
}