import React from "react";

import { Form } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { z } from "zod";

import {
  ConfigurationFormMode,
  sectionSchema
} from "@src/types/FormConfiguration.ts";

import { ConfigurationFormAddLine } from "../ConfigurationFormAddLine/ConfigurationFormAddLine.tsx";
import { ConfigurationFormSectionBlock } from "./ConfigurationFormSectionBlock.tsx";

const meta: Meta<typeof ConfigurationFormSectionBlock> = {
  component: ConfigurationFormSectionBlock,
  title: "configuration/ConfigurationFormSectionBlock"
};

export default meta;

type Story = StoryObj<typeof ConfigurationFormSectionBlock>;

const defaultArgs: Story["args"] = {};

const defaultRenderer = (args: Story["args"]) => {
  return (
    <Form
      schema={z.object({ section: sectionSchema((key: string) => key) })}
      d={(key: string) => key}
      handleSubmit={() => {}}
      defaultValues={{
        section: {
          id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
          name: "Section Name",
          level: 1,
          content: []
        }
      }}
      hideFormButtons
    >
      <ConfigurationFormSectionBlock
        path={[0]}
        section={{ id: "1", level: 0, name: "section", content: [] }}
        mode={ConfigurationFormMode.EDIT}
        key={"section"}
        {...args}
      >
        Content
      </ConfigurationFormSectionBlock>
    </Form>
  );
};

export const Section: Story = {
  args: defaultArgs,
  render: defaultRenderer
};

const subSectionRenderer = (args: Story["args"]) => {
  return (
    <Form
      schema={z.object({ section: sectionSchema((key: string) => key) })}
      d={(key: string) => key}
      handleSubmit={() => console.log("Submit")}
      defaultValues={{
        section: {
          id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
          name: "Sub-Section Name",
          level: 2,
          content: []
        }
      }}
      hideFormButtons
    >
      <ConfigurationFormSectionBlock
        path={[0]}
        section={{ id: "1", level: 0, name: "section", content: [] }}
        mode={ConfigurationFormMode.EDIT}
        key={"section"}
        {...args}
      >
        Content
      </ConfigurationFormSectionBlock>
    </Form>
  );
};

export const SubSection: Story = {
  args: defaultArgs,
  render: subSectionRenderer
};

const sectionSubSectionRenderer = (args: Story["args"]) => {
  return (
    <Form
      schema={z.object({ section: sectionSchema((key: string) => key) })}
      d={(key: string) => key}
      handleSubmit={() => console.log("Submit")}
      defaultValues={{
        section: {
          id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
          name: "Section Name",
          level: 1,
          content: [
            {
              id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
              name: "General",
              level: 2,
              content: []
            },
            {
              id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
              name: "General 2",
              level: 2,
              content: []
            }
          ]
        }
      }}
      hideFormButtons
    >
      <ConfigurationFormAddLine addSection={() => {}} addQuestion={() => {}} />
      <ConfigurationFormSectionBlock
        path={[0]}
        section={{ id: "1", level: 0, name: "section", content: [] }}
        mode={ConfigurationFormMode.EDIT}
        key={"section"}
        {...args}
      >
        <ConfigurationFormSectionBlock
          path={[0, 0]}
          section={{ id: "1", level: 0, name: "section", content: [] }}
          mode={ConfigurationFormMode.EDIT}
          key={"section"}
        >
          Content
        </ConfigurationFormSectionBlock>
        <ConfigurationFormAddLine
          addSection={() => {}}
          addQuestion={() => {}}
        />
        <ConfigurationFormSectionBlock
          path={[0, 1]}
          section={{ id: "1", level: 0, name: "section", content: [] }}
          mode={ConfigurationFormMode.EDIT}
          key={"section"}
        >
          Content
        </ConfigurationFormSectionBlock>
        <ConfigurationFormAddLine
          addSection={() => {}}
          addQuestion={() => {}}
        />
      </ConfigurationFormSectionBlock>
      <ConfigurationFormAddLine addSection={() => {}} addQuestion={() => {}} />
    </Form>
  );
};

export const SectionSubSection: Story = {
  args: defaultArgs,
  render: sectionSubSectionRenderer
};
