package services.oneteam.ai.shared

import org.flywaydb.core.Flyway
import javax.sql.DataSource

class FlywayMigration {
    fun migrate(config: FlywayConfig, dataSource: DataSource) {
        flyway(config, dataSource).migrate()
    }

    fun repair(config: FlywayConfig, dataSource: DataSource) {
        flyway(config, dataSource).repair()
    }
    
    private fun flyway(
        config: FlywayConfig,
        dataSource: DataSource
    ): Flyway = Flyway.configure()
        .defaultSchema(config.schema)
        .baselineOnMigrate(true) // create the flyway_schema_history table if it doesn't exist
        .baselineVersion("0") // so our migrations start at version 1
        .placeholders(config.properties) // to handle environmental variables in migrations - use ${key} in migrations as placeholder
        .outOfOrder(true)
        .locations(*config.locations.toTypedArray())
        .dataSource(dataSource).load()
}