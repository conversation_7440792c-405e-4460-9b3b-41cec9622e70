import React, {
  CSSProperties,
  PropsWithChildren,
  ReactNode,
  useEffect,
  useMemo
} from "react";

import { Button, ButtonGroup, getClassNames } from "../../..";
import { Inline, Stack } from "../../../fermions";
import { Heading, HeadingColor } from "../../Heading";
import { Icon } from "../../Icon";
import { Text } from "../../Text";
import { Modal } from "../Modal";
import "./ConfirmationModal.scss";
import { ConfirmationModalVariant } from "./ConfirmationModalTypes";

export interface ConfirmationModalProps {
  heading?: string;
  headingOverride?: ReactNode;
  message: string | ReactNode;
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  onConfirm: () => void;
  onCancel?: () => void;
  closeOnClickOutside?: boolean;
  confirmLabel?: string;
  confirmIsDisabled?: boolean;
  cancelLabel?: string;
  hideCancelButton?: boolean;
  hideHeadingIcon?: boolean;
  className?: string;
  style?: CSSProperties;
  isOpaque?: boolean;
  variant?: `${ConfirmationModalVariant}`;
}

export const ConfirmationModal = ({
  heading,
  headingOverride,
  message,
  isOpen = true,
  onOpenChange,
  closeOnClickOutside = false,
  className = "",
  confirmLabel = "Confirm",
  confirmIsDisabled = false,
  cancelLabel = "Cancel",
  variant = ConfirmationModalVariant.DEFAULT,
  hideCancelButton = false,
  hideHeadingIcon = false,
  onConfirm,
  onCancel,
  style = {},
  isOpaque = false,
  children
}: PropsWithChildren<ConfirmationModalProps>) => {
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    function handleKeyDown(e: KeyboardEvent) {
      if (!hideCancelButton && e.key === "Escape") {
        onOpenChange?.(false);
        onCancel?.();
      } else if (e.key === "Enter") {
        onConfirm();
        onOpenChange?.(false);
      }
    }

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [hideCancelButton, isOpen, onCancel, onConfirm, onOpenChange]);

  const icon = useMemo(() => {
    if (headingOverride || hideHeadingIcon) {
      return undefined;
    }
    switch (variant) {
      case ConfirmationModalVariant.DEFAULT:
        return (
          <Icon
            name="help"
            size="l"
            color="text-secondary"
            fillStyle="filled"
          />
        );
      case ConfirmationModalVariant.DANGER:
        return (
          <Icon
            name="warning"
            size="l"
            color="traffic-danger"
            fillStyle="filled"
          />
        );
      case ConfirmationModalVariant.WARNING:
        return (
          <Icon
            name="warning"
            size="l"
            color="traffic-warning"
            fillStyle="filled"
          />
        );
      case ConfirmationModalVariant.INFO:
        return (
          <Icon name="info" size="l" color="traffic-info" fillStyle="filled" />
        );
      case ConfirmationModalVariant.SUCCESS:
        return (
          <Icon
            name="check_circle"
            size="l"
            color="traffic-success"
            fillStyle="filled"
          />
        );
    }
  }, [headingOverride, hideHeadingIcon, variant]);

  const headingContent = useMemo(() => {
    if (headingOverride) {
      return headingOverride;
    }
    return (
      <Inline width="100" alignment="top-left">
        <Inline alignment="left" gap="050">
          {icon}
          <Heading
            color={
              variant === ConfirmationModalVariant.DEFAULT
                ? undefined
                : (`traffic-on${variant?.charAt(0).toUpperCase()}${variant?.slice(1)}` as HeadingColor)
            }
            size="m"
          >
            {heading}
          </Heading>
        </Inline>
      </Inline>
    );
  }, [heading, headingOverride, icon, variant]);

  const footerContent = useMemo(() => {
    return (
      <Inline width="100" alignment="right">
        <ButtonGroup className="confirmation-modal__footer" alignment="right">
          {!hideCancelButton && (
            <Button
              label={cancelLabel}
              variant="text"
              onClick={() => {
                onCancel?.();
                onOpenChange?.(false);
              }}
            />
          )}
          <Button
            label={confirmLabel}
            variant="primary"
            traffic={
              variant === ConfirmationModalVariant.DEFAULT ? undefined : variant
            }
            onClick={() => {
              onConfirm();
              onOpenChange?.(false);
            }}
            disabled={confirmIsDisabled}
          />
        </ButtonGroup>
      </Inline>
    );
  }, [
    cancelLabel,
    confirmIsDisabled,
    confirmLabel,
    hideCancelButton,
    onCancel,
    onConfirm,
    onOpenChange,
    variant
  ]);

  return (
    <Modal
      className={getClassNames([className, "confirmation-modal"])}
      headingOverride={headingContent}
      footer={footerContent}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      closeOnClickOutside={closeOnClickOutside}
      style={style}
      isOpaque={isOpaque}
    >
      <Stack className="confirmation-modal__content" gap="150">
        {typeof message === "string" ? <Text>{message}</Text> : message}
        {children}
      </Stack>
    </Modal>
  );
};

ConfirmationModal.displayName = "ConfirmationModal";
