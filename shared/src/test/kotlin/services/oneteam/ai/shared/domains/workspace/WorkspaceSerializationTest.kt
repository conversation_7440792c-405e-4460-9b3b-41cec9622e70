package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.nulls.shouldNotBeNull
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.workspace.BaseSection.*
import services.oneteam.ai.shared.domains.workspace.CommonQuestionProperties.TableQuestionProperties
import services.oneteam.ai.shared.domains.workspace.CommonQuestionProperties.TextQuestionProperties
import services.oneteam.ai.shared.domains.workspace.validation.*
import services.oneteam.ai.shared.otSerializationModule
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WorkspaceSerializationTest {
    private val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun getJson(): Json {
        return Json(builderAction = {
            serializersModule = otSerializationModule
            classDiscriminator = "anything but \"type\""
            prettyPrint = true
            encodeDefaults = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }

    fun jsonProvider(): Stream<String> {
        return Stream.of(
            *QuestionType.entries.map {
                this::class.java.getResource("/questions/${it.name.lowercase()}.json")!!.readText()
            }.toTypedArray()
        )
    }

    fun questionProvider(): Stream<BaseSection> {
        return Stream.of(
            Questions.text(),
            Questions.table(),
            Questions.select(),
            Questions.multiSelect(),
            Questions.number(),
            Questions.boolean(),
            Questions.files(),
            Questions.json(),
            Questions.schema()
//            Questions.date()
        )
    }

    @ParameterizedTest
    @MethodSource("jsonProvider")
    fun `test deserialization`(json: String) {
        val question = getJson().decodeFromString(BaseSection.serializer(), json)
        logger.debug(question.toString())
        assertNotNull(question)
    }

    @ParameterizedTest
    @MethodSource("questionProvider")
    fun `test serialization`(question: BaseSection) {
        val data = getJson().encodeToString(BaseSection.serializer(), question)
        logger.debug(data)
        assertNotNull(data)
    }

    @Test
    fun `json should deserialize`() {

        val workspaceForJson = getJson().decodeFromString<Workspace.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )
        logger.debug(workspaceForJson.toString())
        assertNotNull(workspaceForJson)
        for ((_, form) in workspaceForJson.forms) {
            assertNotNull(form)
            form.content.shouldNotBeNull()
            form.content!!.size.shouldBeGreaterThan(0)
        }
    }

    @Test
    fun `json should serialize`() {

        val section = Section(
            id = Id("1"), name = Name("name"), level = Level(0), content = listOf(Questions.text(), Questions.table())
        )

        val workspaceForJson = Workspace.ForJson(
            id = Workspace.Id(0),
            name = Workspace.Name(""),
            key = Workspace.Key(""),
            description = Workspace.Description(""),
            foundations = OrderedMap(listOf()),
            forms = mapOf(
                Pair<FormConfiguration.Id, FormConfiguration.ForJson>(
                    FormConfiguration.Id("1"), FormConfiguration.ForJson(
                        id = "1",
                        name = "Form 1",
                        description = "description",
                        key = "key",
                        foundationId = "1",
                        seriesId = "1",
                        level = 0,
                        metadata = EntityMetadata(Instant.now(), Instant.now()),
                        content = listOf(section) as List<BaseSection>?
                    )
                )
            ),
            flows = OrderedMap(emptyList()),
            series = emptyMap(),
            labels = emptyMap(),
            metadata = EntityMetadata(Instant.now(), Instant.now()),
            errors = listOf(
                ConstraintError(
                    key = Field("key"),
                    type = Type("type"),
                    path = Path("path"),
                    constraintDetail = ConstraintDetail("detail"),
                    message = Message("error")
                )
            )
        )

        val data = getJson().encodeToString(Workspace.ForJson.serializer(), workspaceForJson)
        logger.debug(data)
    }

    @Test
    fun `json should serialize and deserialize`() {

        val section = Section(
            id = Id("1"), name = Name("name"), level = Level(0), content = listOf(
                Questions.text(),
                Questions.table(),
                Questions.select(),
                Questions.multiSelect(),
                Questions.number(),
                Questions.boolean(),
                Questions.files()
            )
        )

        val workspaceForJson = Workspace.ForJson(
            id = Workspace.Id(0),
            name = Workspace.Name(""),
            key = Workspace.Key(""),
            description = Workspace.Description(""),
            foundations = OrderedMap(listOf()),
            forms = mapOf(
                Pair<FormConfiguration.Id, FormConfiguration.ForJson>(
                    FormConfiguration.Id("1"), FormConfiguration.ForJson(
                        id = "1",
                        name = "Form 1",
                        description = "description",
                        key = "key",
                        foundationId = "1",
                        seriesId = "1",
                        level = 0,
                        metadata = EntityMetadata(Instant.now(), Instant.now()),
                        content = listOf(section) as List<BaseSection>?
                    )
                )
            ),
            flows = OrderedMap(emptyList()),
            series = emptyMap(),
            labels = emptyMap(),
            metadata = EntityMetadata(Instant.now(), Instant.now()),
            errors = emptyList()
        )

        val serialized = getJson().encodeToString(Workspace.ForJson.serializer(), workspaceForJson)
        logger.debug(serialized)

        val deserialized = getJson().decodeFromString(Workspace.ForJson.serializer(), serialized)
        logger.debug(deserialized.toString())
    }


    class Questions {
        companion object {
            fun text(): BaseSection {
                return TextQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "text",
                    properties = TextQuestionProperties(
                        required = true
                    )
                )
            }

            fun boolean(): BaseSection {
                return BooleanQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "text",
                    properties = CommonQuestionProperties.BooleanQuestionProperties(
                        required = true
                    )
                )
            }

            fun number(): BaseSection {
                return NumberQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "text",
                    properties = CommonQuestionProperties.NumberQuestionProperties(
                        required = true
                    )
                )
            }

            fun select(): BaseSection {
                return SelectQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "text",
                    properties = CommonQuestionProperties.SelectQuestionProperties(
                        required = true,
                        placeholder = "placeholder",
                        isMultiSelect = false,
                        allowReuseAcrossForms = true,
                        options = listOf(
                            CommonQuestionProperties.SelectOption(
                                value = "1", label = "label"
                            )
                        )
                    )
                )
            }

            fun multiSelect(): BaseSection {
                return MultiSelectQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "text",
                    properties = CommonQuestionProperties.MultiSelectQuestionProperties(
                        required = true,
                        isMultiSelect = true,
                        placeholder = "placeholder",
                        allowReuseAcrossForms = true,
                        defaultValue = listOf(
                            "label"
                        ),
                        options = listOf(
                            CommonQuestionProperties.SelectOption(
                                value = "1", label = "label"
                            )
                        )
                    )
                )
            }

            fun files(): BaseSection {
                return FilesQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "files",
                    properties = CommonQuestionProperties.FilesQuestionProperties(
                        required = true, min = 1, max = 5, maxFileSizeMB = 70
                    )
                )
            }

            fun table(): BaseSection {
                return TableQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "text",
                    properties = TableQuestionProperties(
                        required = true, columns = listOf(
                            TextQuestion(
                                id = Id("11"),
                                description = "description",
                                identifier = "identifier",
                                text = "text",
                                properties = TextQuestionProperties(
                                    required = true
                                )
                            )
                        )
                    )
                )
            }

            fun json(): BaseSection {
                return JsonQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "text",
                    properties = CommonQuestionProperties.JsonQuestionProperties(
                        required = true, items = listOf(
                            TextQuestion(
                                id = Id("11"),
                                description = "description",
                                identifier = "identifier",
                                text = "text",
                                properties = TextQuestionProperties(
                                    required = true
                                )
                            )
                        )
                    )
                )
            }

            fun schema(): BaseSection {
                return SchemaQuestion(
                    id = Id("1"),
                    description = "description",
                    identifier = "identifier",
                    text = "text",
                    properties = CommonQuestionProperties.SchemaQuestionProperties(
                        required = true,
                    )
                )
            }
        }
    }

}