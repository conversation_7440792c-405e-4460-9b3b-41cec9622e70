@mixin button_traffic($variant, $traffic) {
  border-color: var(
    --components-button-#{$variant}-#{$traffic}-color-border,
    #ff1067
  );
  background-color: var(
    --components-button-#{$variant}-#{$traffic}-color-background,
    #ff1067
  );
  color: var(--components-button-#{$variant}-#{$traffic}-color-text, #fff);
}

@mixin button_disabled($variant: "primary") {
  border-color: var(
    --components-button-#{$variant}-disabled-color-border,
    #cdd4e3
  );
  background: var(
    --components-button-#{$variant}-disabled-color-background,
    #cdd4e3
  );
  color: var(--components-button-#{$variant}-disabled-color-text, #fff);
}

@mixin button_variant($variant) {
  border-color: var(--components-button-#{$variant}-color-border, #021429);
  background: var(--components-button-#{$variant}-color-background, #021429);
  color: var(--components-button-#{$variant}-color-text, #fff);

  &:hover {
    border-color: var(
      --components-button-#{$variant}-hover-color-border,
      #485d74
    );
    background: var(
      --components-button-#{$variant}-hover-color-background,
      #485d74
    );
    color: var(--components-button-#{$variant}-hover-color-text, #fff);
  }

  &.button--traffic {
    &-danger {
      @include button_traffic($variant, "danger");
    }
    &-warning {
      @include button_traffic($variant, "warning");
    }
    &-success {
      @include button_traffic($variant, "success");
    }
    &-info {
      @include button_traffic($variant, "info");
    }
  }

  &:disabled {
    @include button_disabled();
  }
}

.button {
  border: none;
  cursor: pointer;
  transition: all 0.1s;
  max-width: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--components-button-gap, 2px);
  border-width: var(--components-button-border-width, 1px);
  border-style: solid;
  outline-offset: 2px;

  & label {
    cursor: pointer;
    font-style: normal;
    font-weight: var(--components-button-font-weight, 500);
    font-size: var(--components-button-font-size, 14px);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.25;
  }

  &:disabled {
    cursor: not-allowed;
    & label {
      cursor: not-allowed;
    }
  }

  &:focus {
    outline: 1px solid var(--components-inputs-focus-color-border, #0083ff);
  }

  &--size {
    &-default {
      min-height: var(--components-button-height, 32px);
      height: var(--components-button-height, 32px);
      padding: 0px var(--components-button-padding-horizontal, 12px);
      border-radius: var(--components-button-border-radius, 12px);
    }
    &-small {
      gap: 1px;
      min-height: var(--components-button-small-height, 24px);
      height: var(--components-button-small-height, 24px);
      padding: 0px var(--components-button-small-padding-horizontal, 8px);
      border-radius: var(--components-button-small-border-radius, 8px);

      & label {
        font-size: var(--components-button-small-font-size, 12px);
      }
    }
  }

  &--variant {
    &-primary {
      @include button_variant("primary");
    }
    &-secondary {
      @include button_variant("secondary");
    }
    &-text {
      background-color: transparent !important;
      border-color: transparent !important;
      border-radius: var(--border-radius-rounded-slightly, 4px);
      min-height: auto;

      height: auto;
      padding: 0;
      text-decoration: underline;
      @include button_variant("text");
      &:hover,
      &:disabled {
        background-color: transparent;
        border-color: transparent;
      }
    }
  }

  &--width {
    &-fit {
      width: fit-content;
    }
    &-100 {
      width: 100%;
    }
    &-auto {
      width: auto;
    }
  }
}
