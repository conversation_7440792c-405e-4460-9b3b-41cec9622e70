import React, { useMemo } from "react";

import { Alert, Form } from "@oneteam/onetheme";

import { internalOtaiFormField } from "@helpers/otaiFormHelper";

import { InternalOTAIFormFieldQuestion } from "@components/shared/OTAIForm/OTAIFormType";

import { ConfigurationFlowMode } from "@pages/configuration/ConfigurationFlowMode";
import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { CustomPropertyField } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowStepModal/ConfigurationFlowBlockFields/CustomPropertyField";

import { useDictionary } from "@src/hooks/useDictionary";
import { MockFlowContext } from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { CommonTypedStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { FlowStepTypeConfiguration } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";

import { useFlowExecutionContext } from "../../FlowExecutionContext";

export const TriggerFields = ({
  stepTypeConfig,
  configurationStep,
  flowContext
}: {
  configurationStep: FlowStep;
  stepTypeConfig?: FlowStepTypeConfiguration;
  flowContext?: MockFlowContext;
}) => {
  const d = useDictionary();
  const { pathToCurrentExecution } = useFlowExecutionContext();
  const { configurationFlowMode } = useConfigurationFlowContext();

  const stepTypeContentQuestions = useMemo(() => {
    if (
      !stepTypeConfig?.properties.configuration.content?.length ||
      !configurationStep
    ) {
      return undefined;
    }

    return stepTypeConfig.properties.configuration.content.map(q => {
      const question = internalOtaiFormField(
        q as InternalOTAIFormFieldQuestion
      );

      const stepProperties =
        configurationStep.properties as CommonTypedStepProperties;
      const answer = stepProperties?.inputs?.[q.identifier];
      return {
        ...question,
        answer: answer as string
      };
    });
  }, [stepTypeConfig, configurationStep]);

  if ((pathToCurrentExecution ?? []).length > 0 || !stepTypeContentQuestions) {
    return (
      <Alert>
        {d("ui.collection.flowExecution.stepModal.trigger.subFlowMessage")}
      </Alert>
    );
  }

  return (
    <Form d={d}>
      {stepTypeContentQuestions?.map(question => (
        <CustomPropertyField
          key={question.identifier}
          question={question}
          selectedStep={configurationStep}
          handleSaveInput={() => {}}
          mockFlowContext={flowContext}
          pathInDocument={[]}
          onlyTriggerChangeWhenBlur
          disabled={configurationFlowMode !== ConfigurationFlowMode.EDIT}
        />
      ))}
    </Form>
  );
};
