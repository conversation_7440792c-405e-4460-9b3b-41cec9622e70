import React from "react";

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Box } from "../../fermions/index.ts";
import { Chart } from "./Chart.tsx";
import { ChartSubType, ChartType } from "./ChartTypes.ts";

const meta: Meta<typeof Chart> = {
  component: Chart,
  title: "components/Chart",
  argTypes: {}
};

export default meta;

type Story = StoryObj<typeof Chart>;

const mockData = [
  {
    entity: "entity_1",
    "opening Balance": "5000",
    "closing Balance": "1000",
    "expense A": "3000",
    "expense B": "1000"
  },
  {
    entity: "entity_2",
    "opening Balance": "10000",
    "closing Balance": "7000",
    "expense A": "2000",
    "expense B": "1000"
  },
  {
    entity: "entity_3",
    "opening Balance": "8000",
    "closing Balance": "1000",
    "expense A": "4000",
    "expense B": "3000"
  }
];

const defaultRender = (args: Story["args"]) => {
  const config = args?.config || {
    type: ChartType.LINE,
    subType: ChartSubType.BASIC_LINE,
    xAxis: ["entity"],
    series: ["openingBalance"],
    swapColumns: false
  };

  const advancedOptions = {
    ...args?.advancedOptions,
    legend: {
      bottom: "0",
      top: null
    }
  };

  return (
    <Box style={{ width: "500px", height: "400px", margin: "auto" }}>
      <Chart
        title={args?.title}
        data={args?.data || mockData}
        config={config}
        advancedOptions={advancedOptions}
        width="100"
        height="100"
      />
    </Box>
  );
};

export const BasicLineChart: Story = {
  args: {
    title: "Basic Line Chart",
    data: mockData,
    config: {
      type: ChartType.LINE,
      subType: ChartSubType.BASIC_LINE,
      xAxis: ["entity"],
      series: ["openingBalance"],
      swapColumns: false
    }
  },
  render: defaultRender
};

export const SwapLineColumnsAndRowsChart: Story = {
  args: {
    title: "Swap Line Columns/Rows Chart",
    data: mockData,
    config: {
      type: ChartType.LINE,
      xAxis: ["opening Balance", "closing Balance"],
      series: ["entity"],
      swapColumns: true
    }
  },
  render: defaultRender
};

export const SmoothedLineChart: Story = {
  args: {
    title: "Smooth Line Chart",
    data: mockData,
    config: {
      type: ChartType.LINE,
      subType: ChartSubType.SMOOTH_LINE,
      xAxis: ["entity"],
      series: ["openingBalance"],
      swapColumns: false,
      showToolbox: true
    }
  },
  render: defaultRender
};

export const StackedLineChart: Story = {
  args: {
    title: "Stacked Line Chart",
    data: mockData,
    config: {
      type: ChartType.LINE,
      subType: ChartSubType.STACKED_LINE,
      xAxis: ["entity"],
      series: ["openingBalance", "closingBalance"],
      swapColumns: false
    }
  },
  render: defaultRender
};

export const StackedAreaChart: Story = {
  args: {
    title: "Stacked Area Chart",
    data: mockData,
    config: {
      type: ChartType.LINE,
      subType: ChartSubType.STACKED_AREA_LINE,
      xAxis: ["entity"],
      series: ["openingBalance", "closingBalance"],
      swapColumns: false
    }
  },
  render: defaultRender
};

export const BasicBarChart: Story = {
  args: {
    title: "Basic Bar Chart",
    data: mockData,
    config: {
      type: ChartType.BAR,
      subType: ChartSubType.BASIC_BAR,
      xAxis: ["entity"],
      series: ["openingBalance", "closingBalance"],
      swapColumns: false
    }
  },
  render: defaultRender
};

export const StackedBarChart: Story = {
  args: {
    title: "Stacked Bar Chart",
    data: mockData,
    config: {
      type: ChartType.BAR,
      subType: ChartSubType.STACKED_BAR,
      xAxis: ["entity"],
      series: ["openingBalance", "closingBalance"],
      swapColumns: false
    }
  },
  render: defaultRender
};

export const BasicPieChart: Story = {
  args: {
    title: "Basic Pie Chart",
    data: mockData,
    config: {
      type: ChartType.PIE,
      subType: ChartSubType.BASIC_PIE,
      groupBy: ["entity"],
      series: "openingBalance",
      swapColumns: false
    }
  },
  render: defaultRender
};

export const DonutPieChart: Story = {
  args: {
    title: "Donut Pie Chart",
    data: mockData,
    config: {
      type: ChartType.PIE,
      subType: ChartSubType.DONUT_PIE,
      groupBy: ["entity"],
      series: "openingBalance",
      swapColumns: false
    }
  },
  render: defaultRender
};

export const SwapPieColumnsAndRowsChart: Story = {
  args: {
    title: "Swap Pie Columns/Rows Chart",
    data: mockData,
    config: {
      type: ChartType.PIE,
      subType: ChartSubType.BASIC_PIE,
      groupBy: ["openingBalance", "closingBalance", "expenseA", "expenseB"],
      series: "entity",
      swapColumns: true,
      rowIndex: 1
    }
  },
  render: defaultRender
};

const LargeMockData = Array.from({ length: 1000 }, (_, i) => {
  const openingBalance = Math.floor(Math.random() * (20000 - 1000 + 1)) + 1000;
  const expenseA =
    Math.floor(Math.random() * (openingBalance / 2 - 500 + 1)) + 500;
  const maxExpenseB = Math.max(500, openingBalance - expenseA);
  const expenseB = Math.floor(Math.random() * (maxExpenseB - 500 + 1)) + 500;
  const closingBalance = openingBalance - expenseA - expenseB;

  return {
    entity: `entity_${i + 1}`,
    openingBalance: openingBalance.toString(),
    closingBalance: closingBalance.toString(),
    expenseA: expenseA.toString(),
    expenseB: expenseB.toString()
  };
});

export const LargeBasicLineChart: Story = {
  args: {
    title: "Large Basic Line Chart",
    data: LargeMockData,
    config: {
      type: ChartType.LINE,
      subType: ChartSubType.BASIC_LINE,
      xAxis: ["entity"],
      series: ["openingBalance"],
      swapColumns: false
    },
    advancedOptions: {
      dataZoom: [
        {
          type: "slider",
          show: true,
          start: 1,
          end: 10,
          handleSize: 8
        },
        {
          type: "inside",
          start: 1,
          end: 10
        }
      ]
    }
  },
  render: defaultRender
};
