import { beforeEach, describe, expect, it } from "vitest";

import {
  FoundationConfiguration,
  FoundationRelationship
} from "../types/FoundationConfiguration";
import { Resource } from "../types/documentTypes";
import {
  addItem,
  addItemAfterId,
  addItemFirst,
  getResourceAtIndex,
  getResourceEntitiesInOrder,
  getResourceLength,
  mapOverResource,
  moveItemWithIdToIndex,
  removeItem,
  setItemField
} from "./OrderedMapNoState";
import { customNanoId } from "./customNanoIdHelper";

const originalFoundationsDocument: Resource<FoundationConfiguration> = {
  order: ["foundation-0", "foundation-1", "foundation-2"],
  entities: {
    "foundation-0": {
      id: "foundation-0",
      name: "Workspace",
      identifier: "Workspace",
      description: "Workspace Foundation Level",
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: "2024-11-21T05:04:48.893392Z",
        updatedAt: "2024-11-21T05:04:48.893392Z"
      }
    },
    "foundation-1": {
      id: "foundation-1",
      name: "Company",
      identifier: "Company",
      description: "Company Foundation Level",
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: "2024-11-21T05:04:48.857291Z",
        updatedAt: "2024-11-21T05:04:48.857291Z"
      }
    },
    "foundation-2": {
      id: "foundation-2",
      name: "Employee",
      identifier: "Employee",
      description: "Employee Foundation Level",
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: "2024-11-21T05:04:48.858884Z",
        updatedAt: "2024-11-21T05:04:48.858884Z"
      }
    }
  }
};

let foundationsDocument: Resource<FoundationConfiguration>;

describe("OrderedMapNoState", () => {
  beforeEach(() => {
    foundationsDocument = structuredClone(originalFoundationsDocument);
  });

  it("should add an item to the resource", () => {
    const newItem = {
      id: customNanoId(),
      name: "New Foundation",
      identifier: "NewFoundation",
      description: "New Foundation Description",
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
    addItem<FoundationConfiguration>(newItem, foundationsDocument);
    expect(foundationsDocument.entities[newItem.id]).toEqual(newItem);
    expect(foundationsDocument.order.includes(newItem.id)).toBe(true);
  });

  it("should add an item first in the resource", () => {
    const newItem = {
      id: customNanoId(),
      name: "New Foundation",
      identifier: "NewFoundation",
      description: "New Foundation Description",
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
    addItemFirst<FoundationConfiguration>(newItem, foundationsDocument);
    expect(foundationsDocument.entities[newItem.id]).toEqual(newItem);
    expect(foundationsDocument.order[0]).toBe(newItem.id);
  });

  it("should edit first foundation keeping same order", () => {
    const newItem: FoundationConfiguration = {
      id: "foundation-0",
      name: "New Foundation",
      identifier: "NewFoundation",
      description: null,
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: "2024-11-21T05:04:48.893392Z",
        updatedAt: "2024-11-21T05:04:48.893392Z"
      }
    };
    addItemFirst(newItem, foundationsDocument);
    expect(foundationsDocument.order).toEqual(
      originalFoundationsDocument.order
    );
    expect(foundationsDocument.entities["foundation-0"]).toEqual(newItem);
  });

  it("should add an item after a specific id in the resource", () => {
    const newItem = {
      id: customNanoId(),
      name: "New Foundation",
      identifier: "NewFoundation",
      description: "New Foundation Description",
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
    addItemAfterId<FoundationConfiguration>(
      "foundation-1",
      newItem,
      foundationsDocument
    );
    const index = foundationsDocument.order.indexOf("foundation-1");
    expect(foundationsDocument.entities[newItem.id]).toEqual(newItem);
    expect(foundationsDocument.order[index + 1]).toBe(newItem.id);
  });

  it("should not edit a foundation after a given ID", () => {
    const newFoundation: FoundationConfiguration = {
      id: "foundation-0",
      name: "New Foundation",
      identifier: "NewFoundation",
      description: null,
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: "2024-11-21T05:04:48.893392Z",
        updatedAt: "2024-11-21T05:04:48.893392Z"
      }
    };
    addItemAfterId("foundation-1", newFoundation, foundationsDocument);
    expect(foundationsDocument.order).toEqual(
      originalFoundationsDocument.order
    );
    expect(foundationsDocument.entities).toEqual(
      originalFoundationsDocument.entities
    );
  });

  it("should not add a foundation after a given ID that doesn't exist", () => {
    const newFoundation: FoundationConfiguration = {
      id: "new-id",
      name: "New Foundation",
      identifier: "NewFoundation",
      description: null,
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: "2024-11-21T05:04:48.893392Z",
        updatedAt: "2024-11-21T05:04:48.893392Z"
      }
    };
    addItemAfterId("non-existent-id", newFoundation, foundationsDocument);
    expect(foundationsDocument.order).toEqual(
      originalFoundationsDocument.order
    );
    expect(foundationsDocument.entities).toEqual(
      originalFoundationsDocument.entities
    );
  });

  it("should add a foundation at the end", () => {
    const newFoundation: FoundationConfiguration = {
      id: "new-id",
      name: "New Foundation",
      identifier: "NewFoundation",
      description: null,
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: "2024-11-21T05:04:48.893392Z",
        updatedAt: "2024-11-21T05:04:48.893392Z"
      }
    };
    addItem(newFoundation, foundationsDocument);
    expect(
      foundationsDocument.order[foundationsDocument.order.length - 1]
    ).toBe("new-id");
    expect(foundationsDocument.entities["new-id"]).toEqual(newFoundation);
  });

  it("should edit a foundation at the end", () => {
    const newFoundation: FoundationConfiguration = {
      id: "foundation-2",
      name: "New Foundation",
      identifier: "NewFoundation",
      description: null,
      relationship: FoundationRelationship.ONE_TO_MANY,
      metadata: {
        createdAt: "2024-11-21T05:04:48.893392Z",
        updatedAt: "2024-11-21T05:04:48.893392Z"
      }
    };
    addItem(newFoundation, foundationsDocument);
    expect(foundationsDocument.order).toEqual(
      originalFoundationsDocument.order
    );
    expect(foundationsDocument.entities["foundation-2"]).toEqual(newFoundation);
  });

  it("should remove an item from the resource", () => {
    removeItem("foundation-1", foundationsDocument);
    expect(foundationsDocument.entities["foundation-1"]).toBeUndefined();
    expect(foundationsDocument.order.includes("foundation-1")).toBe(false);
  });

  it("should not remove a foundation by ID that doesn't exist", () => {
    removeItem("non-existent-id", foundationsDocument);
    expect(foundationsDocument.order).toEqual(
      originalFoundationsDocument.order
    );
    expect(foundationsDocument.entities).toEqual(
      originalFoundationsDocument.entities
    );
  });

  it("should move a foundation to a specified index before itself", () => {
    moveItemWithIdToIndex("foundation-1", 0, foundationsDocument);
    expect(foundationsDocument.order).toEqual([
      "foundation-1",
      "foundation-0",
      "foundation-2"
    ]);
  });

  it("should move a foundation to a specified index after itself", () => {
    moveItemWithIdToIndex("foundation-0", 2, foundationsDocument);
    expect(foundationsDocument.order).toEqual([
      "foundation-1",
      "foundation-2",
      "foundation-0"
    ]);
  });

  it("should not move a foundation to a specified index that doesn't exist", () => {
    moveItemWithIdToIndex("foundation-0", 3, foundationsDocument);
    expect(foundationsDocument.order).toEqual(
      originalFoundationsDocument.order
    );
  });

  it("should not move a non-existent foundation to a specified index", () => {
    moveItemWithIdToIndex("non-existent-id", 0, foundationsDocument);
    expect(foundationsDocument.order).toEqual(
      originalFoundationsDocument.order
    );
    expect(foundationsDocument.entities).toEqual(
      originalFoundationsDocument.entities
    );
  });

  it("should set a field of an item in the resource", () => {
    setItemField(
      "foundation-0",
      "name",
      "Updated Workspace",
      foundationsDocument
    );
    expect(foundationsDocument.entities["foundation-0"].name).toBe(
      "Updated Workspace"
    );
  });

  it("should get the length of the resource", () => {
    const length = getResourceLength(foundationsDocument);
    expect(length).toBe(3);
  });

  it("should get an item at a specific index in the resource", () => {
    expect(getResourceAtIndex(foundationsDocument, 0)?.id).toBe("foundation-0");
    expect(getResourceAtIndex(foundationsDocument, 1)?.id).toBe("foundation-1");
    expect(getResourceAtIndex(foundationsDocument, 2)?.id).toBe("foundation-2");
    expect(getResourceAtIndex(foundationsDocument, 3)).toBeUndefined();
  });

  it("should map over the resource entities", () => {
    const names = mapOverResource(foundationsDocument, entity => entity.name);
    expect(names).toEqual(["Workspace", "Company", "Employee"]);
  });

  it("should get resource entities in order", () => {
    const entitiesInOrder = getResourceEntitiesInOrder(foundationsDocument);
    expect(entitiesInOrder.map(entity => entity.id)).toEqual([
      "foundation-0",
      "foundation-1",
      "foundation-2"
    ]);
  });
});
