import React from "react";

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { z } from "zod";

import { Box, Inline, Stack } from "../../fermions";
import { Dictionary } from "../../helpers/Dictionary.ts";
import { Heading } from "../Heading/Heading.tsx";
import { NumberType } from "../NumberField";
import { Form } from "./Form.tsx";

const meta: Meta<typeof Form> = {
  component: Form,
  title: "components/Form",
  argTypes: {}
};

export default meta;

type Story = StoryObj<typeof Form>;

const defaultArgs: Story["args"] = {
  children: "Form"
};

const dictionary: Record<string, string> = {
  "ui.forms.submit.label": "Submit",
  "ui.forms.cancel.label": "Cancel",
  "ui.demo.uid": "UID",
  "ui.demo.description": "Description",
  "ui.demo.amount": "Amount",
  "ui.demo.date": "Date",
  "ui.demo.dateRange": "Date Range",
  "ui.demo.color": "Color",
  "ui.demo.colorGroup": "Color Group",
  "ui.demo.flag": "Flag",
  "ui.forms.errors.string.insufficientLength": "Insufficient length: 10",
  "ui.forms.errors.regex.hexRegex": "Must be a valid hex value",
  "ui.demo.type": "Type",
  "ui.demo.user": "User",
  "ui.demo.question": "Question",
  "ui.demo.boolean": "Boolean",
  "ui.demo.checkboxGroup": "Checkbox Group"
};

const d: Dictionary = (a: string): string => {
  return dictionary[a] ?? a;
};

const demoSchema = z.object({
  name: z.string(),
  UID: z
    .string()
    .length(10, {
      message: d("ui.forms.errors.string.insufficientLength", {
        length: 10
      })
    })
    .regex(/^[a-fA-F0-9]+$/, {
      message: d("ui.forms.errors.regex.hexRegex")
    })
    .transform(val => val.toUpperCase()),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9a-fA-F]{6}$/, {
    message: "Must be a valid hex value"
  }),
  colorGroup: z.string().optional(),
  amount: z.coerce
    .number()
    .nonnegative()
    .lt(100.5, { message: "too big" })
    .refine(data => data < 100.5, { message: "too big" }),
  date: z
    .string()
    .date()
    .refine(
      (data: string) => {
        return new Date(data) > new Date();
      },
      {
        message: "Start date must be in the future"
      }
    ),
  dateRange: z.object({
    from: z
      .string()
      .date()
      .refine(
        (data: string) => {
          return new Date(data) > new Date();
        },
        {
          message: "Start date must be in the future"
        }
      ),
    to: z.string().date()
  }),
  flag: z.boolean(),
  type: z.enum(["A", "B", "C"]),
  user: z
    .array(z.enum(["A", "B", "C", "D", "E"]))
    .min(1, { message: "Must select at least one user" }),
  status: z.enum(["draft", "inProgress", "done"]),
  question: z.enum(["yes", "maybe", "no"]).optional(),
  boolean: z.boolean().optional(),
  checkboxGroup: z.array(z.enum(["apple", "pear", "orange"])).optional()
});

type InferredType = z.infer<typeof demoSchema>;

type DemoFormFieldType = Omit<InferredType, "amount"> & {
  amount: string | number;
};

const demoDefaultValues: DemoFormFieldType = {
  name: "Form name",
  description: "This is a description",
  UID: "1234567890",
  amount: "100.45",
  date: "2021-01-01",
  color: "#FF0000",
  colorGroup: undefined,
  flag: true,
  type: "A",
  user: ["A", "B"],
  status: "draft",
  dateRange: {
    from: "2021-01-01",
    to: "2021-01-31"
  }
};

const defaultRender = () => {
  const DemoFormFields = () => {
    return (
      <Stack gap="150" width="100" contentsWidth="100">
        <Heading>
          <Form.Renamable name="name" required minLength={3} maxLength={10} />
        </Heading>
        <Form.PillSelect
          name="status"
          options={[
            { label: "Draft", value: "draft" },
            { label: "In Progress", value: "inProgress" },
            { label: "Done", value: "done" }
          ]}
          required
          handleChange={value => {
            console.log("changed pill select status to", value);
          }}
        />
        <Form.TextField
          name="UID"
          label={d("ui.demo.uid")}
          required
          minLength={3}
          maxLength={10}
          onChange={value => {
            console.log("changed text UID to", value);
          }}
        />
        <Form.TextAreaField
          name="description"
          label={d("ui.demo.description")}
          onChange={value => {
            console.log("changed text area description to", value);
          }}
        />
        <Form.ColorPicker
          name="color"
          label={d("ui.demo.color")}
          onChange={value => {
            console.log("changed color to", value);
          }}
        />
        <Form.ColorGroup
          name="colorGroup"
          label={d("ui.demo.colorGroup")}
          options={[
            { value: "red", label: "Red", color: "#ff4268" },
            { value: "purple", label: "Purple", color: "#b375ff" },
            { value: "green", label: "Green", color: "#daff75" }
          ]}
          onChange={value => {
            console.log("changed color group to", value);
          }}
        />
        <Form.NumberField
          name="amount"
          label={d("ui.demo.amount")}
          required
          type={NumberType.ACCOUNTING}
          onChange={value => {
            console.log("changed number amount to", value);
          }}
        />
        <Form.DatePicker
          name="date"
          label={d("ui.demo.date")}
          required
          onChange={value => {
            console.log("changed date to", value);
          }}
        />
        <Form.DateRangePicker
          name="dateRange"
          label={d("ui.demo.dateRange")}
          required
          onChange={value => {
            console.log("changed date range to", value);
          }}
        />
        <Form.Select
          name="type"
          label={d("ui.demo.type")}
          options={[
            { label: "A", value: "A" },
            { label: "B", value: "B" },
            { label: "C", value: "C" }
          ]}
          required
          onChange={value => {
            console.log("changed type select to", value);
          }}
        />
        <Form.MultiSelect
          name="user"
          label={d("ui.demo.user")}
          options={[
            { label: "A", value: "A" },
            { label: "B", value: "B" },
            { label: "C", value: "C" },
            { label: "D", value: "D" },
            { label: "E", value: "E" }
          ]}
          required
          onChange={value => {
            console.log("changed user multiselect to", value);
          }}
        />
        <Inline gap="200">
          <Form.Toggle
            name="flag"
            label={d("ui.demo.flag")}
            onChange={value => {
              console.log("changed flag toggle to", value);
            }}
          />
          <Form.Checkbox
            name="flag"
            label={d("ui.demo.flag")}
            onChange={value => {
              console.log("changed flag checkbox to", value);
            }}
          />
          <Form.Radio
            name="flag"
            label={d("ui.demo.flag")}
            onChange={value => {
              console.log("changed flag radio to", value);
            }}
          />
        </Inline>
        <Form.RadioGroup
          label={d("ui.demo.question")}
          name="question"
          options={[
            { label: "Yes", value: "yes" },
            { label: "Maybe", value: "maybe" },
            { label: "No", value: "no" }
          ]}
          onChange={value => {
            console.log("changed question radio group to", value);
          }}
        />
        <Form.RadioGroup
          label={d("ui.demo.boolean")}
          name="question2"
          options={[
            { label: "Yes", value: true },
            { label: "No", value: false }
          ]}
          defaultValue={false}
          onChange={value => {
            console.log("changed boolean radio group to", value);
          }}
        />
        <Form.CheckboxGroup
          label={d("ui.demo.checkboxGroup")}
          name="checkboxGroup"
          options={[
            {
              label: "Apple",
              value: "apple"
            },
            { label: "Pear", value: "pear" },
            { label: "Orange", value: "orange" }
          ]}
          onChange={value => {
            console.log("changed checkbox group  to", value);
          }}
        />
      </Stack>
    );
  };

  return (
    <Box style={{ width: "400px", height: "400px", margin: "auto" }}>
      <Form
        d={d}
        defaultValues={demoDefaultValues}
        handleSubmit={data => {
          console.log(`Submitting: ${data}`);
        }}
        handleCancel={() => {}}
        schema={demoSchema}
      >
        <DemoFormFields />
      </Form>
    </Box>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRender
};

const onBlurChangeForm = () => {
  const DemoFormFieldsWithChangeOnBlur = () => {
    return (
      <Stack gap="100" width="100" contentsWidth="100">
        <Heading>
          <Form.Renamable name="name" required minLength={3} maxLength={10} />
        </Heading>
        <Form.PillSelect
          name="status"
          options={[
            { label: "Draft", value: "draft" },
            { label: "In Progress", value: "inProgress" },
            { label: "Done", value: "done" }
          ]}
          onlyTriggerChangeWhenBlur
          handleChange={value => {
            console.log("changed pill select status to", value);
          }}
        />
        <Form.TextField
          name="UID"
          label={d("ui.demo.uid")}
          minLength={3}
          maxLength={10}
          required
          onlyTriggerChangeWhenBlur
          onChange={value => {
            console.log("changed text UID to", value);
          }}
        />
        <Form.TextAreaField
          name="description"
          label={d("ui.demo.description")}
          onlyTriggerChangeWhenBlur
          onChange={value => {
            console.log("changed text area description to", value);
          }}
        />
        <Form.ColorPicker
          name="color"
          label={d("ui.demo.color")}
          onlyTriggerChangeWhenBlur
          onChange={value => {
            console.log("changed color to", value);
          }}
        />
        <Form.NumberField
          name="amount"
          label={d("ui.demo.amount")}
          type={NumberType.ACCOUNTING}
          onlyTriggerChangeWhenBlur
          required
          onChange={value => {
            console.log("changed number amount to", value);
          }}
        />
        <Form.DatePicker
          name="date"
          label={d("ui.demo.date")}
          onlyTriggerChangeWhenBlur
          required
          onChange={value => {
            console.log("changed date to", value);
          }}
        />
        <Form.DateRangePicker
          name="dateRange"
          label={d("ui.demo.dateRange")}
          onlyTriggerChangeWhenBlur
          required
          onChange={value => {
            console.log("changed date range to", value);
          }}
        />
        <Form.Select
          name="type"
          label={d("ui.demo.type")}
          options={[
            { label: "A", value: "A" },
            { label: "B", value: "B" },
            { label: "C", value: "C" }
          ]}
          required
          onlyTriggerChangeWhenBlur
          onChange={value => {
            console.log("changed type select to", value);
          }}
        />
        <Form.MultiSelect
          name="user"
          label={d("ui.demo.user")}
          required
          options={[
            { label: "A", value: "A" },
            { label: "B", value: "B" },
            { label: "C", value: "C" },
            { label: "D", value: "D" },
            { label: "E", value: "E" }
          ]}
          onlyTriggerChangeWhenBlur
          onChange={value => {
            console.log("changed user multiselect to", value);
          }}
        />
        {/* onlyTriggerChangeWhenBlur does not apply to toggle / checkbox / radio */}
        <Inline gap="200">
          <Form.Toggle
            name="flag"
            label={d("ui.demo.flag")}
            onChange={value => {
              console.log("changed flag toggle to", value);
            }}
          />
          <Form.Checkbox
            name="flag"
            label={d("ui.demo.flag")}
            onChange={value => {
              console.log("changed flag checkbox to", value);
            }}
          />
          <Form.Radio
            name="flag"
            label={d("ui.demo.flag")}
            onChange={value => {
              console.log("changed flag radio to", value);
            }}
          />
        </Inline>
        <Form.RadioGroup
          label={d("ui.demo.question")}
          name="question"
          options={[
            { label: "Yes", value: "yes" },
            { label: "Maybe", value: "maybe" },
            { label: "No", value: "no" }
          ]}
          onChange={value => {
            console.log("changed question radio group to", value);
          }}
        />
        <Form.RadioGroup
          label={d("ui.demo.boolean")}
          name="question2"
          options={[
            { label: "Yes", value: true },
            { label: "No", value: false }
          ]}
          defaultValue={false}
          onChange={value => {
            console.log("changed boolean radio group to", value);
          }}
        />
      </Stack>
    );
  };

  return (
    <Box style={{ width: "400px", height: "400px", margin: "auto" }}>
      <Form
        d={d}
        defaultValues={demoDefaultValues}
        handleSubmit={data => {
          console.log(`Submitting: ${data}`);
        }}
        handleCancel={() => {}}
        schema={demoSchema}
      >
        <DemoFormFieldsWithChangeOnBlur />
      </Form>
    </Box>
  );
};

export const OnBlurChangeForm: Story = {
  args: defaultArgs,
  render: onBlurChangeForm
};
