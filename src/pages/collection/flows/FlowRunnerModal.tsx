import React, { useCallback, useMemo } from "react";
import { useOutletContext } from "react-router-dom";

import {
  Box,
  Floating,
  FloatingPosition,
  Modal,
  Overlay,
  SelectOptionType
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { modalZIndex } from "@helpers/modalHelper.ts";
import { internalOtaiFormFields } from "@helpers/otaiFormHelper";
import { postData } from "@helpers/postData";

import { OTAIForm } from "@components/shared/OTAIForm/OTAIForm";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { QuestionTypes } from "@src/types/Question";
import { Workspace } from "@src/types/workspace";

import "./FlowRunnerModal.scss";

export type FlowRunnerModalProps = {
  onOpenChange: () => void;
  flowOptions: SelectOptionType[];
};

export const FlowRunnerModal = ({
  onOpenChange,
  flowOptions
}: FlowRunnerModalProps) => {
  const d = useDictionary();

  const flowRunnerFields = useMemo(
    () =>
      internalOtaiFormFields([
        {
          text: d("ui.terminology.flow"),
          identifier: "configurationId",
          type: QuestionTypes.SELECT,
          properties: {
            required: true,
            options: flowOptions
          }
        }
      ]),
    [d, flowOptions]
  );

  const queryClient = useQueryClient();

  const { workspace } = useOutletContext<{ workspace: Workspace }>();

  const { mutate: runFlow } = useMutation({
    mutationFn: (data: Record<string, unknown>) => {
      return postData(`/flows/manual`, {
        flowConfigurationId: data.configurationId,
        workspaceId: workspace.id
      });
    },
    onSuccess: async response => {
      await queryClient.invalidateQueries({
        queryKey: [`/flows/manual`]
      });
      console.log("Response", response);
      onOpenChange();
    }
  });

  const handleSubmit = useCallback(
    (data: Record<string, unknown>) => {
      runFlow(data);
    },
    [runFlow]
  );

  return (
    <Box style={{ zIndex: modalZIndex }}>
      <Overlay isOpen></Overlay>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="flow-runner-modal"
          onOpenChange={onOpenChange}
          heading={d("ui.flow.runButton")}
        >
          <OTAIForm
            formWrapperProperties={{
              handleSubmit,
              handleCancel: onOpenChange,
              submitLabel: d("ui.common.run")
            }}
            content={flowRunnerFields}
          />
        </Modal>
      </Floating>
    </Box>
  );
};
