import React, {
  CSSProperties,
  useCallback,
  useEffect,
  useRef,
  useState
} from "react";
import { useForm } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import {
  <PERSON><PERSON>,
  AlertVariant,
  Breadcrumbs,
  BreadcrumbsItem,
  BreadcrumbsItemType,
  FontWeight,
  Form,
  Heading,
  HeadingSize,
  Icon,
  Inline,
  Pill,
  Stack,
  Text,
  Tooltip
} from "@oneteam/onetheme";
import { useQuery } from "@tanstack/react-query";
import { MiniMap, Panel, useReactFlow } from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { keyBy } from "lodash";

import { getData } from "@helpers/getData.ts";

import { useFlowStepHash } from "@pages/configuration/flows/hooks/useFlowStepHash.tsx";
import { useFlowVariables } from "@pages/configuration/flows/hooks/useFlowVariables.tsx";
import { useGenerateMockFlowContext } from "@pages/configuration/flows/hooks/useGenerateMockFlowContext.tsx";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  FlowConfiguration,
  FlowConfigurationStatusType
} from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  LabelColor,
  LabelConfiguration,
  labelColorToPill
} from "@src/types/Label.ts";
import { Workspace } from "@src/types/workspace.ts";

import { ConfigurationFlowMode } from "../ConfigurationFlowMode.ts";
import { useConfigurationFlowContext } from "./ConfigurationFlowContext.tsx";
import { ConfigurationFlowStepModal } from "./FlowBuilder/ConfigurationFlowStepModal/ConfigurationFlowStepModal.tsx";
import { ConfigurationFlowToolbar } from "./FlowBuilder/ConfigurationFlowToolbar/ConfigurationFlowToolbar.tsx";
import { ConfigurationFlowVariablesPanel } from "./FlowBuilder/ConfigurationFlowVariablesPanel/ConfigurationFlowVariablesPanel.tsx";
import { StepTypeModal } from "./FlowBuilder/StepTypeModal/StepTypeModal.tsx";
import { CanvasType } from "./FlowCanvas/CanvasType.ts";
import { FlowCanvas } from "./FlowCanvas/FlowCanvas.tsx";
import { FlowModeSwitcher } from "./FlowCanvas/FlowModeSwitcher.tsx";
import { ConfigurationStepNodeFactory } from "./FlowCanvas/FlowNodeFactory.tsx";
import { FlowPageZoomControls } from "./FlowCanvas/FlowPageZoomControls.tsx";

const transition = "all 0.2s";

export interface FlowProps {
  breadcrumbs: BreadcrumbsItemType[];
  configurationFlow: FlowConfiguration;
  configurationFlowLabels?: LabelConfiguration[];
}

export const Flow = ({
  breadcrumbs,
  configurationFlow,
  configurationFlowLabels
}: FlowProps) => {
  const d = useDictionary();
  const { docChange, workspace } = useOutletContext<{
    docChange: DocChange;
    workspace: Workspace;
  }>();
  const {
    setConfigurationFlow,
    setMockFlowContext,
    setMainViewportRef,
    selectedStepId,
    settings,
    setSettings,
    setFlowStepTypeConfigByPrimaryIdentifier,
    setConfigurationFlowMode
  } = useConfigurationFlowContext();
  const {
    data: flowStepTypeConfigByPrimaryIdentifier,
    isFetched: flowConfigurationsFetched
  } = useQuery({
    queryKey: ["/flowStepTypeConfiguration"],
    queryFn: () =>
      getData("/flowStepTypeConfiguration").then(res => {
        const byPrimaryIdentifier = keyBy(res, "primaryIdentifier");
        setFlowStepTypeConfigByPrimaryIdentifier(byPrimaryIdentifier);
        return byPrimaryIdentifier;
      })
  });

  const { reset } = useForm();
  useFlowStepHash();

  const [floatingLayerStyle, setFloatingLayerStyle] = useState<CSSProperties>({
    opacity: 0,
    transition
  });
  const isMounted = useRef(false);

  const mainViewport = useReactFlow();

  useEffect(() => {
    setMainViewportRef(mainViewport);
  }, [mainViewport, setMainViewportRef]);

  useEffect(() => {
    setConfigurationFlowMode(ConfigurationFlowMode.EDIT);
  }, [setConfigurationFlowMode]);

  useEffect(() => {
    isMounted.current = true;

    if (isMounted.current) {
      setTimeout(() => {
        setFloatingLayerStyle({ opacity: 1, transition });
      }, 100);
    }

    return () => {
      setFloatingLayerStyle({ opacity: 0, transition });
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    setConfigurationFlow(configurationFlow);
    reset(configurationFlow);
  }, [configurationFlow, reset, setConfigurationFlow]);

  const generatedMockFlowContext = useGenerateMockFlowContext({
    configurationFlow,
    flowStepTypeConfigByPrimaryIdentifier,
    workspace
  });

  useEffect(() => {
    setMockFlowContext(generatedMockFlowContext);
  }, [
    setMockFlowContext,
    reset,
    configurationFlow,
    flowStepTypeConfigByPrimaryIdentifier,
    workspace,
    generatedMockFlowContext
  ]);

  useFlowVariables(generatedMockFlowContext);
  const handleFlowStatusSetting = useCallback(() => {
    docChange(d => {
      d.flows.entities[configurationFlow.id].status =
        configurationFlow.status === FlowConfigurationStatusType.INACTIVE
          ? FlowConfigurationStatusType.ACTIVE
          : FlowConfigurationStatusType.INACTIVE;
    });
  }, [configurationFlow.id, configurationFlow.status, docChange]);

  const handleToggleFlowHidden = useCallback(() => {
    docChange(d => {
      const updatedHidden = !configurationFlow.properties?.hidden;
      const configurationFlowToUpdate = d.flows.entities[configurationFlow.id];
      configurationFlowToUpdate.properties ??= {};
      configurationFlowToUpdate.properties.hidden = updatedHidden;
    });
  }, [configurationFlow.id, configurationFlow.properties?.hidden, docChange]);

  const handleUpdateSetting = (key: string, value: boolean) => {
    setSettings({
      ...settings,
      [key]: value ?? false
    } as { debugMode: boolean; showMiniMap: boolean; isRenaming: boolean });
  };

  return (
    <>
      <Stack height="100" width="100" className="flow">
        <FlowCanvas
          flow={configurationFlow}
          canvasType={CanvasType.CONFIGURATION}
          flowFactory={new ConfigurationStepNodeFactory()}
          isIterator={false}
          interactive
          centreOnLoad
          spacing={{ x: 150, y: 100 }}
        >
          {settings?.showMiniMap && !selectedStepId && (
            <MiniMap
              className="flow-canvas__mini-map"
              nodeStrokeWidth={3}
              pannable
              zoomable
            />
          )}
          <Panel position="top-left" style={floatingLayerStyle}>
            <Stack gap="025" className="whiteboard-page-body-template__heading">
              {breadcrumbs?.length > 0 && (
                <Breadcrumbs>
                  {breadcrumbs?.map(breadcrumb => (
                    <BreadcrumbsItem key={breadcrumb.href} {...breadcrumb} />
                  ))}
                </Breadcrumbs>
              )}
              <Stack gap="075" alignment="left">
                <Inline
                  gap="050"
                  className="whiteboard-page-body-template__heading__content"
                  alignment="left"
                >
                  <Heading weight={FontWeight.REGULAR} size={HeadingSize.L}>
                    <Form.Renamable
                      name="name"
                      value={configurationFlow.name}
                      onChange={name => {
                        docChange(d => {
                          d.flows.entities[configurationFlow.id].name = name;
                        });
                        handleUpdateSetting("isRenaming", false);
                      }}
                      controlFocus={settings?.isRenaming}
                    />
                  </Heading>
                  {settings?.debugMode && (
                    <Text color="text-secondary">{configurationFlow.id}</Text>
                  )}
                  {/* {headingPills} */}
                  {configurationFlow.properties?.hidden && (
                    <Tooltip
                      content={d("ui.configuration.flows.hiddenTooltip")}
                    >
                      <Icon
                        name="visibility_off"
                        color="text-secondary"
                        size="l"
                      />
                    </Tooltip>
                  )}
                  {configurationFlow.status ===
                    FlowConfigurationStatusType.INACTIVE && (
                    <Pill
                      key={configurationFlow.id}
                      label={d("ui.configuration.flows.status.inactive.label")}
                      variant="danger"
                    />
                  )}
                </Inline>
                <Inline gap="050">
                  {configurationFlowLabels?.map(label => (
                    <Pill
                      key={label.id}
                      label={label.name}
                      {...labelColorToPill[
                        (label.color ?? LabelColor.COLOR_1) as LabelColor
                      ]}
                    />
                  )) ?? []}
                </Inline>
                {/* {headingFilters} */}
              </Stack>
            </Stack>
          </Panel>
          {!(configurationFlow.start && configurationFlow.start !== "") && (
            <Panel
              position="top-right"
              style={{ ...floatingLayerStyle, maxWidth: "30%", right: 0 }}
            >
              <Alert variant={AlertVariant.WARNING}>
                {d(
                  "ui.configuration.flows.step.variants.action.noActionWarning"
                )}
              </Alert>
            </Panel>
          )}
          {/* <Panel position="center-right" style={floatingLayerStyle}>
            <ConfigurationQuestionModal
              mode={"edit"}
              questionPath={`flows.entities.${configurationFlow.id}.startingVariables.0`}
              onClose={() => {}}
              // isMultiLevelQuestion={isMultiLevelQuestionPath(questionPath)}
              // onClose={() => updateQuestionHash("none")}
              // onUp={
              //   isMultiLevelQuestionPath(questionPath)
              //     ? () =>
              //         updateQuestionHash(
              //           questionPath.split(".properties").shift() ?? "none"
              //         )
              //     : undefined
              // }
            />
          </Panel> */}

          <Panel position="bottom-center" style={floatingLayerStyle}>
            <FlowModeSwitcher />
          </Panel>
          <Panel position="bottom-right" style={floatingLayerStyle}>
            <FlowPageZoomControls />
          </Panel>
          <Panel
            position="bottom-right"
            style={{ ...floatingLayerStyle, marginBottom: "80px" }}
          >
            <ConfigurationFlowStepModal docChange={docChange} />
          </Panel>
          <Panel position="bottom-left" style={floatingLayerStyle}>
            {/* TODO: construct variables with helper - inside ConfigurationFlowVariablesPanel */}
            <ConfigurationFlowVariablesPanel />
          </Panel>
          <Panel position="top-center" style={floatingLayerStyle}>
            <ConfigurationFlowToolbar
              d={d}
              configurationFlow={configurationFlow}
              handleUpdateSetting={handleUpdateSetting}
              handleFlowStatusSetting={handleFlowStatusSetting}
              handleToggleFlowHidden={handleToggleFlowHidden}
            />
          </Panel>
        </FlowCanvas>
      </Stack>
      {flowConfigurationsFetched && <StepTypeModal />}
    </>
  );
};
