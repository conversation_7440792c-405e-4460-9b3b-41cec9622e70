.flow-step-field {
  gap: var(--spacing-025);
  & .floating-with-parent {
    right: anchor(end);
    left: auto;
    max-width: 45vw;
  }

  &__variable-select {
    height: 100%;
    & .select__value,
    & .select__value--absolute {
      display: none !important;
    }

    & .select__clear {
      display: none;
    }
  }
}

// In FlowExecutionCanvasStepModal.tsx
.flow-execution-step-modal {
  & .select--disabled {
    background-color: var(--color-surface-secondary) !important;
    color: var(--color-text-primary) !important;
  }

  .label--disabled .label__text {
    color: var(--color-text-secondary) !important;
  }

  .text-field__value {
    background-color: var(--color-surface-secondary) !important;

    & .text-field__input {
      color: var(--color-text-primary) !important;
    }
  }
}
