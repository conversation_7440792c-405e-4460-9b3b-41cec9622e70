package services.oneteam.ai.permissions.rebac

import kotlinx.serialization.Serializable

@JvmInline
@Serializable
value class Resource(val key: String)

@JvmInline
@Serializable
value class Relationship(val key: String)

@JvmInline
@Serializable
value class Permission(val key: String)

/**
 * This object defines the structure of permissions and relationships used in the REBAC system.
 * It directly mirrors the schema eg schema.zed
 */
object PermissionStructure {
    object Common {
        object Relationships {

            val TENANT = Relationship("tenant")
            val WORKSPACE = Relationship("workspace")
            val PARENT = Relationship("parent")
            val INHERIT_FROM = Relationship("inherit_from")

            val MANAGER = Relationship("manager")
            val READ = Relationship("read")
            val WRITE = Relationship("write")

        }

        object Permissions {
            val VIEW = Permission("view")
            val EDIT = Permission("edit")
            val MANAGE = Permission("manage")
        }
    }


    object Tenant {
        val NAME = Resource("tenant")
    }

    object Subject {
        val NAME = Resource("subject")
    }

    object Workspace {
        val NAME = Resource("workspace")

        object Relationships {
            val TENANT = Common.Relationships.TENANT

            val COLLECTION = Relationship("collection")
            val CONFIGURATION = Relationship("configuration")
            val SETTINGS = Relationship("settings")

            fun accessLevels(): List<Relationship> = listOf(COLLECTION, CONFIGURATION, SETTINGS)
        }

        object Permissions {
            val HAS_COLLECTION = Permission("has_collection")
            val HAS_CONFIGURATION = Permission("has_configuration")
            val HAS_SETTINGS = Permission("has_settings")
            val VIEW = Common.Permissions.VIEW
        }
    }

    object Foundation {
        val NAME = Resource("foundation")

        object Relationships {
            val TENANT = Common.Relationships.TENANT
            val WORKSPACE = Common.Relationships.WORKSPACE

            val PARENT = Common.Relationships.PARENT
            val INHERIT_FROM = Common.Relationships.INHERIT_FROM

            val MANAGER = Common.Relationships.MANAGER
            val READ = Common.Relationships.READ
            val WRITE = Common.Relationships.WRITE
        }

        object Permissions {
            val VIEW = Common.Permissions.VIEW
            val EDIT = Common.Permissions.EDIT
            val MANAGE = Common.Permissions.MANAGE
        }
    }

    object Form {
        val NAME = Resource("form")

        object Relationships {
            val TENANT = Common.Relationships.TENANT
            val WORKSPACE = Common.Relationships.WORKSPACE

            val PARENT = Common.Relationships.PARENT
            val INHERIT_FROM = Common.Relationships.INHERIT_FROM

            val MANAGER = Common.Relationships.MANAGER
            val READ = Common.Relationships.READ
            val WRITE = Common.Relationships.WRITE
        }

        object Permissions {
            val VIEW = Common.Permissions.VIEW
            val EDIT = Common.Permissions.EDIT
            val MANAGE = Common.Permissions.MANAGE
        }
    }
}

interface RebacRepository {

    fun readRelationships(
        resourceType: Resource,
        resourceId: String,
        relationship: services.oneteam.ai.permissions.rebac.Relationship,
        subjectType: Resource? = null,
        subjectId: String? = null,
        token: String? = null
    ): List<String>

    fun writeRelationship(
        resourceType: Resource, resourceId: String, relationship: Relationship, subjectType: Resource, subjectId: String
    ): String

    fun findAllResourcesWithPermissionForSubject(
        resourceType: Resource,
        permission: Permission,
        subjectType: Resource,
        subjectId: String,
        token: String? = null
    ): List<String>

    fun findAllSubjectsWithPermissionOnResource(
        resourceType: Resource,
        resourceId: String,
        permission: Permission,
        token: String? = null
    ): List<String>

    fun checkSubjectPermissionForResource(
        resourceType: Resource,
        resourceId: String,
        permission: Permission,
        subjectType: Resource,
        subjectId: String,
        token: String? = null
    ): Boolean

    fun findAllRelationshipsForUser(
        subjectType: Resource,
        subjectId: String,
        prefix: String? = null,
        token: String? = null
    ): List<String>

    fun deleteRelationshipsForResourceAndSubject(resourceType: Resource, resourceId: String, subjectId: String): String

    fun deleteAllRelationshipsForResource(resourceType: Resource, resourceId: String): String

    fun deleteAllRelationshipsForType(resourceType: Resource, relationship: Relationship): String

    fun writeSchema(schema: String): String
}