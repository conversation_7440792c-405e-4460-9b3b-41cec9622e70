import React, { useCallback, useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import {
  Accordion,
  CustomAccordionTrigger,
  Heading,
  HeadingSize,
  Inline,
  OpenCloseIcon,
  Stack
} from "@oneteam/onetheme";

import { ZodAnswerSchemaBuilder } from "@helpers/forms/ZodAnswerSchemaBuilder/ZodAnswerSchemaBuilder.ts";

import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import { TableQuestionProperties } from "@src/types/QuestionProperties";

import "./QuestionPreview.scss";
import { QuestionTablePreview } from "./QuestionTablePreview";

export const QuestionPreview = ({
  question,
  parentQuestion
}: {
  question: Question;
  parentQuestion?: Question;
}) => {
  const [isOpen, setIsOpen] = useState(true);

  const d = useDictionary();

  const formOptions = useMemo(() => {
    const schemaBuilder = new ZodAnswerSchemaBuilder([question], d);
    return {
      resolver: zodResolver(schemaBuilder.generatedSchema),
      mode: "all" as const
    };
  }, [d, question]);

  const formMethods = useForm(formOptions);

  const accordionTrigger: CustomAccordionTrigger = useCallback(
    ({ isOpen, onClick }) => (
      <Inline
        gap="050"
        width="100"
        onClick={onClick}
        alignment="left"
        marginBottom="150"
      >
        <Heading size={HeadingSize.XS}>
          <Inline gap="100">
            {d("ui.configuration.forms.question.preview")}
          </Inline>
        </Heading>
        <OpenCloseIcon isOpen={isOpen} />
      </Inline>
    ),
    [d]
  );

  return (
    <Stack className="question-preview" contentsWidth="100" gap="050">
      <Accordion
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        key={question.id}
        trigger={accordionTrigger}
      >
        <FormProvider {...formMethods}>
          {question.type === QuestionTypes.TABLE ? (
            <QuestionTablePreview
              question={question as Question<TableQuestionProperties>}
            />
          ) : (
            <QuestionAnswer
              question={question}
              answer={undefined}
              answerAccessor={""}
              parentQuestion={parentQuestion ?? undefined}
            />
          )}
        </FormProvider>
      </Accordion>
    </Stack>
  );
};
