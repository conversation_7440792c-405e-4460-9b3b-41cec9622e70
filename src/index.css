:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 200;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  background-color: #ff5bf2aa;
}

body {
  margin: 0;
  display: flex;
  place-items: top;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 1.2em;
  line-height: 1;
}

button {
  border-radius: 15px;
  border: 1px solid transparent;
  padding: 0.2em 0.6em;
  font-size: .7em;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.padded {
  padding: 1em;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    /*background-color: #f9f9f9;*/
  }
}
