package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.JsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.variables.DefaultVariableOperation
import services.oneteam.ai.flow.execution.variables.FileVariableOperation
import services.oneteam.ai.flow.execution.variables.ListVariableOperation
import services.oneteam.ai.flow.execution.variables.TableVariableOperation
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService

fun interface ExecutionStepFactory {
    suspend fun createStep(step: FlowExecution.Step, flowRunner: FlowRunner): ExecutionStep
}

@Deprecated("Use ExecutionStepFactoryV2 instead", ReplaceWith("ExecutionStepFactoryV2"))
class ExecutionStepFactoryV1(
    private val flowStepTypeConfigurationService: FlowStepTypeConfigurationService,
    private val proxyService: ProxyService,
    private val internalProxyService: ProxyService,
    private val filePressService: FilePressService,
    private val mapBuilders: List<MapBuilder>,
    private val workspaceVersionService: WorkspaceVersionService,
    private val expressionEvaluator: JsonataExpressionEvaluator
) : ExecutionStepFactory {

    override suspend fun createStep(step: FlowExecution.Step, flowRunner: FlowRunner): ExecutionStep {
        val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(mapBuilders, step.id.value)


        when (step.variant) {
            FlowExecution.Step.Variant.SET_VARIABLES -> return SetVariablesExecutionStep(
                step, contextToJsonObjectBuilder,
                listOf(
                    TableVariableOperation(true, expressionEvaluator),
                    ListVariableOperation(true, expressionEvaluator),
                    FileVariableOperation(true, expressionEvaluator),
                    DefaultVariableOperation(true, expressionEvaluator)
                ),
            )

            FlowExecution.Step.Variant.ITERATOR -> {
                val identifier = step.properties.typePrimaryIdentifier
                    ?: throw IllegalArgumentException("No identifier found for iterator step")
                val flowStepTypeConfiguration = flowStepTypeConfigurationService.getByPrimaryIdentifier(identifier)
                    ?: throw IllegalArgumentException("No configuration found for iterator step")

                return when (step.properties.typePrimaryIdentifier) {
                    "iteratorForEach" -> IteratorForEachStep(
                        step, flowRunner, contextToJsonObjectBuilder, flowStepTypeConfiguration
                    )

                    "iteratorFilter" -> IteratorFilterStep(
                        step, flowRunner, contextToJsonObjectBuilder, flowStepTypeConfiguration
                    )

                    "iteratorAggregate" -> IteratorAggregateStep(
                        step, flowRunner, contextToJsonObjectBuilder, flowStepTypeConfiguration
                    )

                    else -> throw IllegalArgumentException("Unknown iterator type $identifier")
                }
            }

            FlowExecution.Step.Variant.CONDITION -> {
                return ConditionExecutionStep(
                    step, contextToJsonObjectBuilder, expressionEvaluator
                )
            }

            FlowExecution.Step.Variant.ACTION -> return ActionExecutionStep(
                step,
                flowStepTypeConfigurationService.getByPrimaryIdentifier(step.properties.typePrimaryIdentifier!!)
                    ?: throw IllegalArgumentException("No configuration found for action step"),
                contextToJsonObjectBuilder,
                proxyService,
                internalProxyService,
                filePressService,
                expressionEvaluator
            )

            FlowExecution.Step.Variant.FLOW -> return FlowInFlowExecutionStep(
                step, flowRunner, workspaceVersionService, contextToJsonObjectBuilder
            )

            else -> throw IllegalArgumentException("Unknown step type ${step.variant}")
        }
    }
}

val logger: Logger = LoggerFactory.getLogger("ExecutionStep")

fun interface ExecutionStep {

    suspend fun execute(context: FlowContextWithLocalStep): NextStepId?
    suspend fun populate(context: FlowContextWithLocalStep) {}

    /**
     * Inputs are populated using values from the context, and the step type configuration is used to determine the type of the input.
     * Once populated, inputs are set in the context `thisStep` namespace.
     */
    suspend fun processInputs(
        context: FlowContextWithLocalStep,
        contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
        step: FlowExecution.Step,
        stepTypeConfiguration: FlowStepTypeConfiguration
    ) {

        val valueProvider = ContextPlaceholderValueProvider(contextToJsonObjectBuilder, context)
        logger.trace("Populating inputs {} for step {}", step.properties.inputs, step)
        step.properties.setInputs(JsonPlaceholderReplacer().replacePlaceholders(step.properties.inputs, valueProvider))
        logger.trace("Populated inputs {} for step {}", step.properties.inputs, step)
        val configuration = stepTypeConfiguration.properties?.configuration
            ?: throw IllegalArgumentException("No configuration found for action step in ${stepTypeConfiguration.primaryIdentifier}")

        // add the inputs to the context
        step.properties.inputs.forEach { (k, v) ->
            // convert to type based on configuration
            val content = configuration.findContentByIdentifier(k)

            if (content == null) {
                // Skip this item
                logger.warn(
                    "`{}` is not an input for step type configuration `{}`", k, stepTypeConfiguration.primaryIdentifier
                )
                return@forEach
            }

            if (content.getType().typeDef == "variable") {
                context.setThisStep(k, v)
            } else {
                val type =
                    content.properties.properties?.type ?: content.properties.type ?: content.getType().typeDef

                // Handle the case where v is already a JsonElement
                val typedValue = if (v is JsonPrimitive) {
                    v
                } else {
                    TypeToJsonElementConverter.convert(VariableDataType.fromString(type), v).let {
                        logger.trace(
                            "Converted value for `{}` of type `{}` to JSON element with value `{}`", k, type, it
                        )
                        it
                    }
                }


                context.setThisStep(k, typedValue)
            }

        }

    }
}

