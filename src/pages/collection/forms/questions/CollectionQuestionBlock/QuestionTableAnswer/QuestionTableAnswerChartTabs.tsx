import React, { useMemo } from "react";

import { Stack, TabGroup } from "@oneteam/onetheme";

import { Dictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import {
  ChartQuestionProperties,
  TableDefaultView,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { ChartAnswer } from "@src/types/collection/CollectionForm";

import { QuestionChartAnswer } from "../QuestionChartAnswer/QuestionChartAnswer";

export const QuestionTableAnswerChartTabs = ({
  chartsCanBeDisplayed,
  tableViewPanelTab,
  setTableViewPanelTab,
  chartAnswer,
  parentQuestion,
  d
}: {
  chartsCanBeDisplayed: Question<ChartQuestionProperties>[];
  tableViewPanelTab: TableDefaultView;
  setTableViewPanelTab: (tab: TableDefaultView) => void;
  chartAnswer?: ChartAnswer;
  parentQuestion: Question<TableQuestionProperties>;
  d: Dictionary;
}) => {
  const defaultViewOptions = useMemo(
    () =>
      Object.values(TableDefaultView).map(value => ({
        value,
        label: d(
          `ui.configuration.forms.question.table.visibility.defaultView.tabs.${value}`
        )
      })),
    [d]
  );
  return (
    <>
      <Stack className="question-table-view__header">
        <TabGroup
          options={defaultViewOptions}
          value={tableViewPanelTab}
          handleChange={value =>
            setTableViewPanelTab(value as TableDefaultView)
          }
          className="question-table-view__tabs"
        />
      </Stack>
      {(tableViewPanelTab === TableDefaultView.ALL ||
        tableViewPanelTab === TableDefaultView.CHART) && (
        <>
          {chartsCanBeDisplayed.map(chart => (
            <QuestionChartAnswer
              key={chart.id}
              answer={chartAnswer}
              question={chart}
              parentQuestion={parentQuestion}
            />
          ))}
        </>
      )}
    </>
  );
};
