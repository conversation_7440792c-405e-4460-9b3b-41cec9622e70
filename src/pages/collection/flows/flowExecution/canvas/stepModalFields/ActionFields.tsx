import React, { useMemo } from "react";

import { Form } from "@oneteam/onetheme";

import { internalOtaiFormField } from "@helpers/otaiFormHelper";

import { InternalOTAIFormFieldQuestion } from "@components/shared/OTAIForm/OTAIFormType";

import { ConfigurationFlowMode } from "@pages/configuration/ConfigurationFlowMode";
import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { CustomPropertyField } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowStepModal/ConfigurationFlowBlockFields/CustomPropertyField";

import { useDictionary } from "@src/hooks/useDictionary";
import { MockFlowContext } from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { CommonTypedStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { FlowStepTypeConfiguration } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";

export const ActionFields = ({
  stepTypeConfig,
  configurationStep,
  flowContext
}: {
  stepTypeConfig: FlowStepTypeConfiguration;
  configurationStep: FlowStep;
  flowContext?: MockFlowContext;
}) => {
  const { configurationFlowMode } = useConfigurationFlowContext();

  const stepTypeContentQuestions = useMemo(() => {
    if (!stepTypeConfig?.properties.configuration.content?.length) {
      return undefined;
    }

    return stepTypeConfig.properties.configuration.content.map(q => {
      const question = internalOtaiFormField(
        q as InternalOTAIFormFieldQuestion
      );

      const stepProperties =
        configurationStep.properties as CommonTypedStepProperties;
      const answer = stepProperties?.inputs?.[q.identifier];
      return {
        ...question,
        answer: answer as string
      };
    });
  }, [stepTypeConfig, configurationStep]);

  const d = useDictionary();

  return (
    <Form d={d}>
      {stepTypeContentQuestions?.map(question => (
        <CustomPropertyField
          disabled={configurationFlowMode !== ConfigurationFlowMode.EDIT}
          key={question.identifier}
          question={question}
          selectedStep={configurationStep}
          handleSaveInput={() => {}}
          mockFlowContext={flowContext}
          pathInDocument={[]}
          onlyTriggerChangeWhenBlur
        />
      ))}
    </Form>
  );
};
