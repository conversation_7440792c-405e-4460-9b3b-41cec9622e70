package services.oneteam.ai.flow.expression.functions

import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.expression.functions.Max.DEFAULT_VALUE
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.extensions.maybeParseDate
import services.oneteam.ai.shared.extensions.toBigDecimalList
import java.math.BigDecimal
import java.time.LocalDate

@Serializable
internal object Max : ComputeFunction {

    val DEFAULT_VALUE: BigDecimal = BigDecimal.ZERO

    override val key = "max"
    override val category = "array"
    override val icon = Icon("function")
    override val functionName = "MAX"

    override val syntax = Syntax(
        "MAX(number, ... )", listOf(
            Argument("number", "Numbers (or Tensors) to find the maximum value", "number"),
            Argument("...", "Additional numbers (or Tensors) to find the maximum value", "number")
        )
    )
    override val description = "Find the maximum value in an array"
    override val notes = """
        A Tensor can be a single number (scalar), an array of numbers (vector), or a matrix of numbers.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()
    override val examples = listOf(
        Example(
            "$${functionName}(1,2,3) => 1",
            "Returns the maximum of the numbers"
        ),
        Example(
            "$${functionName}([1,2,6],[4,5,3]) => [1,2,3]",
            "Pairwise maximum of two lists of numbers"
        ),
        Example(
            "$${functionName}([1,2,3], 5) => [1,2,3]",
            "Lower dimension is uplifted (5 -> [5,5,5]) then find pairwise maximum",
        ),
        Example(
            "$${functionName}([1,2,3], [4,5]) => [1, 2, 0]",
            "Finds pairwise maximum of two lists of numbers, shorter list is padded with 0",
        ),
        Example(
            "$${functionName}([4, 5], [[1, 2, 3], [5, 4, 3]]) => [[1,2,0], [4,4,0]]",
            """
                Dimension uplift and padded then find pairwise maximum.
                => [[4,5,0],[4,5,0]] , [[1,2,3], [5,4,3]] 
                => [[1,2,0], [4,4,0]]
            """.trimIndent(),
        ),
    )

    val implementations: List<MatchableFunction> = listOf(
        FuzzyMax,
        FuzzyList(FuzzyMax),
        Pairwise(PairwiseOperation.MAX, DEFAULT_VALUE),
    )

    @Transient
    override val evaluatorFunction = buildJFunctionForImplementations(
        implementations, "<x+:x>",
        functionName
    )
}

private object FuzzyMax : MatchableFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    /**
     * Handles a list of mixed arguments, trying to find the maximum value.
     * 1. Filters out nulls and empty strings.
     * 2. Attempts to parse all remaining strings as dates, returning the latest date if successful.
     * 3. If date parsing fails, falls back to numeric comparison, ignoring non-numeric values.
     * 4. Returns 0 if no valid numbers or dates are found.
     * @param args List of arguments to evaluate.
     * @return The maximum date as a string, the maximum number as a BigDecimal, or 0 if no valid values are found.
     */
    override fun perform(args: List<*>): Any {
        // Filter out nulls and empty strings
        val filtered = args.filter { it != null && (it !is String || it.isNotEmpty()) }
        if (filtered.isEmpty()) return DEFAULT_VALUE

        // Try to parse all as dates, ignore those that fail
        val dates: List<LocalDate> = filtered.mapNotNull {
            maybeParseDate(it)
        }
        if (dates.isNotEmpty()) {
            return dates.maxOrNull()?.toString() ?: DEFAULT_VALUE
        }

        // Fallback to number logic
        return filtered.toBigDecimalList().maxOfOrNull { it }?.convertToNiceBigDecimal() ?: DEFAULT_VALUE
    }
}
