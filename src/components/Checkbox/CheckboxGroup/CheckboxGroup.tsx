import React from "react";

import { Box, FermionProps, Inline } from "../../../fermions/index.ts";
import { composeComponentClassNames } from "../../../helpers/index.ts";
import { Label, LabelTextPosition } from "../../Label/index.ts";
import { Checkbox } from "../Checkbox.tsx";
import "./CheckboxGroup.scss";

export type CheckboxGroupOptionSingleValue = string | boolean | number;
export type CheckboxGroupOptionValue = Array<CheckboxGroupOptionSingleValue>;

export interface CheckboxGroupOption {
  name?: string;
  value: CheckboxGroupOptionSingleValue;
  label?: string;
  disabled?: boolean;
}

export interface CheckboxGroupProps {
  id?: string;
  name?: string;
  value?: CheckboxGroupOptionValue;
  required?: boolean;
  options: CheckboxGroupOption[];
  onChange?: (value?: CheckboxGroupOptionValue) => void;
  defaultValue?: CheckboxGroupOptionValue;
  description?: string;
  tooltip?: string;
  error?: string;
  disabled?: boolean;
  label?: string;
  direction?: "row" | "column";
  labelTextPosition?: LabelTextPosition;
  width?: FermionProps["width"];
  skipFocus?: boolean;
}

export const CheckboxGroup = ({
  id,
  name,
  value,
  options,
  required,
  onChange,
  description,
  tooltip,
  error,
  disabled,
  label = "",
  direction = "row",
  labelTextPosition = LabelTextPosition.TOP,
  width = "fit",
  skipFocus = false
}: CheckboxGroupProps) => {
  return (
    <Box className="checkbox-group" width={width}>
      <Label
        label={label}
        htmlFor={id}
        textPosition={labelTextPosition}
        disabled={disabled}
        description={description}
        error={error}
        required={required}
        tooltip={tooltip}
      >
        <Inline gap="150">
          <fieldset
            id={id}
            className={composeComponentClassNames("checkbox-group__items", {
              direction
            })}
            role="radiogroup"
            name={name}
          >
            {options.map(option => (
              <Checkbox
                name={option.name}
                id={String(option.value)}
                key={String(option.value)}
                value={String(option.value)}
                label={option.label}
                disabled={disabled || option.disabled}
                isChecked={value?.includes(option.value)}
                onChange={() => {
                  if (value?.includes(option.value)) {
                    onChange?.(value.filter(v => v !== option.value) ?? []);
                  } else {
                    onChange?.([...(value ?? []), option.value]);
                  }
                }}
                skipFocus={skipFocus}
              />
            ))}
          </fieldset>
        </Inline>
      </Label>
    </Box>
  );
};
