@mixin button_traffic($variant, $traffic) {
  border-color: var(
    --components-button-#{$variant}-#{$traffic}-color-border,
    #ff1067
  );
  background-color: var(
    --components-button-#{$variant}-#{$traffic}-color-background,
    #ff1067
  );
  color: var(--components-button-#{$variant}-#{$traffic}-color-text, #fff);
}

@mixin button_variant($variant) {
  border-color: var(--components-button-#{$variant}-color-border, #021429);
  background: var(--components-button-#{$variant}-color-background, #021429);
  color: var(--components-button-#{$variant}-color-text, #fff);

  &:hover {
    border-color: var(
      --components-button-#{$variant}-hover-color-border,
      #485d74
    );
    background: var(
      --components-button-#{$variant}-hover-color-background,
      #485d74
    );
    color: var(--components-button-#{$variant}-hover-color-text, #fff);
  }

  &:disabled,
  &:has(.icon-button:disabled) {
    border-color: var(
      --components-button-#{$variant}-disabled-color-border,
      #cdd4e3
    );
    background: var(
      --components-button-#{$variant}-disabled-color-background,
      #cdd4e3
    );
    color: var(--components-button-#{$variant}-disabled-color-text, #fff);
    pointer-events: none;
  }

  &.button--traffic {
    &-danger {
      @include button_traffic($variant, "danger");
    }
    &-warning {
      @include button_traffic($variant, "warning");
    }
    &-success {
      @include button_traffic($variant, "success");
    }
  }
}

.split-button {
  border: none;
  cursor: pointer;
  transition: all 0.1s;
  max-width: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  outline-offset: 2px;

  &__left,
  &__right {
    gap: var(--components-button-gap, 2px);
    display: flex;
    justify-content: center;
    align-items: center;
    outline-offset: 2px;
    &:focus,
    &:focus-within {
      z-index: 1;
      outline: 1px solid var(--components-inputs-focus-color-border, #0083ff);
    }

    & .icon-button:focus {
      outline: none;
    }
  }
  & label {
    cursor: pointer;
    font-style: normal;
    font-weight: var(--components-button-font-weight, 500);
    font-size: var(--components-button-font-size, 14px);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &:disabled {
    cursor: not-allowed;
    pointer-events: none;
    & label {
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  &:focus {
    outline: 1px solid var(--components-inputs-focus-color-border, #0083ff);
  }

  & .dropdown-menu__trigger {
    height: 100%;
  }

  &__right__trigger {
    color: inherit;
    & .icon-button {
      color: inherit;
    }
  }

  &--size {
    &-default {
      $border-width: var(--components-button-border-width, 1px);
      $border-radius: var(--components-button-border-radius, 12px);

      border-radius: $border-radius;
      & .split-button__left,
      & .split-button__right {
        border: none;
        border-style: solid;
        height: var(--components-button-height, 32px);
        padding: 0px var(--components-button-padding-horizontal, 12px);
      }

      & .split-button__right {
        border-width: $border-width $border-width $border-width 0;
        border-radius: 0 $border-radius $border-radius 0;
        padding: 0;
        height: var(--components-button-height, 32px);
        &__trigger {
          padding: 0px var(--spacing-050);
          height: 100%;
        }
      }
      & .split-button__left {
        border-width: $border-width 0 $border-width $border-width;
        border-radius: $border-radius 0 0 $border-radius;
      }
    }
    &-small {
      $border-width: var(--components-button-small-border-width, 1px);
      $border-radius: var(--components-button-small-border-radius, 8px);

      border-radius: $border-radius;

      & .split-button__left,
      & .split-button__right {
        border: none;
        height: var(--components-button-small-height, 24px);
        padding: 0px var(--components-button-small-padding-horizontal, 8px);
      }

      & .split-button__right {
        border-width: $border-width $border-width $border-width 0;
        border-radius: 0 $border-radius $border-radius 0;
        padding: 0;
        height: var(--components-button-small-height, 24px);
        &__trigger {
          padding: 0px var(--spacing-050);
          height: 100%;
        }
      }
      & .split-button__left {
        border-width: $border-width 0 $border-width $border-width;
        border-radius: $border-radius 0 0 $border-radius;
      }
    }
  }

  &--variant {
    &-primary {
      & .split-button__left,
      & .split-button__right {
        @include button_variant("primary");
      }

      & .split-button__divider {
        height: var(--components-button-height, 32px);
        width: var(--components-button-border-width, 1px);
        background-color: var(--components-button-primary-color-text, #fff);
      }
    }
    &-secondary {
      & .split-button__left,
      & .split-button__right {
        @include button_variant("secondary");
      }

      & .split-button__divider {
        height: var(--components-button-height, 32px);
        width: var(--components-button-border-width, 1px);
        background-color: var(--components-button-secondary-color-border, #fff);
      }
    }
  }
}
