import crypto from "crypto";
import { NextFunction, Request, Response } from "express";
import jwt, { JwtPayload } from "jsonwebtoken";
import { appConfig } from "src/helpers/appConfig.ts";
import { Logger } from "src/helpers/logger.ts";
import { Actor, ActorType } from "src/types/Actor.ts";

import { User } from "../types/index.js";

const log = Logger("otai:SessionCookie");
const jwtPublicKey = appConfig.jwtPublicKey;
export interface AuthenticatedRequest extends Request {
  user?: string | JwtPayload;
  actor: Actor;
}

export function decrypt(cookie: string): string {
  const [rawIV, cipherAndSig] = cookie.split("/");
  const [cipher] = cipherAndSig.split(":"); // ignoring signature for now
  const iv = Buffer.from(rawIV, "hex");
  const keyBuffer = Buffer.from(appConfig.sessionEncrypt, "hex");

  const decipher = crypto.createDecipheriv("aes-128-cbc", keyBuffer, iv);
  let dec = decipher.update(cipher, "hex", "utf-8");
  dec += decipher.final();
  return dec;
}

export function getUser(cookieHeader?: string): User | null {
  if (!cookieHeader) {
    return null;
  }
  const cookies = cookieHeader.split(";");
  for (const cookie of cookies) {
    if (cookie.includes("user_session=")) {
      const [, rawValue] = cookie.split("=");
      const value = decodeURIComponent(rawValue);
      const session = JSON.parse(decrypt(value));
      log.info("[checkCookie] session: ", session.user);
      return session?.user;
    }
  }
  return null;
}

export function sessionAuthentication(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (req.header("Authorization")) {
    sessionJWTMiddleware(req, res, next);
  } else if (req?.cookies?.user_session) {
    sessionCookieMiddleware(req, res, next);
  } else {
    log.warn(`[sessionJWTMiddleware] error: no token or cookie`);
    res.status(401).json({ message: "Unauthorized" });
  }
}

export function sessionCookieMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (req?.cookies?.user_session) {
    log.info(
      `[sessionCookieMiddleware] found a cookie: ${req.cookies.user_session}`
    );
    try {
      req.session = JSON.parse(decrypt(req.cookies.user_session));
      if (req.session?.user.id) {
        (req as AuthenticatedRequest).actor = {
          type: ActorType.USER,
          userId: req.session.user.id
        };
      }
      next();
      return;
    } catch (e) {
      log.error(`[sessionCookieMiddleware] error: ${e}`);
    }
  }
  res.status(401).json({ message: "Unauthorized" });
}

export function sessionJWTMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  const token = req.header("Authorization")?.replace("Bearer ", "").trim();

  if (!token) {
    log.warn(`[sessionJWTMiddleware] error: missing token`);
    return res.status(401).json({ message: "Unauthorized" });
  }

  try {
    const authenticatedRequest = req as AuthenticatedRequest;
    authenticatedRequest.user = jwt.verify(token, jwtPublicKey);

    setActorFromFlowHeader(authenticatedRequest);

    next();
  } catch (err) {
    log.warn(`[sessionJWTMiddleware] error: ${err}`);
    return res.status(401).json({ message: "Unauthorized" });
  }
}

function setActorFromFlowHeader(req: AuthenticatedRequest) {
  const xOtaiFlow = req.header("x-otai-flow");
  if (!xOtaiFlow) {
    // system actor, eg: FED updates
    req.actor = { type: ActorType.SYSTEM };
    return;
  }

  const ids = xOtaiFlow.trim().split(":");
  if (ids.length < 4) {
    log.warn(`[sessionJWTMiddleware] error: invalid x-otai-flow format`);
    return;
  }
  const [workspaceId, flowConfigurationId, stepId, flowExecutionId] = ids;
  if (flowExecutionId) {
    req.actor = {
      type: ActorType.FLOW,
      flowExecutionId,
      workspaceId,
      flowConfigurationId,
      stepId
    };
  } else {
    req.actor = {
      type: ActorType.SYSTEM
    };
  }
}
