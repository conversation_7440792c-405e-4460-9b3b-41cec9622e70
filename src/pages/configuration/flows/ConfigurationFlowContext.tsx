import React, { createContext, useContext } from "react";

import { ReactFlowInstance } from "@xyflow/react";

import {
  FlowConfiguration,
  MockFlowContext
} from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStepId,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  FlowStepTypeConfiguration,
  FlowStepTypeConfigurationPrimaryIdentifier
} from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import { VariableTypeDefinition } from "@src/types/FlowConfiguration/Variables";

import { ConfigurationFlowMode } from "../ConfigurationFlowMode";

export type SelectingStepType = {
  type: `${FlowStepVariant}`;
  parentStepId?: FlowStepId; // to add after existing step
  branchIndex?: number; // if parent is a condition step - need to know which branch to add to
  stepId?: FlowStepId; // for update
  currentFlowStepTypePrimaryIdentifier?: FlowStepTypeConfigurationPrimaryIdentifier;
};

export type ConfigurationFlowContextType = {
  selectedStepId?: string;
  setSelectedStepId: (step?: string) => void;
  setSelectedStepIdWithUnfocus: (step?: string) => void;

  flowStepTypeConfigByPrimaryIdentifier?: {
    [
      key: FlowStepTypeConfigurationPrimaryIdentifier
    ]: FlowStepTypeConfiguration;
  };
  setFlowStepTypeConfigByPrimaryIdentifier: (flowStepType?: {
    [
      key: FlowStepTypeConfigurationPrimaryIdentifier
    ]: FlowStepTypeConfiguration;
  }) => void;

  configurationFlow?: FlowConfiguration;
  setConfigurationFlow: (flow?: FlowConfiguration) => void;

  selectingStepType?: SelectingStepType;
  setSelectingStepType: (type?: SelectingStepType) => void;

  path?: string[];
  setPath: (path: string[]) => void;

  mockFlowContext?: MockFlowContext;
  setMockFlowContext: (context?: MockFlowContext) => void;

  variableDefinitions?: { [path: string]: VariableTypeDefinition };
  setVariableDefinitions: (context?: {
    [path: string]: VariableTypeDefinition;
  }) => void;

  variablesByName?: { [path: string]: VariableTypeDefinition };
  setVariablesByName: (context?: {
    [path: string]: VariableTypeDefinition;
  }) => void;

  variablesByPath?: { [path: string]: VariableTypeDefinition };
  setVariablesByPath: (context?: {
    [path: string]: VariableTypeDefinition;
  }) => void;

  zoomLevel?: number;
  setZoomLevel: (level: number) => void;

  mainViewportRef?: ReactFlowInstance | null;
  setMainViewportRef: (ref: ReactFlowInstance | null) => void;

  configurationFlowMode?: ConfigurationFlowMode;
  setConfigurationFlowMode: (mode: ConfigurationFlowMode) => void;

  settings?: {
    debugMode: boolean;
    showMiniMap: boolean;
    isRenaming?: boolean;
  };
  setSettings: (settings: {
    debugMode: boolean;
    showMiniMap: boolean;
    isRenaming: boolean;
  }) => void;

  refreshFlowCanvas: () => void;
  setRefreshFlowCanvas: React.Dispatch<React.SetStateAction<() => void>>;
};

export const ConfigurationFlowContext =
  createContext<ConfigurationFlowContextType>({
    // Opening modal
    selectedStepId: undefined,
    setSelectedStepId: () => {},
    setSelectedStepIdWithUnfocus: () => {},

    // Flow document
    configurationFlow: undefined,
    setConfigurationFlow: () => {},

    // Flow step type definitions
    flowStepTypeConfigByPrimaryIdentifier: undefined,
    setFlowStepTypeConfigByPrimaryIdentifier: () => {},

    // Flow step type selection
    selectingStepType: undefined,
    setSelectingStepType: () => {},

    // Nested iterator paths
    path: undefined,
    setPath: () => {},

    // Mock flow context
    mockFlowContext: undefined,
    setMockFlowContext: () => {},

    // Variable definitions
    variableDefinitions: undefined,
    setVariableDefinitions: () => {},

    // Variables by name
    variablesByName: undefined,
    setVariablesByName: () => {},

    // Variables by path
    variablesByPath: undefined,
    setVariablesByPath: () => {},

    // Zoom level
    zoomLevel: 1,
    setZoomLevel: () => {},

    // Main viewport reference
    mainViewportRef: null,
    setMainViewportRef: () => {},

    // Settings
    settings: {
      debugMode: false,
      showMiniMap: true,
      isRenaming: false
    },
    setSettings: () => {},

    // Configuration Flow Mode
    configurationFlowMode: undefined,
    setConfigurationFlowMode: () => {},

    // Flow canvas refresh
    refreshFlowCanvas: () => {},
    setRefreshFlowCanvas: () => {}
  });

export const useConfigurationFlowContext = () =>
  useContext(ConfigurationFlowContext);
