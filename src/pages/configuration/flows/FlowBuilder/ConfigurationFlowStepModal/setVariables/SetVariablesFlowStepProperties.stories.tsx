import React from "react";

import { Form, Stack } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { z } from "zod";

import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep.ts";
import { SetVariablesStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties.ts";

import { MockFlowStep } from "../storyConstants.ts";
import { SetVariablesFlowStepProperties } from "./SetVariablesFlowStepProperties.tsx";

const meta: Meta<typeof SetVariablesFlowStepProperties> = {
  component: SetVariablesFlowStepProperties,
  title: "collection/ConfigurationFlowStepModal/setVariables"
};

export default meta;

type Story = StoryObj<typeof SetVariablesFlowStepProperties>;

const mockSetVariables = MockFlowStep[
  "4"
] as FlowStep<SetVariablesStepProperties>;

const defaultArgs: Story["args"] = {
  step: mockSetVariables
};

const defaultRender = (args: Story["args"]) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [step, setStep] = React.useState(mockSetVariables);

  const handleVariableChange = (
    variables: SetVariablesStepProperties["variables"]
  ) => {
    setStep(current => ({
      ...current,
      properties: {
        ...current.properties,
        variables
      }
    }));
  };

  return (
    <Stack
      gap="100"
      width="fill"
      overflow="auto"
      style={{ maxHeight: "100vh" }}
    >
      <Form
        schema={z.any()}
        d={(key: string) => key}
        handleSubmit={() => console.log("Submit")}
        defaultValues={mockSetVariables}
        hideFormButtons
      >
        <SetVariablesFlowStepProperties
          {...args}
          step={step}
          onVariableAdd={variable =>
            handleVariableChange([...step.properties.variables, variable])
          }
          onFieldChange={(index, field, value) => {
            const newVariables = [...step.properties.variables];
            newVariables[index] = {
              ...newVariables[index], // this type of update should not be done with automerge document
              [field]: value
            };
            handleVariableChange(newVariables);
          }}
          onVariableRemove={index => {
            if (step.properties.variables.length === 1) {
              alert("Cannot remove the last variable");
              return;
            }
            handleVariableChange(
              step.properties.variables.filter((_, i) => i !== index)
            );
          }}
          onVariableUpdate={() => {}}
          path={[]}
        />
      </Form>
    </Stack>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRender
};
