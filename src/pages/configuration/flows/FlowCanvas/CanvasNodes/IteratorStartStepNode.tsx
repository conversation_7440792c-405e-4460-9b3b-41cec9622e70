import React, { useMemo } from "react";

import {
  Box,
  Heading,
  Icon,
  Inline,
  Stack,
  WhiteboardBlock,
  WhiteboardLine
} from "@oneteam/onetheme";
import { Handle, NodeProps, Position, useNodeConnections } from "@xyflow/react";

import { getIteratorStartingVariables } from "@helpers/flows/flowVariables/flowVariableHelpers";

import { useFlowStepHandlers } from "@pages/configuration/flows/hooks/useFlowStepHandlers";

import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { IteratorStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

import { useConfigurationFlowContext } from "../../ConfigurationFlowContext";
import { ConfigurationFlowVariable } from "../../FlowBuilder/ConfigurationFlowVariable/ConfigurationFlowVariable";
import { CanvasType } from "../CanvasType";
import { FlowAddButton } from "../FlowAddButton/FlowAddButton";
import { useFlowCanvas } from "../FlowCanvasContext";
import { findConfigurationStep } from "../FlowCanvasHelper";
import "./IteratorStartStep.scss";

export const IteratorStartStepNode = (props: NodeProps) => {
  const outgoingConnections = useNodeConnections({
    id: props.id,
    handleType: "source"
  });

  const { handleAddFlowStep, handlePasteFlowStep } = useFlowStepHandlers();
  const { path, canvasType } = useFlowCanvas();
  const { configurationFlow, variablesByPath, variablesByName } =
    useConfigurationFlowContext();

  const iteratorStep = useMemo(() => {
    if (!configurationFlow) {
      return;
    }
    const pathToIteratorParent = path.slice(0, -2);
    const stepId = pathToIteratorParent.pop();
    if (!stepId) {
      return;
    }
    const iteratorStep = findConfigurationStep(stepId, configurationFlow);
    return iteratorStep as FlowStep<IteratorStepProperties>;
  }, [configurationFlow, path]);

  const variables = useMemo(() => {
    if (!iteratorStep || !variablesByPath) {
      return <></>;
    }

    const startingVariables = getIteratorStartingVariables(
      iteratorStep,
      variablesByPath,
      variablesByName
    );

    return Object.values(startingVariables).map(variable => (
      <ConfigurationFlowVariable
        key={`iterator-variable-${variable.id ?? variable.identifier}-${iteratorStep.id}`}
        variable={variable}
      />
    ));
  }, [iteratorStep, variablesByPath, variablesByName]);

  if (!iteratorStep) {
    return <></>;
  }

  return (
    <Box className="nodrag nopan">
      <WhiteboardBlock className="iterator-start-step">
        <Stack gap="100" style={{ minWidth: "fit-content" }}>
          <Inline gap="050" alignment="left">
            <Icon size="m" name="play_circle" color="color" />
            <Heading size="xs">Iterator starting variables</Heading>
          </Inline>
          <Inline
            gap="050"
            wrap
            alignment="left"
            style={{ minWidth: "fit-content" }}
          >
            {variables}
          </Inline>
        </Stack>
      </WhiteboardBlock>
      {outgoingConnections.length === 0 &&
        canvasType === CanvasType.CONFIGURATION && (
          <Stack alignment="center">
            <WhiteboardLine className="flow-step-last-edge" />
            <FlowAddButton
              addStep={variant => {
                handleAddFlowStep({
                  variant,
                  parentStepId: undefined,
                  branchIndex: 0,
                  path
                });
              }}
              pasteStep={async clipboardStep => {
                handlePasteFlowStep({
                  parentStepId: undefined,
                  skipTypeSelect: true,
                  clipboardStep,
                  path
                });
              }}
            />
          </Stack>
        )}
      <Handle type="source" position={Position.Bottom} style={{ opacity: 0 }} />
    </Box>
  );
};
