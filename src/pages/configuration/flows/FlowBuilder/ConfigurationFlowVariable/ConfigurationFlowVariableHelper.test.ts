import { describe, expect, test } from "vitest";

import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";

import {
  containsVariableReference,
  findVariableReferences,
  replaceVariableReference
} from "./ConfigurationFlowVariableHelper";

describe("correctly identifies references to variable from string", () => {
  test("identifies a normal variable", () => {
    const variableName = "myVariable";
    const input = "this is a string with {{myVariable}} in it";
    const result = containsVariableReference(input, variableName);
    expect(result).toBe(true);
  });

  test("identifies a variable accessing a property", () => {
    const variableName = "myVariable";
    const input = "this is a string with {{myVariable.property}} in it";
    const result = containsVariableReference(input, variableName);

    expect(result).toBe(true);
  });

  test("identifies a variable accessing a nested property", () => {
    const variableName = "myVariable";
    const input = "this is a string with {{myVariable.property.number}} in it";
    const result = containsVariableReference(input, variableName);

    expect(result).toBe(true);
  });

  test("identifies no variable", () => {
    const variableName = "myVariable";
    const input = "this is a string with no variable in it";
    const result = containsVariableReference(input, variableName);

    expect(result).toBe(false);
  });

  test("identifies no variable with empty braces", () => {
    const variableName = "myVariable";
    const input = "this is a string with no variable in it {{}}";
    const result = containsVariableReference(input, variableName);
    expect(result).toBe(false);
  });

  test("doesn't identify variable with a similar start", () => {
    const variableName = "hello";
    const input = "{{hellodawdawdawd.awdawd}}";
    const result = containsVariableReference(input, variableName);
    expect(result).toBe(false);
  });
});

describe("replaces variables in a string", () => {
  test("replaces a single variable", () => {
    const variableName = "myVariable";
    const newVariableName = "newVariable";
    const input = "this is a string with {{myVariable}} in it";
    const result = replaceVariableReference(
      input,
      variableName,
      newVariableName
    );
    expect(result).toBe("this is a string with {{newVariable}} in it");
  });

  test("replaces multiple variables", () => {
    const variableName = "myVariable";
    const newVariableName = "newVariable";
    const input =
      "this is a string with {{myVariable}} and {{myVariable}} in it";
    const result = replaceVariableReference(
      input,
      variableName,
      newVariableName
    );
    expect(result).toBe(
      "this is a string with {{newVariable}} and {{newVariable}} in it"
    );
  });

  test("replaces a variable accessing a property", () => {
    const variableName = "myVariable";
    const newVariableName = "newVariable";
    const input = "this is a string with {{myVariable.property}} in it";
    const result = replaceVariableReference(
      input,
      variableName,
      newVariableName
    );
    expect(result).toBe("this is a string with {{newVariable.property}} in it");
  });

  test("preserves existing property names", () => {
    const variableName = "oldVariable";
    const newVariableName = "newVariable";
    const input = "this is a string with {{oldVariable.oldVariable}} in it";
    const result = replaceVariableReference(
      input,
      variableName,
      newVariableName
    );
    expect(result).toBe(
      "this is a string with {{newVariable.oldVariable}} in it"
    );
  });

  test("preserves existing property names with mix of property names", () => {
    const variableName = "oldVariable";
    const newVariableName = "newVariable";
    const input =
      "this is a string with {{oldVariable.oldVariable}} {{oldVariable.name.oldVariable}} {{oldVariable}} in it";
    const result = replaceVariableReference(
      input,
      variableName,
      newVariableName
    );
    expect(result).toBe(
      "this is a string with {{newVariable.oldVariable}} {{newVariable.name.oldVariable}} {{newVariable}} in it"
    );
  });

  test("preserves variables with a similar start", () => {
    const variableName = "hello";
    const newVariableName = "newVariable";
    const input = "{{hellodawdawdawd.awdawd}}";
    const result = replaceVariableReference(
      input,
      variableName,
      newVariableName
    );
    expect(result).toBe("{{hellodawdawdawd.awdawd}}");
  });
});

const emptyFlowConfiguration: Omit<FlowConfiguration, "steps"> = {
  id: "l9OBNfm0ZgNeFeOZ0FazV",
  metadata: {
    createdAt: "2025-04-09T01:16:29.843Z",
    updatedAt: "2025-04-09T01:16:29.843Z"
  },
  name: "f1",
  startingVariables: [
    {
      identifier: "foundation",
      properties: {
        required: true
      },
      type: "foundation.{{thisStep.foundationConfigurationId}}"
    }
  ]
};

describe("finds variable references within flow configuration", () => {
  test("identifies inputs within an action step", () => {
    const flowConfiguration: FlowConfiguration = {
      ...emptyFlowConfiguration,
      steps: {
        kWtbo5q_6HoV60QP53bzC: {
          id: "kWtbo5q_6HoV60QP53bzC",
          metadata: {
            createdAt: "2025-04-11T00:18:05.562Z",
            updatedAt: "2025-04-11T00:18:05.562Z"
          },
          name: "Set form answer",
          next: "",
          properties: {
            inputs: {
              isTable: "false",
              operation: "",
              questionId: "{{textVariable}}",
              rowSelection: "",
              formId: "{{textVariable}}"
            },
            typePrimaryIdentifier: "setAnswer"
          },
          variant: "action"
        }
      }
    };
    const variableName = "textVariable";
    const variablePaths = findVariableReferences(
      flowConfiguration,
      variableName
    );
    expect(variablePaths).toEqual([
      "kWtbo5q_6HoV60QP53bzC.properties.inputs.questionId",
      "kWtbo5q_6HoV60QP53bzC.properties.inputs.formId"
    ]);
  });

  test("identifies list to iterate over in iterator step", () => {
    const flowConfiguration: FlowConfiguration = {
      ...emptyFlowConfiguration,
      steps: {
        "9F9j1M-Ysa-SOD-TcXnW7": {
          id: "9F9j1M-Ysa-SOD-TcXnW7",
          variant: "iterator",
          name: "Aggregate",
          properties: {
            typePrimaryIdentifier: "iteratorAggregate",
            inputs: {
              itemVariableName: "item_9F9j1M-Ysa-SOD-TcXnW7",
              resultVariableName: "aggregate__step_9F9j1M-Ysa-SOD-TcXnW7",
              resultVariableType: "number",
              list: "{{table}}"
            },
            configuration: {
              start: "",
              steps: {},
              startingVariables: [
                {
                  type: "json",
                  identifier: "item_9F9j1M-Ysa-SOD-TcXnW7",
                  properties: null
                },
                {
                  type: "number",
                  identifier: "item_9F9j1M-Ysa-SOD-TcXnW7_index",
                  properties: null
                },
                {
                  type: "unknown",
                  identifier: "item_9F9j1M-Ysa-SOD-TcXnW7_output",
                  properties: null
                }
              ]
            }
          },
          next: "",
          metadata: {
            createdAt: "2025-04-11T05:00:18.228Z",
            updatedAt: "2025-04-11T05:00:18.228Z"
          }
        }
      }
    };

    const variableName = "table";
    const variablePaths = findVariableReferences(
      flowConfiguration,
      variableName
    );
    expect(variablePaths).toEqual([
      "9F9j1M-Ysa-SOD-TcXnW7.properties.inputs.list"
    ]);
  });

  test("identifies nested variables in iterator step", () => {
    const flowConfiguration: FlowConfiguration = {
      ...emptyFlowConfiguration,
      steps: {
        TvUt8d1YA8_Jd3TWW9qeH: {
          id: "TvUt8d1YA8_Jd3TWW9qeH",
          variant: "iterator",
          name: "For each",
          properties: {
            typePrimaryIdentifier: "iteratorForEach",
            inputs: {
              itemVariableName: "item_TvUt8d1YA8_Jd3TWW9qeH",
              isReturn: "false",
              transformedValueType: "json",
              resultVariableName: "output__step_TvUt8d1YA8_Jd3TWW9qeH"
            },
            configuration: {
              start: "D3pJs5tHoZswwM_u9UKJs",
              steps: {
                D3pJs5tHoZswwM_u9UKJs: {
                  id: "D3pJs5tHoZswwM_u9UKJs",
                  variant: "setVariables",
                  name: "Set variable(s)",
                  properties: {
                    variables: [
                      {
                        identifier: "newvariable",
                        value: "{{textVariable}}",
                        type: "text"
                      }
                    ]
                  },
                  next: "",
                  metadata: {
                    createdAt: "2025-04-14T05:49:00.910Z",
                    updatedAt: "2025-04-14T05:49:00.910Z"
                  }
                }
              },
              startingVariables: [
                {
                  type: "json",
                  identifier: "item_TvUt8d1YA8_Jd3TWW9qeH",
                  properties: null
                },
                {
                  type: "number",
                  identifier: "item_TvUt8d1YA8_Jd3TWW9qeH_index",
                  properties: null
                },
                {
                  type: "unknown",
                  identifier: "item_TvUt8d1YA8_Jd3TWW9qeH_output",
                  properties: null
                }
              ]
            }
          },
          next: "",
          metadata: {
            createdAt: "2025-04-14T05:48:54.933Z",
            updatedAt: "2025-04-14T05:48:54.933Z"
          }
        }
      }
    };
    const variableName = "textVariable";
    const variablePaths = findVariableReferences(
      flowConfiguration,
      variableName
    );
    expect(variablePaths).toEqual([
      "TvUt8d1YA8_Jd3TWW9qeH.properties.configuration.steps.D3pJs5tHoZswwM_u9UKJs.properties.variables.0.value"
    ]);
  });

  test("identifies variables within a set variable step", () => {
    const flowConfiguration: FlowConfiguration = {
      ...emptyFlowConfiguration,
      steps: {
        "7_UwdqwcSKGf8AvSX6ZXL": {
          id: "7_UwdqwcSKGf8AvSX6ZXL",
          metadata: {
            createdAt: "2025-04-11T00:15:00.780Z",
            updatedAt: "2025-04-11T00:15:00.780Z"
          },
          name: "Set variable(s)",
          next: "4E0z52MK3DXfgcEsbNFhG",
          properties: {
            variables: [
              { type: "text", identifier: "textVariable", value: "hmm" },
              {
                type: "text",
                identifier: "anotherVariable",
                value: "{{textVariable}}"
              }
            ]
          },
          variant: "setVariables"
        }
      }
    };

    const variableName = "textVariable";
    const variablePaths = findVariableReferences(
      flowConfiguration,
      variableName
    );

    expect(variablePaths).toEqual([
      "7_UwdqwcSKGf8AvSX6ZXL.properties.variables.0.identifier",
      "7_UwdqwcSKGf8AvSX6ZXL.properties.variables.1.value"
    ]);
  });

  test("identifies variables within flat conditions", () => {
    const flowConfiguration: FlowConfiguration = {
      ...emptyFlowConfiguration,
      steps: {
        QuttjnPVWdfohSUHH_hpl: {
          id: "QuttjnPVWdfohSUHH_hpl",
          variant: "condition",
          name: "Condition",
          properties: {
            branches: [
              {
                name: "If",
                condition: { lhs: "{{textVariable}}", operator: "=", rhs: "" },
                next: null
              },
              {
                name: "If 2",
                condition: { lhs: "", operator: "=", rhs: "{{textVariable}}" },
                next: null
              }
            ]
          },
          next: "",
          metadata: {
            createdAt: "2025-04-11T07:30:02.737Z",
            updatedAt: "2025-04-11T07:30:02.737Z"
          }
        }
      }
    };
    const variableName = "textVariable";
    const variablePaths = findVariableReferences(
      flowConfiguration,
      variableName
    );

    expect(variablePaths).toEqual([
      "QuttjnPVWdfohSUHH_hpl.properties.branches.0.condition.lhs",
      "QuttjnPVWdfohSUHH_hpl.properties.branches.1.condition.rhs"
    ]);
  });

  test("identifies variables within compound conditions", () => {
    const flowConfiguration: FlowConfiguration = {
      ...emptyFlowConfiguration,
      steps: {
        "RyB7P-9oW83g62OGOtUZK": {
          id: "RyB7P-9oW83g62OGOtUZK",
          metadata: {
            createdAt: "2025-04-11T06:51:59.835Z",
            updatedAt: "2025-04-11T06:51:59.835Z"
          },
          name: "Condition",
          next: "",
          properties: {
            branches: [
              {
                condition: {
                  OR: [
                    {
                      lhs: "{{textVariable}}",
                      operator: "=",
                      rhs: "{{textVariable}}"
                    },
                    { lhs: "{{textVariable}}", operator: "=", rhs: "" }
                  ]
                },
                name: "If",
                next: null
              },
              {
                name: "If 2",
                condition: { lhs: "{{textVariable}}", operator: "=", rhs: "" },
                next: null
              }
            ]
          },
          variant: "condition"
        }
      }
    };
    const variableName = "textVariable";
    const variablePaths = findVariableReferences(
      flowConfiguration,
      variableName
    );

    expect(variablePaths).toEqual([
      "RyB7P-9oW83g62OGOtUZK.properties.branches.0.condition.OR.0.lhs",
      "RyB7P-9oW83g62OGOtUZK.properties.branches.0.condition.OR.0.rhs",
      "RyB7P-9oW83g62OGOtUZK.properties.branches.0.condition.OR.1.lhs",
      "RyB7P-9oW83g62OGOtUZK.properties.branches.1.condition.lhs"
    ]);
  });

  test("identifies condition steps", () => {
    const flowConfiguration: FlowConfiguration = {
      ...emptyFlowConfiguration,
      steps: {
        xUEcqaHG7I8_kERH0kTVn: {
          id: "xUEcqaHG7I8_kERH0kTVn",
          metadata: {
            createdAt: "2025-04-14T01:24:08.696Z",
            updatedAt: "2025-04-14T01:24:08.696Z"
          },
          name: "Condition",
          next: "",
          properties: {
            branches: [
              {
                condition: {
                  AND: [
                    { lhs: "haha", operator: "=", rhs: "haha" },
                    { lhs: "{{textVariable}}", operator: "=", rhs: "" },
                    {
                      AND: [
                        { lhs: "{{textVariable}}", operator: "=", rhs: "2" }
                      ]
                    }
                  ]
                },
                name: "If",
                next: null
              }
            ]
          },
          variant: "condition"
        }
      }
    };

    const variableName = "textVariable";

    const paths = findVariableReferences(flowConfiguration, variableName);
    expect(paths).toEqual([
      "xUEcqaHG7I8_kERH0kTVn.properties.branches.0.condition.AND.1.lhs",
      "xUEcqaHG7I8_kERH0kTVn.properties.branches.0.condition.AND.2.AND.0.lhs"
    ]);
  });
});
