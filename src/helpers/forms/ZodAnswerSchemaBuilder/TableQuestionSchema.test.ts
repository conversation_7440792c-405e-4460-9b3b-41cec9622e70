import { describe, expect, test } from "vitest";

import { Dictionary } from "../../../hooks/useDictionary";
import { Question, QuestionTypes } from "../../../types/Question";
import {
  NumberQuestionProperties,
  TableQuestionProperties,
  TextQuestionProperties
} from "../../../types/QuestionProperties";
import {
  TableAnswer,
  TableAnswerRow
} from "../../../types/collection/CollectionForm";
import { ZodAnswerSchemaBuilder } from "./ZodAnswerSchemaBuilder";

describe("generateZodSchema for Table questions", () => {
  test("should generate schema for Table question with text and number columns", () => {
    const question: Question<TableQuestionProperties> = {
      id: "questionId1",
      type: QuestionTypes.TABLE,
      text: "Table Question",
      identifier: "identifier",
      properties: {
        columns: [
          {
            id: "col1",
            type: QuestionTypes.TEXT,
            text: "Text Column",
            identifier: "id1",
            properties: {
              minLength: 2,
              maxLength: 15,
              required: true
            } as TextQuestionProperties
          },
          {
            id: "col2",
            type: QuestionTypes.NUMBER,
            text: "Number Column",
            identifier: "id2",
            properties: {
              min: 1,
              max: 100,
              required: true
            } as NumberQuestionProperties
          }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generatedSchema;
    const result = schema.safeParse({
      questionId1: {
        order: ["row1", "row2"],
        entities: {
          row1: {
            id: "row1",
            columns: {
              col1: {
                value: "Valid Text",
                questionId: "col1",
                type: QuestionTypes.TEXT
              },
              col2: {
                value: 50,
                questionId: "col2",
                type: QuestionTypes.NUMBER
              }
            }
          } as TableAnswerRow,
          row2: {
            id: "row2",
            columns: {
              col1: {
                value: "Valid Text 2",
                questionId: "col1",
                type: QuestionTypes.TEXT
              },
              col2: {
                value: 75,
                questionId: "col2",
                type: QuestionTypes.NUMBER
              }
            }
          } as TableAnswerRow
        }
      } as TableAnswer
    });

    expect(result.success).toBe(true);
  });

  test("should fail validation for Table question with invalid text column", () => {
    const question: Question<TableQuestionProperties> = {
      id: "questionId2",
      type: QuestionTypes.TABLE,
      text: "Table Question",
      identifier: "identifier",
      properties: {
        columns: [
          {
            id: "col1",
            type: QuestionTypes.TEXT,
            text: "Text Column",
            identifier: "id1",
            properties: {
              minLength: 2,
              maxLength: 10,
              required: true
            } as TextQuestionProperties
          }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generatedSchema;
    const result = schema.safeParse({
      questionId2: {
        order: ["row1"],
        entities: {
          row1: {
            id: "row1",
            columns: {
              col1: {
                value: "T",
                questionId: "col1",
                type: QuestionTypes.TEXT
              }
            }
          } as TableAnswerRow
        }
      } as TableAnswer
    });

    expect(result.success).toBe(false);
    expect(result.error?.issues[0].code).toBe("too_small");
  });

  test("should fail validation for Table question with invalid number column", () => {
    const question: Question<TableQuestionProperties> = {
      id: "questionId3",
      type: QuestionTypes.TABLE,
      text: "Table Question",
      identifier: "identifier",
      properties: {
        columns: [
          {
            id: "col2",
            type: QuestionTypes.NUMBER,
            text: "Number Column",
            identifier: "id2",
            properties: {
              min: 1,
              max: 100,
              required: true
            } as NumberQuestionProperties
          }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generatedSchema;
    const result = schema.safeParse({
      questionId3: {
        order: ["row1"],
        entities: {
          row1: {
            id: "row1",
            columns: {
              col2: {
                value: 101,
                questionId: "col2",
                type: QuestionTypes.NUMBER
              }
            }
          } as TableAnswerRow
        }
      } as TableAnswer
    });

    expect(result.success).toBe(false);
    expect(result.error?.issues[0].code).toBe("too_big");
  });
});
