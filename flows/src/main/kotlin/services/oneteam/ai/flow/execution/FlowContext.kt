package services.oneteam.ai.flow.execution

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.serialization.json.*
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.listeners.FlowContextListener
import services.oneteam.ai.shared.JsonValue.valueOrNull
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableIdentifier
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.otSerializer

@Serializable
data class FlowContextWithLocalStep(
    val stepId: Step.Id? = null,
    val flowContext: FlowContext,
    val thisStep: MutableMap<String, JsonElement> = mutableMapOf(),
) {

    fun setThisStep(key: String, value: JsonElement) {
        thisStep[key] = value
    }

    fun deepCopy(): FlowContextWithLocalStep {
        return FlowContextWithLocalStep(
            stepId = stepId, flowContext = flowContext.copy(
                global = flowContext.global.copy(),
                variables = flowContext.variables.toMutableMap(),
                event = flowContext.event?.copy(),
            ), thisStep = thisStep.toMutableMap()
        )
    }

    fun getThisStep(key: String): JsonElement? {
        return thisStep[key]
    }

    fun getVariable(identifier: VariableIdentifier): VariableInstance? {
        return flowContext.variables[identifier]
    }

    fun containsVariable(identifier: VariableIdentifier): Boolean {
        return flowContext.variables.containsKey(identifier)
    }

    override fun toString(): String {
        return otSerializer.encodeToString(this)
    }
}

@Serializable
data class FlowContext(
    val global: GlobalVariables,
    val workspace: WorkspaceContext,
    val variables: MutableMap<VariableIdentifier, VariableInstance> = mutableMapOf(),
    val event: Event.ForApi?,
) {
    @Transient
    var listeners = mutableListOf<FlowContextListener>()

    suspend fun set(variable: VariableInstance) {
        variables[variable.identifier] = variable
        listeners.forEach { it.onVariableSet(variable) }
    }

    override fun toString(): String {
        return otSerializer.encodeToString(this)
    }

    @Serializable
    data class GlobalVariables(
        val workspaceId: Workspace.Id,
        val workspaceVersionId: WorkspaceVersion.Id,
        val tenantId: Long,
        val flowConfigurationId: FlowConfiguration.Id,
        val flowConfigurationName: FlowConfiguration.Name,
        val flowDepth: Int = 1,
    ) {
        fun toMap(): JsonElement {
            // is this good enough? or does it need customisation?
            return otSerializer.encodeToJsonElement(this)
        }
    }

    @Serializable
    data class WorkspaceContext(
        val documentId: Workspace.DocumentId?,
        val id: Workspace.Id,
        val key: Workspace.Key,
        val name: Workspace.Name,
        val workspaceFoundationId: Foundation.Id,
        var variables: Map<VariableIdentifier, VariableInstance> = mutableMapOf(),
    ) {
        fun toMap(): JsonObject {
            return JsonObject(
                mapOf(
                    "documentId" to valueOrNull(documentId?.value),
                    "id" to valueOrNull(id.value),
                    "key" to valueOrNull(key.value),
                    "name" to valueOrNull(name.value),
                    "workspaceFoundationId" to valueOrNull(workspaceFoundationId.value),
                    "variables" to otSerializer.encodeToJsonElement(variables.map { it.key to it.value.get() }.toMap()),
                )
            )
        }
    }

    companion object {


        // todo use enumeration for type
        @OptIn(ExperimentalSerializationApi::class)
        fun toJsonElement(value: Any?, type: VariableDataType): JsonElement {
            if (value == null) {
                return JsonNull
            }
            if (value is String && value.isBlank()) {
                if (type == VariableDataType.LIST) {
                    return JsonArray(emptyList())
                } else if (type == VariableDataType.TABLE || type == VariableDataType.JSON) {
                    return JsonObject(emptyMap())
                }
                return JsonPrimitive(null)
            }
            if (value is JsonElement) {
                return value
            }

            // todo can this all be removed?
            when (type) {
                is VariableDataType.TEXT -> return JsonPrimitive(value.toString())
                is VariableDataType.SELECT -> return JsonPrimitive(value.toString())
                is VariableDataType.DATE -> return JsonPrimitive(value.toString())
                is VariableDataType.NUMBER -> {
                    return try {
                        JsonPrimitive(
                            value.toString().toBigDecimal()
                        ) // how to distinguish between int/long and double? - we need properties or more types?
                    } catch (e: NumberFormatException) {
                        return JsonPrimitive(0)
                    }
                }

                is VariableDataType.BOOLEAN -> return JsonPrimitive(value.toString().toBoolean())
                is VariableDataType.LIST -> return JsonArray((value as List<*>).map {
                    TypeToJsonElementConverter.toJsonElement(
                        it
                    )
                }.toList())

                is VariableDataType.TABLE, VariableDataType.JSON, VariableDataType.MULTISELECT, VariableDataType.SECTION, VariableDataType.VARIABLE -> return TypeToJsonElementConverter.toJsonElement(
                    value
                )

                is VariableDataType.FILES -> return TypeToJsonElementConverter.toJsonElement(value)
                is VariableDataType.SCHEMA -> return TypeToJsonElementConverter.toJsonElement(value)
                is VariableDataType.CUSTOM -> TODO()
            }
        }
    }
}









