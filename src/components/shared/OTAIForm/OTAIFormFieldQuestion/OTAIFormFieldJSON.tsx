import React from "react";
import { useFormContext } from "react-hook-form";

import { Box, JsonEditor, Label, Stack } from "@oneteam/onetheme";

import { generateSchema, getAnswersAsJson } from "@helpers/jsonEditorHelper";

import { Question } from "@src/types/Question";
import { JSONQuestionProperties } from "@src/types/QuestionProperties";

import { OTAIFormFieldCommonProps } from "../OTAIFormType";

export const OTAIFormFieldJSON = ({
  id,
  question,
  commonProps
}: {
  id: string;
  question: Question<JSONQuestionProperties>;
  commonProps: OTAIFormFieldCommonProps;
}) => {
  const { watch } = useFormContext();
  const answers = watch(id) ?? [{}];

  return (
    <Stack gap="025">
      <Label
        label={commonProps.label ?? ""}
        description={commonProps.description}
        required={commonProps.required}
      />

      <Box style={{ maxHeight: "20vh" }} overflow="auto">
        <JsonEditor
          localJSONContent={JSON.stringify(
            getAnswersAsJson(question.properties, answers) ?? {},
            null,
            2
          )}
          disabled={false}
          schema={generateSchema(question)}
          height="200px"
        />
      </Box>
    </Stack>
  );
};
