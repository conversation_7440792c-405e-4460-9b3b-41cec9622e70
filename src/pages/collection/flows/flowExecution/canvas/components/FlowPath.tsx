import React, { <PERSON>actN<PERSON>, useCallback, useMemo, useState } from "react";

import {
  Box,
  Button,
  Divider,
  DropdownItem,
  IconButton,
  Inline,
  OpenCloseIcon,
  Pill,
  Stack,
  Text
} from "@oneteam/onetheme";

import { FlowStepVariantPill } from "@components/flows/FlowStepVariantPill/FlowStepVariantPill";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { FieldDivider } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowStepModal/ConfigurationFlowBlockFields/FieldDivider";

import { useDictionary } from "@src/hooks/useDictionary";
import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowExecutionDocument,
  FlowResult
} from "@src/types/FlowConfiguration/FlowExecution";
import {
  FlowStep,
  FlowStepId,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { useFlowExecutionContext } from "../../FlowExecutionContext";
import { getNestedFlowExecution } from "../../helpers/ navigationHelper";
import { useExecutionStepMetadata } from "../../hooks/useExecutionMetadata";
import { useFlowExecutionNavigationHelper } from "../../hooks/useFlowExecutionNavigationHelper";
import "./FlowPath.scss";

export const FlowPathItem = ({
  configurationStep,
  rightElementOverride,
  executionFlow: flowExecution,
  iteratorId,
  disableExpanding,
  pathToCurrentExecution = []
}: {
  configurationStep: FlowStep;
  executionFlow: FlowExecutionDocument;
  pathToCurrentExecution?: string[];
  rightElementOverride?: ReactNode;
  iteratorId?: FlowStepId;
  disableExpanding?: boolean;
}) => {
  const { path, setPath } = useConfigurationFlowContext();
  const {
    setSelectedStepId,
    executionFlowForModal,
    executionFlow,
    setExecutionFlowForModal,
    workspaceDocument
  } = useFlowExecutionContext();

  const [isOpen, setIsOpen] = useState(false);

  const metadata = useExecutionStepMetadata(
    configurationStep.id,
    flowExecution,
    path ?? []
  );

  const { navigateToSubFlowViaPath } = useFlowExecutionNavigationHelper();

  const selectStep = useCallback(() => {
    if (!executionFlowForModal && setExecutionFlowForModal) {
      setExecutionFlowForModal(executionFlow);
    }
    setSelectedStepId(configurationStep.id);

    if (pathToCurrentExecution.length > 0 && executionFlow) {
      navigateToSubFlowViaPath(pathToCurrentExecution);
    }

    if (!iteratorId) {
      return;
    }

    const newPath = (path ?? []).concat(
      "steps",
      iteratorId,
      "properties",
      "configuration"
    );

    setPath(newPath);
  }, [
    setSelectedStepId,
    configurationStep?.id,
    iteratorId,
    path,
    setPath,
    pathToCurrentExecution,
    executionFlow,
    executionFlowForModal,
    setExecutionFlowForModal,
    navigateToSubFlowViaPath
  ]);

  const variant =
    metadata?.result === FlowResult.SUCCESS ||
    configurationStep.variant === FlowStepVariant.TRIGGER
      ? "success"
      : "danger";

  const nestedExecutionFlow = useMemo(() => {
    if (configurationStep.variant !== FlowStepVariant.FLOW || !flowExecution) {
      return undefined;
    }

    return flowExecution.steps[configurationStep.id]?.subFlows?.[
      configurationStep.id
    ];
  }, [configurationStep, flowExecution]);

  const configurationFlow = useMemo(() => {
    if (!nestedExecutionFlow) {
      return undefined;
    }

    return workspaceDocument?.flows?.entities?.[
      nestedExecutionFlow.context.global.flowConfigurationId
    ];
  }, [workspaceDocument?.flows?.entities, nestedExecutionFlow]);

  if (!configurationStep?.variant) {
    return <></>;
  }

  return (
    <Stack>
      <Inline alignment="center">
        {configurationStep.variant === FlowStepVariant.FLOW &&
          !disableExpanding && (
            <Box onClick={() => setIsOpen(!isOpen)}>
              <OpenCloseIcon isOpen={isOpen} />
            </Box>
          )}
        <DropdownItem
          className="flow-path-item"
          onClick={selectStep}
          leftElement={
            <FlowStepVariantPill iconOnly variant={configurationStep.variant} />
          }
          rightElement={
            rightElementOverride ?? (
              <Pill
                background="icon-only"
                label="s"
                variant={variant}
                icon="standard"
              />
            )
          }
        >
          <Inline gap="050">
            <Text truncate>{configurationStep.name}</Text>
            {configurationStep.variant === FlowStepVariant.FLOW &&
              nestedExecutionFlow && (
                <>
                  <Pill
                    label={String(nestedExecutionFlow.state.steps.order.length)}
                  />
                  <IconButton
                    name="open_in_new"
                    onClick={e => {
                      e.stopPropagation();
                      navigateToSubFlowViaPath([
                        ...pathToCurrentExecution,
                        "steps",
                        configurationStep.id,
                        "subFlows",
                        configurationStep.id
                      ]);
                    }}
                  />
                </>
              )}
          </Inline>
        </DropdownItem>
      </Inline>
      {configurationStep.variant === FlowStepVariant.FLOW &&
        configurationFlow &&
        executionFlow &&
        nestedExecutionFlow &&
        !disableExpanding &&
        isOpen && (
          <Box paddingLeft="200">
            <FlowPath
              hideTitle
              configurationFlow={configurationFlow}
              flowExecution={nestedExecutionFlow}
              pathToCurrentExecution={[
                ...pathToCurrentExecution,
                "steps",
                configurationStep.id,
                "subFlows",
                configurationStep.id
              ]}
            />
          </Box>
        )}
    </Stack>
  );
};

export const FlowPath = ({
  configurationFlow,
  flowExecution,
  iteratorId,
  hideTitle,
  pathToCurrentExecution = [],
  showBreadcrumb
}: {
  configurationFlow: FlowConfiguration;
  flowExecution: FlowExecutionDocument;
  pathToCurrentExecution?: string[];
  iteratorId?: FlowStepId;
  hideTitle?: boolean;
  showBreadcrumb?: boolean;
}) => {
  const d = useDictionary();

  const {
    pathToCurrentExecution: pathToCurrentExecutionFromContext,
    rootExecutionFlow
  } = useFlowExecutionContext();

  const { navigateToPreviousExecution } = useFlowExecutionNavigationHelper();

  const orderedConfigurationSteps = useMemo(() => {
    if (!flowExecution?.state.steps.order || !configurationFlow) {
      return [];
    }

    const executedSteps = flowExecution.state.steps.order
      .map(stepId => configurationFlow.steps[stepId])
      .filter(step => step !== undefined);

    if (flowExecution.trigger) {
      const triggerStep =
        configurationFlow.triggers?.[flowExecution.trigger.id];
      if (!triggerStep) {
        return executedSteps;
      }
      executedSteps.unshift(triggerStep);
    }

    return executedSteps;
  }, [configurationFlow, flowExecution]);

  const previousExecutionName = useMemo(() => {
    const truncatedPath = pathToCurrentExecutionFromContext?.slice(0, -4);

    if (
      !rootExecutionFlow ||
      !truncatedPath ||
      !pathToCurrentExecutionFromContext
    ) {
      return "";
    }
    const previousExecution = getNestedFlowExecution(
      rootExecutionFlow,
      truncatedPath
    );

    return previousExecution?.context.global.flowConfigurationName;
  }, [pathToCurrentExecutionFromContext, rootExecutionFlow]);

  return (
    <Stack gap="050" width="fill" contentsWidth="fill" alignment="left">
      {!hideTitle && (
        <FieldDivider
          label={d("ui.collection.flowExecution.stepModal.flowPath.title")}
        />
      )}
      {showBreadcrumb &&
        (pathToCurrentExecutionFromContext ?? []).length > 0 && (
          <Inline>
            <Button
              onClick={navigateToPreviousExecution}
              leftIcon={{ name: "arrow_insert" }}
              variant="text"
              label={d(
                "ui.collection.flowExecution.stepModal.summary.backToExecutionButton",
                { executionName: previousExecutionName }
              )}
            />
          </Inline>
        )}

      {orderedConfigurationSteps.map((step, index) => {
        return (
          <Stack key={step.id} gap="050">
            <FlowPathItem
              configurationStep={step}
              executionFlow={flowExecution}
              iteratorId={iteratorId}
              pathToCurrentExecution={pathToCurrentExecution}
            />
            {index !== orderedConfigurationSteps.length - 1 && (
              <Divider size="small" />
            )}
          </Stack>
        );
      })}
    </Stack>
  );
};
