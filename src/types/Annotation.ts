import { Actor } from "./Actor.ts";
import {
  AnnotationLocation,
  AnswerLocation,
  HighlightLocation,
  QuestionLocation,
  WhiteboardCoordinateLocation
} from "./AnnotationLocation.ts";

export type AnnotationAttachment = {
  path: string;
  name: string;
};
export type AnnotationAttachments = AnnotationAttachment[];

export enum AnnotationResolvedStatus {
  RESOLVED = "resolved",
  UNRESOLVED = "unresolved"
}

export enum AnnotationVariant {
  COMMENT = "comment",
  ALERT = "alert",
  HIGHLIGHT = "highlight"
}

export type BaseAnnotation = {
  id: string;
  variant: `${AnnotationVariant}`;
  message: string;
  attachments?: AnnotationAttachments;
  createdAt: string;
  updatedAt: string;
  createdBy: Actor;
  updatedBy: Actor;
  replies?: {
    [id: string]: AnnotationReply;
  };
};

export type AnnotationReply = BaseAnnotation & {
  variant: `${AnnotationVariant.COMMENT}`;
};

export type BaseAnnotationWithResolved = BaseAnnotation & {
  // Alternative to just have status AnnotationStatus on the annotation and the
  //  AnnotationReply can come with status to "change" it so we track who did the status change and when??
  resolved?: {
    status: `${AnnotationResolvedStatus}`;
    actor: Actor;
  };
};

export type HighlightAnnotation = BaseAnnotationWithResolved & {
  variant: `${AnnotationVariant.HIGHLIGHT}`;
  location: HighlightLocation;
};

export type CommentAnnotation = BaseAnnotationWithResolved & {
  variant: `${AnnotationVariant.COMMENT}`;
  location?: QuestionLocation | AnswerLocation | WhiteboardCoordinateLocation;
};

export enum AlertType {
  BLOCKER = "blocker",
  WARNING = "warning",
  INFO = "info",
  SUCCESS = "success"
}

export type AlertAnnotation = BaseAnnotationWithResolved & {
  variant: `${AnnotationVariant.ALERT}`;
  type: AlertType;
  location: QuestionLocation;
  groupIdentifier?: string;
};

export type Annotation =
  | AlertAnnotation
  | CommentAnnotation
  | HighlightAnnotation;

export type CreateAnnotationHandler = ({
  annotation: {
    variant,
    location,
    message,
    attachments,
    type,
    groupIdentifier
  },
  parentAnnotationId
}: {
  annotation: {
    variant: Annotation["variant"];
    message: Annotation["message"];
    location?: AnnotationLocation;
    attachments?: Annotation["attachments"];
    type?: AlertAnnotation["type"];
    groupIdentifier?: AlertAnnotation["groupIdentifier"];
  };
  parentAnnotationId?: Annotation["id"];
}) => Annotation["id"] | undefined;

export type UpdateAnnotationHandler = ({
  annotation: { id, variant, message, attachments, type, groupIdentifier },
  parentAnnotationId
}: {
  annotation: {
    id: Annotation["id"];
    variant: Annotation["variant"];
    message: Annotation["message"];
    location?: AnnotationLocation;
    attachments?: Annotation["attachments"];
    type?: AlertAnnotation["type"];
    groupIdentifier?: AlertAnnotation["groupIdentifier"];
  };
  parentAnnotationId?: string;
}) => void;

export type DeleteAnnotationHandler = ({
  annotationId,
  parentAnnotationId
}: {
  annotationId: Annotation["id"];
  parentAnnotationId?: Annotation["id"];
}) => void;

export type ChangeResolveAnnotationHandler = ({
  annotationId,
  parentAnnotationId,
  isResolved
}: {
  annotationId: Annotation["id"];
  parentAnnotationId?: Annotation["id"];
  isResolved: boolean;
}) => void;

export type GetAnnotationLocationDisplay = (
  location: Annotation["location"]
) => string | undefined;
