import React, { useMemo, useState } from "react";

import { Stack, TabGroup } from "@oneteam/onetheme";

import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import {
  TableDefaultView,
  TableQuestionProperties
} from "@src/types/QuestionProperties";

export const QuestionTablePreview = ({
  question,
  parentQuestion
}: {
  question: Question<TableQuestionProperties>;
  parentQuestion?: Question<TableQuestionProperties>;
}) => {
  const [tablePreviewPanelTab, setTablePreviewPanelTab] = useState<string>(
    question?.properties?.defaultView ?? TableDefaultView.ALL
  );

  const d = useDictionary();

  const defaultViewOptions = Object.values(TableDefaultView).map(value => ({
    value,
    label: d(
      `ui.configuration.forms.question.table.visibility.defaultView.tabs.${value}`
    )
  }));

  const chartsCanBeDisplayed = useMemo(() => {
    return (
      question?.properties?.charts?.filter(
        chart => !chart.properties?.hidden
      ) ?? []
    );
  }, [question]);

  return (
    <Stack className="question-table-preview__container" gap="150">
      <Stack className="question-table-preview__header">
        <TabGroup
          options={defaultViewOptions}
          value={tablePreviewPanelTab}
          handleChange={setTablePreviewPanelTab}
          className="question-table-preview__tabs"
        />
      </Stack>

      {(tablePreviewPanelTab === TableDefaultView.ALL ||
        tablePreviewPanelTab === TableDefaultView.CHART) && (
        <>
          {chartsCanBeDisplayed?.map(chart => (
            <QuestionAnswer
              key={chart.id}
              question={chart}
              answer={undefined}
              answerAccessor={""}
              parentQuestion={question}
            />
          ))}
        </>
      )}
      {(tablePreviewPanelTab === TableDefaultView.ALL ||
        tablePreviewPanelTab === TableDefaultView.TABLE) && (
        <QuestionAnswer
          question={question}
          answer={undefined}
          answerAccessor={""}
          parentQuestion={parentQuestion}
        />
      )}
    </Stack>
  );
};
