import { describe, expect, test } from "vitest";

import { FlowConfiguration } from "../../../../types/FlowConfiguration/FlowConfiguration.ts";
import { CanvasType } from "./CanvasType.ts";
import {
  FlowBuilder,
  findConfigurationStep,
  getParentPath
} from "./FlowCanvasHelper.ts";

const mockFlow: FlowConfiguration = {
  id: "1",
  name: "Mock Flow",
  metadata: {
    createdAt: "2021-08-01T00:00:00Z",
    updatedAt: "2021-08-01T00:00:00Z"
  },
  triggers: {
    "123Trigger": {
      id: "123Trigger",
      metadata: {
        createdAt: "2021-08-01T00:00:00Z",
        updatedAt: "2021-08-01T00:00:00Z"
      },
      properties: {
        typeId: 1
      }
    }
  },
  start: "A1id",
  steps: {
    A1id: {
      id: "A1id",
      variant: "action",
      properties: {
        typeId: 1
      },
      next: "A2id",
      name: "Process first name",
      metadata: {
        createdAt: "2021-08-01T00:00:00Z",
        updatedAt: "2021-08-01T00:00:00Z"
      }
    },
    A2id: {
      id: "A2id",
      variant: "action",
      properties: {
        typeId: 2
      },
      name: "Process last name",
      metadata: {
        createdAt: "2021-08-01T00:00:00Z",
        updatedAt: "2021-08-01T00:00:00Z"
      }
    },
    ConditionStep: {
      id: "ConditionStep",
      variant: "condition",
      name: "Check variables exist",
      properties: {
        branches: [
          {
            name: "Check if x exists",
            next: undefined
          },
          {
            name: "Check if y exists",
            next: undefined
          }
        ]
      },
      next: undefined,
      metadata: {
        createdAt: "2021-08-01T00:00:00Z",
        updatedAt: "2021-08-01T00:00:00Z"
      }
    }
  }
};

const canvasType = CanvasType.CONFIGURATION;

describe("Build nodes and edges", () => {
  test("should handle an empty flow", () => {
    const flowConfig: FlowConfiguration = {
      steps: {},
      triggers: {},
      start: "",
      id: "1",
      metadata: { createdAt: "", updatedAt: "" },
      name: ""
    };
    const flowBuilder = new FlowBuilder(canvasType, flowConfig);
    const nodes = flowBuilder.forTesting.buildNodesFromDocument();
    const edges = flowBuilder.forTesting.buildEdgesFromDocument();
    expect(nodes.length).toEqual(1);
    expect(edges.length).toEqual(0);
  });

  test("should return the trigger node", () => {
    const flowBuilder = new FlowBuilder(canvasType, mockFlow);
    const nodes = flowBuilder.forTesting.buildNodesFromDocument();
    expect(nodes.length).toBeGreaterThan(0);
    console.log(nodes);
  });
});

describe("Build edges list", () => {
  test("should return edge between start trigger and start", () => {
    const flowBuilder = new FlowBuilder(canvasType, mockFlow);
    const edges = flowBuilder.forTesting.buildEdgesFromDocument();
    expect(edges.length).toBeGreaterThan(0);
    console.log(edges);
  });
});

describe("Build condition node ", () => {
  test("should handle an empty configuration", () => {
    const flowConfig: FlowConfiguration = {
      steps: {},
      triggers: {},
      start: "",
      id: "1",
      metadata: { createdAt: "", updatedAt: "" },
      name: ""
    };

    const flowBuilder = new FlowBuilder(canvasType, flowConfig);
    const { branchNodes: conditionNodes, branchEdges: conditionEdges } =
      flowBuilder.forTesting.buildConditions();
    expect(conditionNodes).toEqual([]);
    expect(conditionEdges).toEqual([]);
  });

  test("should handle a condition with dead-end branches", () => {
    const flowBuilder = new FlowBuilder(canvasType, mockFlow);
    const { branchNodes: conditionNodes, branchEdges: conditionEdges } =
      flowBuilder.forTesting.buildConditions();
    expect(conditionNodes.length).toEqual(4);
    expect(conditionEdges.length).toBeGreaterThan(0);
  });

  test("should handle a condition with no branches", () => {
    const flowConfig: FlowConfiguration = {
      steps: {
        ConditionStep: {
          id: "ConditionStep",
          variant: "condition",
          name: "Check variables exist",
          properties: {
            branches: []
          },
          metadata: {
            createdAt: "2021-08-01T00:00:00Z",
            updatedAt: "2021-08-01T00:00:00Z"
          },
          next: undefined
        }
      },
      triggers: {},
      start: "",
      id: "1",
      metadata: { createdAt: "", updatedAt: "" },
      name: ""
    };

    const flowBuilder = new FlowBuilder(canvasType, flowConfig);

    const { branchNodes, branchEdges } =
      flowBuilder.forTesting.buildConditions();

    expect(branchNodes.length).toEqual(2);
    expect(branchEdges.length).toEqual(1);
  });

  test("should handle a condition with no dead-end branches", () => {
    const flowConfig: FlowConfiguration = {
      steps: {
        ConditionStep: {
          id: "ConditionStep",
          variant: "condition",
          name: "Check variables exist",
          properties: {
            branches: [
              {
                name: "Check if x exists",
                next: "update X answer"
              },
              {
                name: "Check if y exists",
                next: "update Y answer"
              }
            ]
          },
          metadata: {
            createdAt: "2021-08-01T00:00:00Z",
            updatedAt: "2021-08-01T00:00:00Z"
          },
          next: "Else execute this"
        }
      },
      triggers: {},
      start: "",
      id: "1",
      metadata: { createdAt: "", updatedAt: "" },
      name: ""
    };
    const flowBuilder = new FlowBuilder(canvasType, flowConfig);
    const { branchNodes, branchEdges } =
      flowBuilder.forTesting.buildConditions();
    expect(branchNodes.length).toEqual(1);
    expect(branchEdges.length).toEqual(3);
  });
});

describe("Get parent path", () => {
  test("works in iterator step path", () => {
    const path = [
      "steps",
      "wCXhFIHQElffi6GXpYOa4",
      "properties",
      "configuration"
    ];
    const parentPath = getParentPath(path);
    expect(parentPath.length).toEqual(0);
  });

  test("works for an empty path", () => {
    const path: string[] = [];
    const parentPath = getParentPath(path);
    expect(parentPath.length).toEqual(0);
  });

  test("returns input when provided path is incorrect", () => {
    const path = ["steps", "wCXhFIHQElffi6GXpYOa4", "properties"];
    const parentPath = getParentPath(path);
    expect(parentPath.length).toEqual(path.length);
  });

  test("works for nested iterator", () => {
    const path = [
      "steps",
      "wCXhFIHQElffi6GXpYOa4",
      "properties",
      "configuration",
      "steps",
      "Kjdf2839fjHQ3436GXpYO",
      "properties",
      "configuration"
    ];
    const parentPath = getParentPath(path);
    expect(parentPath.length).toEqual(4);
    expect(parentPath).toEqual([
      "steps",
      "wCXhFIHQElffi6GXpYOa4",
      "properties",
      "configuration"
    ]);
  });
});

describe("Find step with path", () => {
  test("Finds trigger step", () => {
    const id = "123Trigger";

    const step = findConfigurationStep(id, mockFlow);
    const expectedStep = {
      id: "123Trigger",
      metadata: {
        createdAt: "2021-08-01T00:00:00Z",
        updatedAt: "2021-08-01T00:00:00Z"
      },
      properties: {
        typeId: 1
      }
    };

    expect(step).toBeDefined();
    expect(step).toEqual(expectedStep);
  });

  test("Finds step at root", () => {
    const id = "A2id";
    const step = findConfigurationStep(id, mockFlow);
    const expectedStep = {
      id: "A2id",
      variant: "action",
      properties: {
        typeId: 2
      },
      name: "Process last name",
      metadata: {
        createdAt: "2021-08-01T00:00:00Z",
        updatedAt: "2021-08-01T00:00:00Z"
      }
    };
    expect(step).toBeDefined();
    expect(step).toEqual(expectedStep);
  });

  test("Finds step in iterator", () => {});
  const flowConfig: FlowConfiguration = {
    description: "flow1",
    endingVariables: [],
    id: "acJsqRt6ilrrouGJqKdn6",
    labels: [],
    metadata: {
      createdAt: "2025-05-23T01:21:51.995Z",
      updatedAt: "2025-05-23T01:21:51.995Z"
    },
    name: "flow1",
    start: "ahajdN0BI2oVXIAbY9NkP",
    startingVariables: [
      {
        identifier: "foundation",
        properties: {
          required: true
        },
        type: "foundation.auYesbdiOnWkong42S5Cy"
      }
    ],
    status: "active",
    steps: {
      afA3w8xS3rmXiadj8Jcuz: {
        id: "afA3w8xS3rmXiadj8Jcuz",
        metadata: {
          createdAt: "2025-07-21T05:34:54.124Z",
          updatedAt: "2025-07-21T05:34:54.124Z"
        },
        name: "For each",
        next: "",
        properties: {
          configuration: {
            start: "aBJezCQsTu1HTOb71uNN2",
            steps: {
              aBJezCQsTu1HTOb71uNN2: {
                id: "aBJezCQsTu1HTOb71uNN2",
                metadata: {
                  createdAt: "2025-07-21T05:35:23.524Z",
                  updatedAt: "2025-07-21T05:35:23.524Z"
                },
                name: "Combine Name",
                next: "",
                properties: {
                  variables: [
                    {
                      identifier: "combined_name",
                      type: "text",
                      value: ""
                    }
                  ]
                },
                variant: "setVariables"
              }
            }
          },
          inputs: {
            isReturn: "false",
            itemVariableName: "employee",
            list: "{{employees}}",
            resultVariableName: "output__step_afA3w8xS3rmXiadj8Jcuz",
            transformedValueType: "json"
          },
          typePrimaryIdentifier: "iteratorForEach"
        },
        variant: "iterator"
      },
      ahajdN0BI2oVXIAbY9NkP: {
        id: "ahajdN0BI2oVXIAbY9NkP",
        metadata: {
          createdAt: "2025-07-21T05:34:55.556Z",
          updatedAt: "2025-07-21T05:34:55.556Z"
        },
        name: "Est. employee list",
        next: "afA3w8xS3rmXiadj8Jcuz",
        properties: {
          variables: [
            {
              identifier: "employees",
              properties: {
                listOperation: "setList"
              },
              type: "list",
              value: ""
            }
          ]
        },
        variant: "setVariables"
      }
    },
    triggers: {
      awwo5q1B347TFOf3rPYt4: {
        id: "awwo5q1B347TFOf3rPYt4",
        metadata: {
          createdAt: "2025-05-23T01:21:53.794Z",
          updatedAt: "2025-05-23T01:21:53.794Z"
        },
        name: "Manually trigger from a foundation",
        next: "",
        properties: {
          inputs: {
            buttonLabel: "Global",
            foundationConfigurationId: "auYesbdiOnWkong42S5Cy",
            foundationVariableName: "foundation"
          },
          typePrimaryIdentifier: "manualTriggerFromFoundation"
        },
        variant: "trigger"
      }
    }
  };
  const id = "aBJezCQsTu1HTOb71uNN2";
  const path = [
    "steps",
    "afA3w8xS3rmXiadj8Jcuz",
    "properties",
    "configuration"
  ];
  const step = findConfigurationStep(id, flowConfig, path);
  const expected = {
    id: "aBJezCQsTu1HTOb71uNN2",
    metadata: {
      createdAt: "2025-07-21T05:35:23.524Z",
      updatedAt: "2025-07-21T05:35:23.524Z"
    },
    name: "Combine Name",
    next: "",
    properties: {
      variables: [
        {
          identifier: "combined_name",
          type: "text",
          value: ""
        }
      ]
    },
    variant: "setVariables"
  };
  expect(step).toBeDefined();
  expect(step).toEqual(expected);
});
