import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";

import {
  <PERSON><PERSON>,
  Alert<PERSON>t,
  Box,
  Button,
  ButtonGroup,
  Form,
  Inline,
  Label,
  Loading,
  Modal,
  ModalDialog,
  Overlay,
  Stack,
  Text,
  TextField
} from "@oneteam/onetheme";

import { CustomError } from "@helpers/errorHelper";

import { OTAIFormFieldQuestion } from "@components/shared/OTAIForm/OTAIFormFieldQuestion";

import { commonIcons } from "@src/constants/iconConstants";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import {
  ApiKeyForCreate,
  RevealedApiKey,
  getApiKeyForCreateSchema
} from "@src/types/ApiKeys";
import { Question } from "@src/types/Question";

import { useCreateApiKey } from "./helpers/apiKeysService";

export type ApiKeyGenerateModalProps = {
  onOpenChange: (isOpen: boolean) => void;
};

const crudFields = (d: Dictionary): Question[] => {
  return [
    {
      description: "",
      id: "name",
      identifier: "name",
      properties: {
        placeholder: "",
        required: true,
        defaultValue: ""
      },
      text: d("ui.appSettings.apiKeys.fields.name.label"),
      type: "text"
    },
    {
      description: "",
      id: "description",
      identifier: "description",
      properties: {
        placeholder: "",
        required: false,
        defaultValue: ""
      },
      text: d("ui.appSettings.apiKeys.fields.description.label"),
      type: "text"
    }
  ];
};

// user can close the modal at any time
// creation is handled inside the modal as there's two states
export const ApiKeyGenerateModal = ({
  onOpenChange
}: ApiKeyGenerateModalProps) => {
  const d = useDictionary();
  const [revealedApiKey, setRevealedApiKey] = useState<RevealedApiKey | null>(
    null
  );

  const [serverError, setServerError] = useState<CustomError | null>(null);
  const [errorsByField, setErrorsByField] = useState<Record<string, string>>(
    {}
  );

  const { mutateAsync: createApiKey, isPending } = useCreateApiKey();

  const handleSubmit = useCallback(
    async (data: ApiKeyForCreate) => {
      setErrorsByField({});
      if (isPending) {
        return;
      }
      createApiKey(data)
        .then(createdApiKey => {
          setRevealedApiKey(createdApiKey);
        })
        .catch((error: CustomError) => {
          if (error.errors?.length) {
            error.errors?.forEach(err => {
              if (err.field && err.key) {
                setErrorsByField(prev => ({
                  ...prev,
                  [err.field]: d(err.key)
                }));
              }
            });
            return;
          }
          setServerError(error);
        });
    },
    [createApiKey, d, isPending]
  );

  return (
    <ModalDialog isOpen onOpenChange={onOpenChange}>
      <Overlay isOpen />

      {revealedApiKey ? (
        <PostCreateModalBody
          revealedApiKey={revealedApiKey}
          onClose={() => {
            setRevealedApiKey(null);
            onOpenChange(false);
          }}
        />
      ) : (
        <CreateModalBody
          onClose={() => {
            setRevealedApiKey(null);
            onOpenChange(false);
          }}
          handleSubmit={handleSubmit}
          isPending={isPending}
          error={serverError}
          errorsByField={errorsByField}
        />
      )}
    </ModalDialog>
  );
};

const CreateModalBody = ({
  onClose,
  handleSubmit,
  isPending,
  errorsByField,
  error
}: {
  onClose: () => void;
  handleSubmit: (data: ApiKeyForCreate) => Promise<void>;
  isPending: boolean;
  errorsByField: Record<string, string>;
  error: CustomError | null;
}) => {
  const d = useDictionary();
  const formFields = useMemo(() => crudFields(d), [d]);

  return (
    <Modal
      heading={d("ui.appSettings.apiKeys.create.title")}
      onOpenChange={onClose}
      style={{ width: "25vw", minWidth: "400px" }}
    >
      <Stack>
        <Form<ApiKeyForCreate>
          schema={getApiKeyForCreateSchema(d)}
          submitLabel={d("ui.common.save")}
          handleSubmit={handleSubmit}
          cancelLabel={d("ui.common.cancel")}
          handleCancel={onClose}
          disabled={isPending}
          d={d}
        >
          {formFields.map((field: Question) => (
            <OTAIFormFieldQuestion
              key={field.id}
              question={field}
              onlyTriggerChangeWhenBlur={false}
              error={errorsByField[field.id] ?? ""}
            />
          ))}
          {error && (
            <Alert variant={AlertVariant.DANGER} width="100">
              {d("ui.appSettings.apiKeys.create.failed")}
            </Alert>
          )}
          {isPending && <Loading size={24} />}
        </Form>
      </Stack>
    </Modal>
  );
};

const PostCreateModalBody = ({
  revealedApiKey,
  onClose
}: {
  revealedApiKey: RevealedApiKey;
  onClose: () => void;
}) => {
  const d = useDictionary();
  const [didCopy, setDidCopy] = React.useState(false);

  const timerRef = React.useRef<NodeJS.Timeout | null>(null);
  const isMounted = React.useRef(true);
  const formFields = useMemo(() => crudFields(d), [d]);

  if (revealedApiKey === undefined) {
    return <></>;
  }

  const clickToCopy = (textToCopy: string) => {
    navigator.clipboard.writeText(textToCopy);
    setDidCopy(true);

    if (timerRef?.current) {
      clearTimeout(timerRef?.current);
    }

    timerRef.current = setTimeout(() => {
      if (!isMounted.current) {
        return;
      }
      setDidCopy(false);
    }, 2000);
  };

  return (
    <Modal
      heading={d("ui.appSettings.apiKeys.postCreate.title")}
      onOpenChange={onClose}
      style={{ width: "25vw", minWidth: "400px" }}
    >
      <Stack gap="200">
        {formFields.map((field: Question) => (
          <TextField
            key={field.id}
            label={field.text}
            value={
              revealedApiKey[field.identifier as keyof ApiKeyForCreate] ?? ""
            }
            width="100"
            allowClear={false}
            disabled
          />
        ))}

        <Stack gap="025">
          {/* NB: styling needs to be look more consistent. look like an input field but monospace and uneditable */}
          <Label label={d("ui.appSettings.apiKeys.fields.apiKey.label")} />
          <Inline gap="100" alignment="center" width="100">
            <Box
              className="app-settings-api-keys__revealed-api-key"
              padding="075"
              width="100"
            >
              <Text color="text-secondary" style={{ userSelect: "all" }}>
                <code>{revealedApiKey.apiKey}</code>
              </Text>
            </Box>
            <Button
              variant="text"
              leftIcon={commonIcons.copy}
              label={
                didCopy
                  ? d("ui.appSettings.apiKeys.postCreate.copied")
                  : d("ui.appSettings.apiKeys.postCreate.copyButton")
              }
              onClick={() => clickToCopy(revealedApiKey.apiKey)}
            ></Button>
          </Inline>
        </Stack>

        <Alert variant={AlertVariant.WARNING} width="100">
          {d("ui.appSettings.apiKeys.postCreate.viewOnceOnlyWarning")}
        </Alert>

        <ButtonGroup alignment="right">
          <Button
            label={d("ui.appSettings.apiKeys.postCreate.closeButton")}
            onClick={onClose}
            variant="secondary"
          />
        </ButtonGroup>
      </Stack>
    </Modal>
  );
};
