package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Jsonata.JFunctionCallable
import kotlinx.serialization.Serializable

@Serializable
internal object Vlookup : ComputeFunction {
    override val key = "vlookup"
    override val syntax = Syntax(
        "VLOOKUP(lookupValue, tableArray, col_index_num, [range_lookup])", listOf(
            Argument("lookupValue", "The value to search for in the first column", "any"),
            Argument("tableArray", "The 2D array to search", "array"),
            Argument("col_index_num", "The column number to return", "number"),
            Argument("range_lookup", "Optional, TRUE for approximate, FALSE for exact", "boolean")
        )
    )
    override val description = "Vertical lookup in a 2D array"
    override val notes = "Exact match when range_lookup is FALSE; approximate match (numeric) when TRUE"
    override val category = "lookup"
    override val icon = Icon("function")
    override val functionName = "VLOOKUP"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction(JFunctionCallable { _, args ->
        val lookupValue = args!![0]
        val tableArray = args[1] as List<*>
        val colIndexNum = args[2] as Int

        val rangeLookup = if (args.size > 3 && args[3] != null) {
            when (args[3]) {
                is Boolean -> args[3] as Boolean
                is String -> args[3] as Boolean
                else -> true
            }
        } else true

        if (tableArray.isEmpty()) throw IllegalArgumentException("VLOOKUP: tableArray is empty")

        val rows = tableArray.map {
            if (it !is List<*>) throw IllegalArgumentException("VLOOKUP: tableArray must be a 2D array")
            it
        }

        if (colIndexNum < 1) throw IllegalArgumentException("VLOOKUP: col_index_num must be >= 1")

        if (!rangeLookup) {
            for (row in rows) {
                if (row.isEmpty()) continue
                if (row[0] == lookupValue) {
                    if (colIndexNum > row.size) throw IllegalArgumentException("VLOOKUP: col_index_num out of range")
                    return@JFunctionCallable row[colIndexNum - 1]
                }
            }
            return@JFunctionCallable "#N/A"
        } else {
            var resultRow: List<*>? = null
            for (row in rows) {
                if (row.isEmpty()) continue
                val cell = row[0]
                if (cell !is Number || lookupValue !is Number)
                    throw IllegalArgumentException("VLOOKUP: approximate match requires numeric values")
                if (cell.toDouble() > (lookupValue as Number).toDouble()) break
                resultRow = row
            }
            if (resultRow == null) throw IllegalArgumentException("VLOOKUP: no approximate match found")
            if (colIndexNum > resultRow.size) throw IllegalArgumentException("VLOOKUP: col_index_num out of range")
            return@JFunctionCallable resultRow[colIndexNum - 1]
        }
    }, "<xxxx?:x>")
}
