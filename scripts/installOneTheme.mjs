#!/usr/bin/env node
import { program } from "commander";
import fs from "fs";
import { dirname } from "path";
import process from "process";
import { fileURLToPath } from "url";
import { $ } from "zx";

program
  .option("-v, --version <version>", "Specify the version of the OneTheme")
  .option("--help", "Print usage help")
  .parse(process.argv);

// Constants
const options = program.opts();
const exceptedVersionTags = ["0.4.1"];
const __filename = fileURLToPath(import.meta.url);
const SCRIPT_DIR = dirname(__filename);
const BITBUCKET_REPO_FULL_NAME = "devops-martinit/onetheme";
const localPathToOneThemeFolder = `${SCRIPT_DIR}/../../..`; // ${SCRIPT_DIR}/.. is to the top of current, then path to where onetheme should sit
const themeCodes = {
  oneteam: "oneteam",
  otai: "otai"
};
const baseThemeCode = themeCodes.oneteam;

// Where the base theme file will be put in the project, if it's not oneteam
const themeFileLocation = `src/theme`;

function printUsageHelp() {
  console.log(`
        Usage: ./installOneTheme.mjs -v <version> [--help]

        Options:
        -v, --version <version> Specify the version of the OneTheme
            Can be a specific version number or 'latest' or 'local'
            -- 'latest' will install the latest version of the OneTheme
            -- 'local' will install the OneTheme from the local directory (<project-root>../onetheme)
        
        --help Print usage help

        Example:
        ./installOneTheme.mjs -v 0.4.0 # install a specific version
        ./installOneTheme.mjs -v latest # install the latest version
        ./installOneTheme.mjs -v local # install the OneTheme from the local directory (<project-root>../onetheme)
        ./installOneTheme.mjs -v main # install the main branch
        ./installOneTheme.mjs --help # print usage help
        `);
}

if (options.help) {
  printUsageHelp();
  process.exit(0);
}

const version = options.version;

if (!version) {
  console.error("Error: Please specify the version of the OneTheme");
  printUsageHelp();
  process.exit(1);
}

console.log(`Installing OneTheme version: ${version}`);

async function getLatestOneThemeVersion() {
  // get latest onetheme version
  const repoUrl = `*****************:${BITBUCKET_REPO_FULL_NAME}.git`;

  const { stdout } = await $`git ls-remote --tags ${repoUrl}`;
  const versions = stdout
    .split("\n")
    .map(line => {
      return line.split("/").pop();
    })
    .filter(tag => {
      return !exceptedVersionTags.includes(tag);
    })
    .filter(tag => {
      // should be x.y.z
      return /^\d+\.\d+\.\d+$/.test(tag);
    })
    .sort((a, b) => {
      return a.localeCompare(b, undefined, { numeric: true });
    });
  const latestVersion = versions.pop();
  return latestVersion;
}

async function installOneThemeFromBitbucket() {
  // remove existing onetheme
  await $`rm -rf ${SCRIPT_DIR}/../node_modules/@oneteam/onetheme`;
  await $`rm -rf ${SCRIPT_DIR}/../node_modules/@oneteam`; // Remove empty scope dir if needed
  const packageJsonPath = `${SCRIPT_DIR}/../package.json`;
  const packageLockJsonPath = `${SCRIPT_DIR}/../package-lock.json`;

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
  const packageLockJson = JSON.parse(
    fs.readFileSync(packageLockJsonPath, "utf8")
  );

  packageJson.dependencies["@oneteam/onetheme"] =
    `bitbucket:${BITBUCKET_REPO_FULL_NAME}#${targetVersion}`;
  delete packageLockJson.packages["node_modules/@oneteam/onetheme"];

  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  fs.writeFileSync(
    packageLockJsonPath,
    JSON.stringify(packageLockJson, null, 2)
  );
  console.log("Installing OneTheme from Bitbucket...");

  // Show output of npm install
  $.verbose = true;
  await $`cd ${SCRIPT_DIR}/.. && npm install`;
  $.verbose = false;
}

async function installOneThemeFromLocal() {
  const OneThemePath = `${localPathToOneThemeFolder}/onetheme`;
  if (!fs.existsSync(OneThemePath)) {
    console.log(
      "OnetTheme Directory not found. Cloning OneTheme from Bitbucket"
    );
    await $`cd ${localPathToOneThemeFolder}/ && <NAME_EMAIL>:${BITBUCKET_REPO_FULL_NAME}.git`;
  }

  await $`cd ${OneThemePath} && rm -rf *.tgz`;
  await $`cd ${OneThemePath} && npm install && npm pack`;

  const tgzFile = fs.readdirSync(OneThemePath).filter(file => {
    return file.endsWith(".tgz");
  })[0];

  await $`cd ${SCRIPT_DIR}/.. && npm install ${OneThemePath}/${tgzFile}`;
}

// If the base theme is not oneteam, copy the theme file into the project to commit changes
function copyThemeFileIfRequired() {
  if (baseThemeCode !== themeCodes.oneteam) {
    const themeFilePath = `${localPathToOneThemeFolder}/onetheme/src/tokens/stylesheets/${baseThemeCode}.css`;
    const pathToProjectThemeFile = `${SCRIPT_DIR}/../${themeFileLocation}/${baseThemeCode}.css`;

    if (fs.existsSync(themeFilePath)) {
      fs.mkdirSync(dirname(pathToProjectThemeFile), { recursive: true });
      fs.copyFileSync(themeFilePath, pathToProjectThemeFile);
      console.log(
        `Copied theme file from ${themeFilePath} to ${pathToProjectThemeFile}`
      );
    } else {
      console.error(`ERROR: Theme file not found: ${themeFilePath}`);
    }
  }
}

let targetVersion = version;

if (version === "latest") {
  targetVersion = await getLatestOneThemeVersion();
  console.log(`Latest OneTheme version: ${targetVersion}`);
}

if (targetVersion === "local") {
  await installOneThemeFromLocal();
} else {
  await installOneThemeFromBitbucket();
}

copyThemeFileIfRequired();
