package services.oneteam.ai.flow.execution.mapBuilders

import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.MapBuilder
import services.oneteam.ai.shared.domains.flow.variables.Variable
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.Interval
import services.oneteam.ai.shared.domains.workspace.IntervalId
import services.oneteam.ai.shared.domains.workspace.SeriesConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace

/*
*
* {
*   id: string,
*   name: string,
*   seriesConfiguration: {
*     id: string,
*     name: string
*   },
*   next:  {
*     id: string | null,
*     name: string | null
*   },
*   previous: {
*     id: string | null,
*     name: string | null
*   }
* }
*
*
* */
class SeriesIntervalMapBuilder(
    private val workspace: Workspace.ForJson,
) : MapBuilder {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun match(variable: Variable): Boolean {
        return variable.type.typeDef.startsWith("seriesInterval")
    }

    override suspend fun handle(variable: VariableInstance, cacheKey: String?): JsonElement {
        try {
            val id = variable.get().toString().trim('"')

            if (id == "") {
                return JsonNull
            }

            val intervalId = variable.get().toString().trim('"')
            val (seriesConfiguration, interval) = workspace.findSeriesAndIntervalByIntervalId(IntervalId(intervalId))
            return buildMap(interval, seriesConfiguration)
        } catch (e: Exception) {
            logger.error("Could not handle variable $variable", e)
            throw e
        }
    }

    private fun buildMinimalIntervalMap(
        interval: Interval? = null,
    ): JsonObject {
        if (interval == null) {
            return JsonObject(
                mapOf(
                    "id" to JsonNull, "name" to JsonNull
                )
            )
        }
        return JsonObject(
            mapOf(
                "id" to JsonPrimitive(interval.id.value), "name" to JsonPrimitive(interval.name.value)
            )
        )
    }

    private fun buildMap(
        interval: Interval,
        seriesConfiguration: SeriesConfiguration.ForApi,
    ): JsonObject {
        var map: JsonObject = buildMinimalIntervalMap(interval)
        val intervalId = interval.id

        val seriesConfigurationMap = JsonObject(
            mapOf(
                "id" to JsonPrimitive(seriesConfiguration.id.value),
                "name" to JsonPrimitive(seriesConfiguration.name.value),
                "intervals" to seriesConfiguration.getIntervals()
            )
        )
        map = JsonObject(map.plus(mapOf("seriesConfiguration" to seriesConfigurationMap)))

        val nextInterval = seriesConfiguration.findNextIntervalOrNull(intervalId)
        val nextIntervalMap = JsonObject(
            mapOf(
                "next" to buildMinimalIntervalMap(nextInterval)
            )
        )
        map = JsonObject(map.plus(nextIntervalMap))

        val previousInterval = seriesConfiguration.findPreviousIntervalOrNull(intervalId)
        val previousIntervalMap = JsonObject(
            mapOf(
                "previous" to buildMinimalIntervalMap(previousInterval)
            )
        )
        map = JsonObject(map.plus(previousIntervalMap))

        return map
    }
}