package services.oneteam.ai.app

import io.ktor.http.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.rebac.spicedb.SpiceDbRepository
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import javax.sql.DataSource

/**
 * The health endpoints are instantiated with a list of health checks:
 * ```
 * if (debugConfig.enableEndpoints) {
 *     debugEndpoints(workspaceService)
 *     healthEndpoints(
 *         listOf(
 *             SpiceDBHealthCheck(spiceDbRepository),
 *             DatabaseHealthCheck(database.standard.dataSource),
 *             AutoMergeHealthCheck(apiDocumentService)
 *         )
 *     )
 * }
 * ```
 * The endpoint returns a map of check → result
 * ```
 * {
 *   "SpiceDBHealthCheck": false,
 *   "DatabaseHealthCheck": true,
 *   "AutoMergeHealthCheck": true
 * }
 * ```
 * You can use this to make sure your system is configured and running.
 */
fun Route.healthEndpoints(healthChecks: List<HealthCheck>) {
    val logger = LoggerFactory.getLogger("services.oneteam.ai.app.healthEndpoints")

    get("/tools/health/all") {
        // build a map of health check classname to result
        val results: Map<String, Boolean> = healthChecks.associate { check ->
            val className = check::class.simpleName ?: "UnknownHealthCheck"
            val result = try {
                check.check()
            } catch (e: Exception) {
                logger.error("Health check failed", e)
                false
            }
            className to result
        }

        if (results.all { it.value }) {
            call.respond(HttpStatusCode.OK, results)
        } else {
            call.respond(HttpStatusCode.InternalServerError, results)
        }
    }

}

interface HealthCheck {
    suspend fun check(): Boolean
}

class SpiceDBHealthCheck(private val spiceDbRepository: SpiceDbRepository) : HealthCheck {
    override suspend fun check(): Boolean {
        return spiceDbRepository.healthCheck()
    }
}

class DatabaseHealthCheck(private val datasource: DataSource) : HealthCheck {
    override suspend fun check(): Boolean {
        datasource.connection.use { conn ->
            // check if the database is up and running
            conn.prepareStatement("SELECT 1").use { statement ->
                statement.executeQuery().use { resultSet ->
                    return resultSet.next()
                }
            }
        }
    }
}

class AutoMergeHealthCheck(private val documentService: ApiDocumentService) : HealthCheck {
    override suspend fun check(): Boolean {
        return documentService.healthCheck()
    }
}