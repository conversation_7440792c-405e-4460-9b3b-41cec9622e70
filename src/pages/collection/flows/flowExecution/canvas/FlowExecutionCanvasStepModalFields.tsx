import React, { useMemo, useState } from "react";

import {
  Accordion,
  Box,
  FontWeight,
  Heading,
  HeadingSize,
  Inline,
  JsonEditor,
  Pill,
  Stack
} from "@oneteam/onetheme";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { useDictionary } from "@src/hooks/useDictionary";
import { MockFlowContext } from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  ExecutionStep,
  FlowExecutionDocument,
  FlowResult,
  FlowStateStepEntity
} from "@src/types/FlowConfiguration/FlowExecution";
import {
  FlowStep,
  FlowStepVariant,
  flowStepVariantsWithTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  CommonTypedStepProperties,
  ConditionStepProperties,
  IteratorStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { resultColumnGetVariant } from "../../FlowExecutionHelper";
import { useFlowExecutionContext } from "../FlowExecutionContext";
import { FlowExecutionErrorBox } from "./components/FlowExecutionErrorBox";
import { ActionFields } from "./stepModalFields/ActionFields";
import { ConditionFields } from "./stepModalFields/ConditionFields";
import { FlowFields } from "./stepModalFields/FlowFields";
import { IteratorFields } from "./stepModalFields/IteratorFields";
import { SetVariablesFields } from "./stepModalFields/SetVariablesFields";
import { SummaryFields } from "./stepModalFields/SummaryFields";
import { TriggerFields } from "./stepModalFields/TriggerFields";

export interface ExecutionFlowStepModalFieldsProps {
  configurationStep?: FlowStep;
  executionMetadata?: FlowStateStepEntity;
  executionStep?: ExecutionStep;
  executionFlow?: FlowExecutionDocument;
  flowContext?: MockFlowContext;
  workspaceDocument?: WorkspaceDocument;
}

export const FlowExecutionCanvasStepModalFields = ({
  configurationStep,
  executionStep,
  executionMetadata,
  executionFlow,
  flowContext,
  workspaceDocument
}: ExecutionFlowStepModalFieldsProps) => {
  const [expandMetadata, setExpandMetadata] = useState(false);

  const {
    selectedStepId,
    executionFlowForModal,
    executionFlow: renderedExecutionFlow,
    settings
  } = useFlowExecutionContext();

  const { configurationFlow, flowStepTypeConfigByPrimaryIdentifier } =
    useConfigurationFlowContext();

  const selectedStepTypeConfig = useMemo(
    () =>
      flowStepVariantsWithTypeConfiguration.includes(
        configurationStep?.variant as FlowStepVariant
      )
        ? flowStepTypeConfigByPrimaryIdentifier?.[
            (configurationStep?.properties as CommonTypedStepProperties)
              ?.typePrimaryIdentifier
          ]
        : undefined,
    [
      flowStepTypeConfigByPrimaryIdentifier,
      configurationStep?.properties,
      configurationStep?.variant
    ]
  );

  // TODO: Remove debuggingFields after finishing all step types
  const debuggingFields = useMemo(() => {
    if (!configurationStep?.variant) {
      return <></>;
    }
    return (
      <>
        {executionMetadata && (
          <Box width="100">
            <Accordion
              onOpenChange={() => setExpandMetadata(!expandMetadata)}
              isOpen={expandMetadata}
              trigger={"Step State"}
            >
              <JsonEditor
                height="20vh"
                disabled
                localJSONContent={JSON.stringify(executionMetadata, null, "\t")}
              />
            </Accordion>
          </Box>
        )}
        <Box style={{ height: "70vh" }}>
          <JsonEditor
            disabled
            localJSONContent={JSON.stringify(executionStep, null, "\t")}
          />
        </Box>
      </>
    );
  }, [executionStep, executionMetadata, expandMetadata, configurationStep]);

  const d = useDictionary();

  const executionResult = useMemo(() => {
    if (
      !configurationStep ||
      configurationStep.variant === FlowStepVariant.TRIGGER
    ) {
      return executionFlow?.state.result;
    }

    if (
      configurationStep.variant === FlowStepVariant.ITERATOR &&
      renderedExecutionFlow
    ) {
      return renderedExecutionFlow.state.result;
    }

    return executionFlow?.state.steps.entities[configurationStep.id]?.result;
  }, [configurationStep, executionFlow, renderedExecutionFlow]);

  const executionStatus = useMemo(() => {
    if (!executionResult) {
      return <></>;
    }

    return (
      <Pill
        icon={executionResult === FlowResult.PENDING ? "custom" : "standard"}
        customIcon={{ name: "pending", fillStyle: "filled" }}
        variant={resultColumnGetVariant(executionResult)}
        key={executionResult}
        label={d(`ui.flow.result.${executionResult}`)}
      />
    );
  }, [d, executionResult]);

  const fields = useMemo(() => {
    if (selectedStepId === undefined) {
      return <SummaryFields />;
    }

    if (settings?.displayJson) {
      return debuggingFields;
    }

    if (
      configurationStep?.variant === FlowStepVariant.ITERATOR &&
      flowContext &&
      workspaceDocument &&
      executionStep
    ) {
      return (
        <IteratorFields
          iteratorExecutionStep={
            executionStep as ExecutionStep<IteratorStepProperties>
          }
          configurationStep={configurationStep}
        />
      );
    }

    if (
      configurationStep?.variant === FlowStepVariant.SET_VARIABLES &&
      flowContext &&
      workspaceDocument
    ) {
      return (
        <SetVariablesFields
          executionFlow={executionFlowForModal}
          executionStep={executionStep}
        />
      );
    }

    if (
      configurationStep?.variant === FlowStepVariant.ACTION &&
      selectedStepTypeConfig &&
      executionFlowForModal
    ) {
      return (
        <ActionFields
          stepTypeConfig={selectedStepTypeConfig}
          configurationStep={configurationStep}
          flowContext={flowContext}
        />
      );
    }

    if (
      configurationStep?.variant === FlowStepVariant.CONDITION &&
      executionStep
    ) {
      return (
        <ConditionFields
          executionStep={
            executionStep as ExecutionStep<ConditionStepProperties>
          }
        />
      );
    }

    if (
      configurationStep?.variant === FlowStepVariant.TRIGGER &&
      configurationStep
    ) {
      return (
        <TriggerFields
          stepTypeConfig={selectedStepTypeConfig}
          configurationStep={configurationStep}
          flowContext={flowContext}
        />
      );
    }

    if (
      configurationStep?.variant === FlowStepVariant.FLOW &&
      executionStep &&
      workspaceDocument
    ) {
      return (
        <FlowFields
          executionStep={executionStep}
          workspaceDocument={workspaceDocument}
        />
      );
    }

    return debuggingFields;
  }, [
    selectedStepId,
    configurationStep,
    executionStep,
    debuggingFields,
    flowContext,
    workspaceDocument,
    selectedStepTypeConfig,
    executionFlowForModal,
    settings?.displayJson
  ]);

  return (
    <Stack
      gap="100"
      contentsWidth="100"
      style={{ overflowX: "hidden", overflowY: "auto" }}
      height="100"
    >
      <Inline spaceBetween>
        <Heading
          size={HeadingSize.S}
          weight={FontWeight.MEDIUM}
          style={{ maxWidth: "100%" }}
        >
          {configurationStep?.name ?? configurationFlow?.name}
        </Heading>
        {executionStatus}
      </Inline>
      <Stack height="100" gap="100" contentsWidth="100" overflow="auto">
        {executionMetadata?.result === FlowResult.FAILED && (
          <FlowExecutionErrorBox
            message={executionMetadata.message?.[0] ?? ""}
            failedStepConfiguration={configurationStep}
            iteratorParentId={
              configurationStep?.variant === FlowStepVariant.ITERATOR
                ? configurationStep.id
                : undefined
            }
          />
        )}
        {fields}
      </Stack>
    </Stack>
  );
};
