import { FlowExecutionDocument } from "@src/types/FlowConfiguration/FlowExecution";

export const getNestedFlowExecution = (
  flowExecution: FlowExecutionDocument,
  path: string[]
) => {
  if (path.length === 0) {
    return flowExecution;
  }

  const innerExecution = path.reduce((current, key) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (current as Record<string, any>)?.[key] ?? null;
  }, flowExecution);

  return innerExecution;
};
