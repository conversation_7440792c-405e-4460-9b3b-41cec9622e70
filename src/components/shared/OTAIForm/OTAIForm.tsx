import React, { MutableRefObject, useCallback } from "react";

import { Form } from "@oneteam/onetheme";
import { ZodType } from "zod";

import { ZodAnswerSchemaBuilder } from "@helpers/forms/ZodAnswerSchemaBuilder/ZodAnswerSchemaBuilder.ts";
import { internalOtaiFormField } from "@helpers/otaiFormHelper";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import { Section } from "@src/types/FormConfiguration";

import { OTAIFormFieldQuestion } from "./OTAIFormFieldQuestion";
import { OTAIFormFieldSection } from "./OTAIFormFieldSection/OTAIFormFieldSection";
import {
  InternalOTAIFormField,
  InternalOTAIFormFieldQuestion,
  InternalOTAIFormFieldSection,
  OTAIFormFieldPositioning,
  OTAISectionUIControls
} from "./OTAIFormType";

export const OTAIForm = ({
  content,
  sectionName,
  uiControls,
  level = 0,
  positioning,
  addFormWrapper = true,
  formWrapperProperties,
  autoFocusFirstField = false
}: {
  content: InternalOTAIFormField[];
  sectionName?: string;
  uiControls?: OTAISectionUIControls;
  level?: number;
  positioning?: OTAIFormFieldPositioning;
  // Whether to add <Form> wrapper
  addFormWrapper?: boolean;
  formWrapperProperties?: OTAIFormWrapperProperties;
  autoFocusFirstField?: boolean;
}) => {
  const d = useDictionary();

  return (
    <Wrapper
      addFormWrapper={addFormWrapper}
      d={d}
      formWrapperProperties={formWrapperProperties}
      content={content}
    >
      <OTAIFormFieldSection
        name={sectionName}
        level={level}
        uiControls={uiControls}
      >
        {content.map((item, index) => {
          if (item.type === "section") {
            const subSection = item as InternalOTAIFormFieldSection;
            return (
              <OTAIForm
                key={subSection.identifier}
                content={subSection.content}
                level={level + 1}
                addFormWrapper={false}
                autoFocusFirstField={autoFocusFirstField && index === 0}
                positioning={positioning}
                sectionName={subSection.name}
                uiControls={subSection.properties?.uiControls}
              />
            );
          }
          const question = item as InternalOTAIFormFieldQuestion;
          return (
            <OTAIFormFieldQuestion
              key={question.id ?? question.identifier}
              question={question}
              autoFocus={autoFocusFirstField && index === 0}
            />
          );
        })}
      </OTAIFormFieldSection>
    </Wrapper>
  );
};

export type OTAIFormWrapperProperties = {
  formButtonsPortal?: MutableRefObject<HTMLElement | null>;
  handleSubmit?: (data: Record<string, unknown>) => void;
  handleCancel?: () => void;
  hideFormButtons?: boolean;
  defaultValues?: Record<string, unknown>;
  disableAutoGenerateSchema?: boolean;
  schema?: ZodType<Partial<Record<string, unknown>>>;
  submitLabel?: string;
  cancelLabel?: string;
};

const Wrapper = ({
  addFormWrapper,
  d,
  formWrapperProperties,
  children,
  content
}: {
  addFormWrapper: boolean;
  d: Dictionary;
  formWrapperProperties?: OTAIFormWrapperProperties;
  children: React.ReactNode;
  content: InternalOTAIFormField[];
}) => {
  const getSchema: () => ZodType<Partial<Record<string, unknown>>> | undefined =
    useCallback(() => {
      if (formWrapperProperties?.schema) {
        return formWrapperProperties?.schema;
      } else if (formWrapperProperties?.disableAutoGenerateSchema) {
        return;
      }

      const schemaBuilder = new ZodAnswerSchemaBuilder(
        content.map(item => internalOtaiFormField(item)) as Section["content"],
        d
      );
      return schemaBuilder.generatedSchema;
    }, [
      content,
      d,
      formWrapperProperties?.disableAutoGenerateSchema,
      formWrapperProperties?.schema
    ]);

  if (!addFormWrapper) {
    return <>{children}</>;
  }

  return (
    <Form
      d={d}
      {...formWrapperProperties}
      handleSubmit={formWrapperProperties?.handleSubmit ?? (() => {})}
      schema={getSchema()}
      cancelLabel={formWrapperProperties?.cancelLabel ?? d("ui.common.cancel")}
      submitLabel={formWrapperProperties?.submitLabel ?? d("ui.common.save")}
    >
      {children}
    </Form>
  );
};
