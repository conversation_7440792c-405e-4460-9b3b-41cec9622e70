import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import {
  HotColumn,
  HotColumnProps,
  HotTable,
  HotTableProps,
  HotTableRef
} from "@handsontable/react-wrapper";
import {
  Box,
  IconButton,
  Inline,
  NumberType,
  SearchBar,
  SelectOptionType,
  Stack,
  Text
} from "@oneteam/onetheme";
import {
  ChangeSource,
  CellChange as HandsontableCellChange
} from "handsontable/common";
import { ManualRowMove, registerPlugin } from "handsontable/plugins";
import { registerAllModules } from "handsontable/registry";
import "handsontable/styles/handsontable.min.css";
import "handsontable/styles/ht-theme-main.min.css";
import { cloneDeep, keyBy, range, sum } from "lodash";

import {
  addItem,
  mapOverResource,
  removeItems
} from "@helpers/OrderedMapNoState";
import { getByPath } from "@helpers/automergeDocumentHelper";
import { customNanoId } from "@helpers/customNanoIdHelper";
import {
  AnswerUpdate,
  UpdateMultipleFormAnswers,
  updateAnswer,
  updateMultipleAnswers
} from "@helpers/forms/answerHelper";
import { isEditCollectionFormDisabled } from "@helpers/forms/formHelper";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";
import { QuestionTableHelper } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionTableHelper";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question, QuestionTypes } from "@src/types/Question";
import {
  BooleanQuestionProperties,
  NumberQuestionProperties,
  SelectQuestionProperties,
  TableDefaultView,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import {
  FormAnswer,
  TableAnswer,
  TableAnswerRow
} from "@src/types/collection/CollectionForm";
import { Resource } from "@src/types/documentTypes";

import "./QuestionTableAnswer.scss";
import { BasicCellRenderer } from "./QuestionTableAnswerCell/BasicCell";
import { BooleanEditor } from "./QuestionTableAnswerCell/BooleanCell";
import { DateRenderer } from "./QuestionTableAnswerCell/DropdownCells/DateCell";
import {
  MultiSelectEditor,
  MultiSelectRenderer
} from "./QuestionTableAnswerCell/DropdownCells/MultiSelectCell";
import {
  SelectEditor,
  SelectRenderer
} from "./QuestionTableAnswerCell/DropdownCells/SelectCell";
import { ListEditor, ListRenderer } from "./QuestionTableAnswerCell/ListCell";
import { QuestionTableAnswerChartTabs } from "./QuestionTableAnswerChartTabs";
import { QuestionTableAnswerKebabMenu } from "./QuestionTableAnswerKebabMenu";
import {
  getCellValueToDisplay,
  getCellValueToSave
} from "./questionTableAnswerHelper";

registerAllModules();
const licenseKey = import.meta.env.VITE_HANDSONTABLE_KEY;
const rowHeaderWidth = 40;
const defaultRowHeight = 30;
const columnHeaderHeight = 24;
const defaultColumnWidth = 120;

type RowId = string;
type RowIndex = number;
type ColumnId = string;
type ColumnIndex = number;
type RowValue = {
  [columnId: ColumnId]: FormAnswer["value"];
};
type TableData = RowValue[];
type ChangesByRow = {
  [rowId: RowId]: RowValue;
};

export const QuestionTableAnswer = ({
  tableAnswerAccessor = "",
  question,
  disabled
}: {
  tableAnswerAccessor?: string;
  question: Question<TableQuestionProperties>;
  disabled?: boolean;
}) => {
  const [tableViewPanelTab, setTableViewPanelTab] = useState<TableDefaultView>(
    question?.properties?.defaultView ?? TableDefaultView.ALL
  );
  const [isInitiallyRendered, setIsInitiallyRendered] = React.useState(false);
  const d = useDictionary();

  const chartsCanBeDisplayed = useMemo(() => {
    return (
      question?.properties?.charts?.filter(
        chart => !chart.properties?.hidden
      ) ?? []
    );
  }, [question]);

  const showTabsAndChart = useMemo(() => {
    const charts = chartsCanBeDisplayed ?? [];
    return charts.length > 0;
  }, [chartsCanBeDisplayed]);

  const showPreviewAnswerRow = useMemo(() => {
    const columns = question.properties?.columns ?? [];
    return columns.length > 0;
  }, [question.properties?.columns]);

  registerPlugin(ManualRowMove);
  const {
    mode,
    docChange,
    collectionFormId,
    documentId,
    formData,
    annotationDocChange,
    formAnnotationDocument,
    fullScreenQuestion,
    setFullScreenQuestion
  } = useCollectionFormContext();

  const [temporaryValues, setTemporaryValues] = useState<{
    entities: {
      [changeId: string]: ChangesByRow;
    };
    order: string[];
  }>({
    entities: {},
    order: []
  });

  const answer = useMemo(() => {
    return formData?.answers?.[question.id] as FormAnswer<TableAnswer>;
  }, [formData?.answers, question.id]);

  const getRowIdByIndex = useCallback(
    (rowIndex: RowIndex): RowId | undefined => {
      const tableAnswerValue = answer?.value as TableAnswer;
      if (!tableAnswerValue || !Array.isArray(tableAnswerValue?.order)) {
        return undefined;
      }
      return tableAnswerValue.order[rowIndex];
    },
    [answer]
  );

  const questionDisabled = useMemo(
    () =>
      isEditCollectionFormDisabled(mode) ||
      disabled ||
      question.properties?.disabled,
    [disabled, mode, question.properties?.disabled]
  );

  const questionTableHelper = useMemo(() => {
    return new QuestionTableHelper(question);
  }, [question]);

  const createEmptyAnswerRow = useCallback(() => {
    return addItem(questionTableHelper.createNewRow(), {
      entities: {},
      order: []
    });
  }, [questionTableHelper]);

  const displayColumnQuestions = useMemo(() => {
    return question.properties?.columns?.filter(
      c => !c.properties?.hidden
    ) as Question<TableQuestionProperties>[];
  }, [question.properties?.columns]);

  const displayColumnQuestionsById: {
    [key: string]: Question<TableQuestionProperties>;
  } = useMemo(
    () => keyBy(displayColumnQuestions, "id"),
    [displayColumnQuestions]
  );

  const tableWidth = useMemo(() => {
    if (!isInitiallyRendered) {
      return 0; // Initial width, will be recalculated after mount
    }
    const element = document.getElementById(
      `question-table-answer-${question.id}`
    );
    if (element) {
      return element.offsetWidth;
    }
    return 0; // Fallback if the element is not found
  }, [question.id, isInitiallyRendered]);

  const tableSettings = useMemo(
    () =>
      formAnnotationDocument?.state?.tableQuestionSettings?.[question.id] ?? {
        widths: {},
        heights: {},
        frozenColumnIds: []
      },
    [formAnnotationDocument, question.id]
  );

  const columnWidths = useMemo(() => {
    const fallbackColumnWidth = (() => {
      if (!tableWidth) {
        return defaultColumnWidth; // Default fallback width if tableWidth is not set
      }
      const tableWidthWithoutCounterColumn = tableWidth - rowHeaderWidth; // 50px for the row counter column

      if (
        displayColumnQuestions?.length * defaultColumnWidth >=
        tableWidthWithoutCounterColumn
      ) {
        return defaultColumnWidth; // If the total width of columns is greater than the table width, use 120px
      }

      const numberOfSetWidths = Object.keys(tableSettings.widths).length;
      const setWidthsTotal = Object.values(tableSettings.widths).reduce(
        (total, width) => total + width,
        0
      );
      const remainingWidth = tableWidthWithoutCounterColumn - setWidthsTotal;
      const remainingColumnsCount =
        displayColumnQuestions.length - numberOfSetWidths;
      const dividedWidth = remainingWidth / remainingColumnsCount;
      return Math.max(defaultColumnWidth, dividedWidth);
    })();

    return displayColumnQuestions?.map(column => {
      return (
        tableSettings.widths[column.id] ?? fallbackColumnWidth // Use width from properties or default to 120
      );
    });
  }, [displayColumnQuestions, tableSettings.widths, tableWidth]);

  const temporaryValuesByRowId = useMemo(() => {
    const temporaryValuesByRowId: ChangesByRow = {};

    // Run through all the changes and put them into a change object by rowId
    temporaryValues.order.forEach(changeId => {
      const change = temporaryValues.entities[changeId];
      if (!change) {
        return;
      }
      Object.entries(change).forEach(([rowId, rowData]) => {
        temporaryValuesByRowId[rowId] = {
          ...temporaryValuesByRowId[rowId],
          ...rowData
        };
      });
    });

    return temporaryValuesByRowId;
  }, [temporaryValues]);

  const createFirstRow = useCallback(() => {
    // if there are no rows on the table answer yet, create the first one
    if (docChange && question.properties) {
      docChange(d => {
        if (!(d.answers[question.id]?.value as TableAnswer)?.order?.length) {
          const resource = createEmptyAnswerRow();
          d.answers[question.id] = {
            questionId: question.id,
            value: resource as TableAnswer,
            type: question.type
          };
        }
      });
    }
  }, [
    createEmptyAnswerRow,
    docChange,
    question.id,
    question.properties,
    question.type
  ]);

  const rowValues = useMemo(() => {
    const tableAnswer = (() => {
      if (showPreviewAnswerRow && !docChange) {
        return createEmptyAnswerRow();
      }
      return answer?.value;
    })();

    if (
      !tableAnswer ||
      !Array.isArray(tableAnswer?.order) ||
      !tableAnswer?.order.length
    ) {
      createFirstRow();
      return [];
    }

    const rows: TableData = mapOverResource(tableAnswer, row => {
      const rowData: RowValue = {};

      Object.entries(temporaryValuesByRowId[row.id] ?? {}).forEach(
        ([columnId, value]) => {
          const columnQuestion = displayColumnQuestionsById[columnId];
          if (!columnQuestion) {
            return;
          }
          rowData[columnId] = getCellValueToDisplay({
            question: columnQuestion,
            value: value as FormAnswer["value"]
          });
        }
      );

      if (row?.columns) {
        Object.entries(row.columns).forEach(([columnId, columnData]) => {
          // Skip if the row has a temporary value for this column
          if (columnId in rowData) {
            return;
          }
          const columnQuestion = displayColumnQuestionsById[columnId];
          if (!columnQuestion) {
            return;
          }

          rowData[columnId] = getCellValueToDisplay({
            question: columnQuestion,
            value: columnData.value as FormAnswer["value"]
          });
        });
      }

      return rowData;
    });

    return rows;
  }, [
    showPreviewAnswerRow,
    docChange,
    answer?.value,
    createEmptyAnswerRow,
    temporaryValuesByRowId,
    displayColumnQuestionsById,
    createFirstRow
  ]);

  const chartAnswer = useMemo(() => {
    if (!docChange || !rowValues) {
      return undefined;
    }
    return rowValues?.map(row => {
      const result: Record<string, string> = {};
      displayColumnQuestions?.forEach(col => {
        result[col.text] = row[col.id] as string;
      });
      return result;
    });
  }, [rowValues, displayColumnQuestions, docChange]);

  const rowHeights = useMemo(() => {
    return rowValues.map((_, index) => {
      const rowId = getRowIdByIndex(index);
      if (!rowId) {
        return defaultRowHeight; // Default height if not set
      }
      return tableSettings.heights[rowId] ?? defaultRowHeight; // Use height from properties or default to 30
    });
  }, [tableSettings.heights, rowValues, getRowIdByIndex]);

  const tableHeight = useMemo(
    () => Math.min(sum(rowHeights), 440),
    [rowHeights]
  );

  const columnProps: HotColumnProps[] = useMemo(() => {
    return (
      displayColumnQuestions?.map(column => {
        const props: HotColumnProps = {};
        // Handsontable column properties
        props.title = column.text;
        props.data = column.id;
        props.type = "text";
        props.readOnly = questionDisabled || column.properties?.disabled;
        props.headerClassName = "htLeft";

        // Custom properties for the column
        props.tableQuestionId = question.id;
        props.columnQuestion = column;
        props.getRowIdByIndex = getRowIdByIndex;

        if (column.type === QuestionTypes.TEXT) {
          props.renderer = BasicCellRenderer;
        } else if (column.type === QuestionTypes.NUMBER) {
          props.headerClassName = "htRight";
          props.type = "numeric";
          props.renderer = BasicCellRenderer;
          // TODO: remove
          const numberProperties =
            column.properties as NumberQuestionProperties;
          let numericFormatPattern = "i,ii";

          if (numberProperties.decimalPlaces) {
            numericFormatPattern += `.${"i".repeat(numberProperties.decimalPlaces)}`;
          }
          if (
            (column.properties as NumberQuestionProperties).type ===
            NumberType.PERCENTAGE
          ) {
            numericFormatPattern += "%"; // Add percentage sign
          }
          props.numericFormat = {
            pattern: numericFormatPattern
          };
        } else if (
          column.type === QuestionTypes.SELECT ||
          column.type === QuestionTypes.MULTISELECT
        ) {
          const selectProperties =
            column.properties as SelectQuestionProperties;
          props.options = selectProperties?.options?.map(option => ({
            value: option.value,
            label: option.label || option.value
          }));
          props.optionNameByValue = props.options.reduce(
            (acc: Record<string, string>, option: SelectOptionType) => {
              acc[option.value] = option.label;
              return acc;
            },
            {} as Record<string, string>
          );

          if (column.type === QuestionTypes.MULTISELECT) {
            props.renderer = MultiSelectRenderer;
            props.editor = MultiSelectEditor;
          } else {
            props.renderer = SelectRenderer;
            props.editor = SelectEditor;
          }
        } else if (column.type === QuestionTypes.LIST) {
          props.renderer = ListRenderer;
          props.editor = ListEditor;
        } else if (column.type === QuestionTypes.DATE) {
          props.renderer = DateRenderer;
          props.type = "date";
          props.dateFormat = "YYYY-MM-DD";
        } else if (column.type === QuestionTypes.BOOLEAN) {
          props.renderer = BasicCellRenderer;
          props.editor = BooleanEditor;
          const booleanProperties =
            column.properties as BooleanQuestionProperties;
          props.optionNameByValue = {
            true:
              booleanProperties?.trueText ??
              d("ui.configuration.forms.question.boolean.trueDefault"),
            false:
              booleanProperties?.falseText ??
              d("ui.configuration.forms.question.boolean.falseDefault")
          };
        }
        return props;
      }) ?? []
    );
  }, [
    d,
    displayColumnQuestions,
    getRowIdByIndex,
    question.id,
    questionDisabled
  ]);

  const handleMoveRows = useCallback(
    (
      rowIndexes: RowIndex[],
      finalRowIndex: RowIndex,
      _: RowIndex | undefined, // Drop index
      movePossible: boolean
    ) => {
      if (questionDisabled || !movePossible || !docChange) {
        return false; // No change in order, do nothing
      }

      const order = [...(answer?.value?.order ?? [])];
      if (!order) {
        return;
      }
      let offsetIndex = 0;

      // List of row IDs to be moved
      const rowIds: string[] = [];

      // Remove all the rows from the current position
      rowIndexes
        .sort((a, b) => a - b)
        .forEach(rowIndex => {
          const rowId = order[rowIndex - offsetIndex];
          rowIds.push(rowId);
          order.splice(rowIndex - offsetIndex, 1);
          offsetIndex++;
        });

      // Then add all of them back at the new position
      order.splice(finalRowIndex, 0, ...rowIds);

      docChange(doc => {
        const questionAnswers = getByPath<Resource<TableAnswerRow>>(
          doc,
          tableAnswerAccessor.split(".")
        );
        questionAnswers.order = order;
        return doc;
      });

      // Highlight the moved rows
      const newRowIndex = finalRowIndex + rowIds.length - 1;
      hotRef.current?.hotInstance?.selectRows(finalRowIndex, newRowIndex);

      // Cancel the default move action since we handle it manually
      return false;
    },
    [answer?.value?.order, docChange, tableAnswerAccessor, questionDisabled]
  );

  const handleRemoveRows = useCallback(
    (rowIndex: RowIndex, amount: number) => {
      if (!answer?.value?.order) {
        return;
      }
      const rowIds = range(rowIndex, rowIndex + amount).map(
        index => (answer?.value as TableAnswer).order[index]
      );

      const updatedTable = removeItems(
        rowIds,
        JSON.parse(JSON.stringify(answer?.value))
      );

      if (updatedTable.order.length === 0) {
        // If no rows left, create a new empty row
        const newRowId = questionTableHelper.createRowId();
        updatedTable.order.push(newRowId);
        updatedTable.entities[newRowId] =
          questionTableHelper.createNewRow(newRowId);
      }
      updateAnswer({
        question,
        value: updatedTable as TableAnswer,
        answerAccessor: tableAnswerAccessor,
        documentId
      });

      annotationDocChange?.(d => {
        for (const rowId of rowIds) {
          if (
            d.state?.tableQuestionSettings?.[question.id]?.heights[rowId] !==
            undefined
          ) {
            delete d.state?.tableQuestionSettings[question.id].heights[rowId];
          }
        }
      });
      // Cancel the default remove action since we handle it manually
      return false;
    },
    [
      answer?.value,
      question,
      tableAnswerAccessor,
      documentId,
      annotationDocChange,
      questionTableHelper
    ]
  );

  const applyAnswerChanges = useCallback(
    ({
      changesByRow = {},
      changedColumnIds,
      // changedRowIds,
      newRowIdByIndex = {}
    }: {
      changesByRow?: ChangesByRow;
      newRowIdByIndex?: {
        [rowIndex: RowIndex]: RowId;
      };
      changedColumnIds: ColumnId[];
      changedRowIds: string[];
    }) => {
      const changeId = customNanoId();
      setTemporaryValues(prev => {
        const newValues = { ...prev };
        newValues.entities[changeId] = changesByRow;
        newValues.order.push(changeId);
        return newValues;
      });

      const updateAnswerAndClearTemporaryValues = ({
        answers,
        documentId
      }: UpdateMultipleFormAnswers) => {
        updateMultipleAnswers({
          answers,
          documentId
        }).finally(() =>
          setTimeout(
            () =>
              setTemporaryValues(prev => {
                const newValues = { ...prev };
                delete newValues.entities[changeId];
                newValues.order = newValues.order.filter(id => id !== changeId);
                return newValues;
              }),
            500 // Buffer time for doc update - ideally reduce this later
          )
        );
      };

      const setTableRequired =
        Object.keys(newRowIdByIndex).length > 0 ||
        // TODO: update sync-server to be able to handle list in a cell
        changedColumnIds.filter(
          columnId =>
            displayColumnQuestionsById[columnId]?.type === QuestionTypes.LIST
        ).length > 0;

      if (setTableRequired) {
        const updatedTable = cloneDeep(
          answer?.value ?? {
            entities: {},
            order: []
          }
        ) as TableAnswer;
        // Add new rows if they don't exist
        Object.entries(newRowIdByIndex)
          .sort(
            ([rowIndexA], [rowIndexB]) => Number(rowIndexA) - Number(rowIndexB)
          )
          .forEach(([rowIndex, newRowId]) => {
            updatedTable.entities[newRowId] =
              questionTableHelper.createNewRow(newRowId);
            updatedTable.order.splice(Number(rowIndex), 0, newRowId);
          });

        Object.entries(changesByRow).forEach(([rowId, columnChanges]) => {
          Object.entries(columnChanges).forEach(([columnId, newValue]) => {
            if (!updatedTable.entities[rowId].columns[columnId]) {
              updatedTable.entities[rowId].columns[columnId] = {
                value: newValue,
                questionId: columnId,
                type: displayColumnQuestionsById[columnId]?.type
              };
            } else {
              updatedTable.entities[rowId].columns[columnId].value = newValue;
            }
          });
        });

        updateAnswerAndClearTemporaryValues({
          answers: [
            {
              question,
              value: updatedTable,
              answerAccessor: tableAnswerAccessor
            }
          ],
          documentId
        });
        return;
      }

      const answers: AnswerUpdate[] = Object.entries(changesByRow).flatMap(
        ([rowId, columnChanges]) =>
          Object.entries(columnChanges).map(([columnId, value]) => {
            const columnQuestion = displayColumnQuestionsById[columnId];
            const answerAccessor = `${tableAnswerAccessor}.entities.${rowId}.columns.${columnId}.value`;
            return {
              question: columnQuestion,
              value,
              answerAccessor
            };
          })
      );

      if (answers.length === 0) {
        return;
      }

      updateAnswerAndClearTemporaryValues({
        answers,
        documentId
      });
    },
    [
      answer?.value,
      question,
      tableAnswerAccessor,
      documentId,
      questionTableHelper,
      displayColumnQuestionsById
    ]
  );

  const hotRef = useRef<HotTableRef>(null);

  const handleChange: (
    changes: Array<HandsontableCellChange | null>,
    source: ChangeSource
  ) => void = useCallback(
    changes => {
      if (changes === null || questionDisabled) {
        return false;
      }

      const changesByRow: ChangesByRow = {};

      const newRowIdByIndex = {} as Record<RowIndex, RowId>;
      const changedColumnIds = new Set<ColumnId>();
      changes?.forEach(cellChange => {
        if (!cellChange) {
          return;
        }
        const [rowIndex, colId, prevValue, nextValue] =
          cellChange as HandsontableCellChange;
        const columnId = String(colId); // Just for typescript typing warnings (we always have colId as string)
        const columnQuestion = displayColumnQuestionsById[columnId];
        if (!columnQuestion || columnQuestion.properties?.disabled) {
          return;
        }
        let rowId = getRowIdByIndex(rowIndex);
        if (!rowId) {
          newRowIdByIndex[rowIndex] ??= questionTableHelper.createRowId();
          rowId = newRowIdByIndex[rowIndex];
        }
        if (rowId && columnQuestion && prevValue !== nextValue) {
          changedColumnIds.add(columnId);
          changesByRow[rowId] ??= {};
          changesByRow[rowId][columnId] = getCellValueToSave({
            question: columnQuestion,
            value: nextValue,
            prevAnswer: (answer?.value as TableAnswer)?.entities?.[rowId]
              ?.columns?.[columnId]?.value as FormAnswer["value"]
          });
        }
      });

      if (changedColumnIds.size > 0) {
        applyAnswerChanges({
          changesByRow,
          changedColumnIds: Array.from(changedColumnIds),
          changedRowIds: Object.keys(changesByRow),
          newRowIdByIndex
        });
      }

      // Return false to prevent the default behavior of Handsontable
      return false;
    },
    [
      questionDisabled,
      displayColumnQuestionsById,
      getRowIdByIndex,
      questionTableHelper,
      answer?.value,
      applyAnswerChanges
    ]
  );

  const addRowAtIndex = useCallback(
    (newRowIndex: number) => {
      if (docChange) {
        docChange(d => {
          d.answers[question.id] ??= {
            questionId: question.id,
            value: {
              entities: {},
              order: []
            },
            type: QuestionTypes.TABLE
          };
          const newRowId = questionTableHelper.createRowId();
          (d.answers[question.id]?.value as TableAnswer).order.splice(
            newRowIndex,
            0,
            newRowId
          );
          (d.answers[question.id]?.value as TableAnswer).entities[newRowId] =
            questionTableHelper.createNewRow(newRowId);
        });
      }
    },
    [docChange, question.id, questionTableHelper]
  );

  const handleCreateRow: (
    index: RowIndex,
    amount: number,
    source?: ChangeSource
  ) => void = useCallback(
    (index, _, source) => {
      if (questionDisabled || !hotRef.current?.hotInstance) {
        return false;
      }
      let newRowIndex: RowIndex = index;
      if (source === "ContextMenu.rowBelow") {
        newRowIndex = index + 1;
      }

      addRowAtIndex(newRowIndex);
      hotRef.current?.hotInstance?.selectRows(newRowIndex, newRowIndex);

      return false;
    },
    [questionDisabled, addRowAtIndex]
  );

  const handleColumnResize: (
    width: number | undefined,
    columnIndex: ColumnIndex,
    isDoubleClick: boolean
  ) => void = useCallback(
    (width, columnIndex) => {
      if (fullScreenQuestion?.id === question.id) {
        setIsInitiallyRendered(false);
        return;
      }
      const column = displayColumnQuestions?.[columnIndex];
      if (!column) {
        return;
      }

      const currentColumnWidth = tableSettings.widths[column.id];
      if (width === currentColumnWidth) {
        return; // No change needed
      }

      annotationDocChange?.(d => {
        d.state ??= {
          sectionsCollapsed: {},
          tableQuestionSettings: {}
        };
        d.state.tableQuestionSettings ??= {};
        d.state.tableQuestionSettings[question.id] ??= {
          widths: {},
          heights: {},
          frozenColumnIds: []
        };
        d.state.tableQuestionSettings[question.id].widths ??= {};
        if (!width) {
          delete d.state.tableQuestionSettings[question.id].widths[column.id];
        } else {
          d.state.tableQuestionSettings[question.id].widths[column.id] = width;
        }
      });
    },
    [
      annotationDocChange,
      displayColumnQuestions,
      fullScreenQuestion?.id,
      question.id,
      tableSettings.widths
    ]
  );

  const handleRowResize: (
    width: number | undefined,
    rowIndex: RowIndex,
    isDoubleClick: boolean
  ) => void = useCallback(
    (height, rowIndex) => {
      if (fullScreenQuestion?.id === question.id) {
        setIsInitiallyRendered(false);
        return;
      }
      const rowId = getRowIdByIndex(rowIndex);
      if (!rowId) {
        return;
      }

      const currentRowHeight = tableSettings.heights[rowId];
      if (height === currentRowHeight) {
        return; // No change needed
      }

      annotationDocChange?.(d => {
        d.state ??= {
          sectionsCollapsed: {},
          tableQuestionSettings: {}
        };
        d.state.tableQuestionSettings ??= {};
        d.state.tableQuestionSettings[question.id] ??= {
          widths: {},
          heights: {},
          frozenColumnIds: []
        };
        d.state.tableQuestionSettings[question.id].heights ??= {};
        if (!height) {
          delete d.state.tableQuestionSettings[question.id].heights?.[rowId];
        } else {
          d.state.tableQuestionSettings[question.id].heights[rowId] = height;
        }
      });
    },
    [
      annotationDocChange,
      fullScreenQuestion?.id,
      getRowIdByIndex,
      question.id,
      tableSettings.heights
    ]
  );

  const contextMenu: HotTableProps["contextMenu"] = useMemo(
    () => ({
      items: {
        // freeze_column: {
        //   disabled: questionDisabled,
        //   hidden: function () {
        //     // If the column is already frozen, hide this option
        //     if (!hotRef?.current) {
        //       return;
        //     }
        //     // if first row, disable this option
        //     const selectedCellRange =
        //       hotRef?.current?.hotInstance?.getSelectedRange()?.[0];

        //     if (
        //       !selectedCellRange ||
        //       selectedCellRange.from.col === -1 ||
        //       selectedCellRange.from.row !== -1
        //     ) {
        //       return true; // Hide if not on the first row
        //     }
        //     return false;
        //   }
        // },
        // unfreeze_column: {
        //   hidden: function () {
        //     // If the column is not frozen, hide this option
        //     if (!hotRef?.current) {
        //       return true;
        //     }
        //     const selectedCellRange =
        //       hotRef?.current?.hotInstance?.getSelectedRange()?.[0];
        //     if (
        //       !selectedCellRange ||
        //       selectedCellRange.from.col === -1 ||
        //       selectedCellRange.from.row !== -1
        //     ) {
        //       return true; // Hide if not on the first row
        //     }
        //     return false;
        //   }
        // },
        clear_column: {
          disabled: questionDisabled,
          hidden: function () {
            // If the column is already frozen, hide this option
            if (!hotRef?.current) {
              return true;
            }
            const selectedCellRange =
              hotRef?.current?.hotInstance?.getSelectedRange()?.[0];

            if (
              !selectedCellRange ||
              selectedCellRange.from.col === -1 ||
              selectedCellRange.from.row !== -1
            ) {
              return true; // Hide if not column header
            }
            return false;
          }
        },
        reset_column_width: {
          name: "Reset column width",
          hidden: function () {
            // If the column has no width
            if (fullScreenQuestion || !hotRef?.current) {
              return true;
            }
            const selectedCellRange =
              hotRef?.current?.hotInstance?.getSelectedRange()?.[0];
            if (
              !selectedCellRange ||
              selectedCellRange.from.col === -1 ||
              selectedCellRange.from.row !== -1
            ) {
              return true; // Hide if not column header
            }
            const columnIndex = selectedCellRange.from.col;
            const column = displayColumnQuestions?.[columnIndex];
            if (!column) {
              return true; // Hide if no column is selected
            }

            const currentColumnWidth = tableSettings.widths[column.id];
            if (currentColumnWidth === undefined) {
              return true; // Hide if the column has no width set
            }
            return false;
          },
          callback: function () {
            if (!hotRef?.current) {
              return;
            }
            const selectedCellRange =
              hotRef?.current?.hotInstance?.getSelectedRange()?.[0];
            if (
              !selectedCellRange ||
              selectedCellRange.from.col === -1 ||
              selectedCellRange.from.row !== -1
            ) {
              return; // Hide if not on the first row
            }
            const columnIndexFrom = selectedCellRange.from.col;
            const columnIndexTo = selectedCellRange.to.col;

            range(columnIndexFrom, columnIndexTo + 1).forEach(columnIndex => {
              handleColumnResize(
                undefined,
                columnIndex,
                false // Not a double click
              );
            });
          }
        },
        row_above: {
          disabled: questionDisabled,
          hidden: function () {
            if (!hotRef?.current) {
              return true;
            }
            const selectedCellRange =
              hotRef?.current?.hotInstance?.getSelectedRange()?.[0];
            if (!selectedCellRange || selectedCellRange.from.row === -1) {
              return true; // Hide in column header
            }
            return false;
          }
        },
        row_below: {
          disabled: questionDisabled,
          hidden: function () {
            if (!hotRef?.current) {
              return true;
            }
            const selectedCellRange =
              hotRef?.current?.hotInstance?.getSelectedRange()?.[0];
            if (!selectedCellRange || selectedCellRange.from.row === -1) {
              return true; // Hide in column header
            }
            return false;
          }
        },
        "---------": {
          name: "---------",
          disabled: true,
          hidden: function () {
            if (!hotRef?.current) {
              return true;
            }
            const selectedCellRange =
              hotRef?.current?.hotInstance?.getSelectedRange()?.[0];
            if (
              !selectedCellRange ||
              selectedCellRange.from.row === -1 ||
              selectedCellRange.from.col !== -1
            ) {
              return true; // Hide if not row header
            }
            return false;
          }
        },
        remove_row: {
          disabled: questionDisabled,
          hidden: () => {
            if (!hotRef?.current) {
              return true;
            }
            const selectedCellRange =
              hotRef?.current?.hotInstance?.getSelectedRange()?.[0];
            if (
              !selectedCellRange ||
              selectedCellRange.from.row === -1 ||
              selectedCellRange.from.col !== -1
            ) {
              return true; // Hide if not row header
            }
            return false;
          }
        }
      }
    }),
    [
      displayColumnQuestions,
      fullScreenQuestion,
      handleColumnResize,
      questionDisabled,
      tableSettings.widths
    ]
  );

  // const handleColumnFreeze =
  //   (type: "freeze" | "unfreeze" = "freeze") =>
  //   (columnIndex: number, isFreezingPerformed: boolean) => {
  //     if (fullScreenQuestion?.id === question.id) {
  //       return;
  //     }
  //     const column = displayColumnQuestions?.[columnIndex];
  //     if (!column) {
  //       return;
  //     }

  //     annotationDocChange?.(d => {
  //       d.state ??= {
  //         sectionsCollapsed: {},
  //         tableQuestionSettings: {}
  //       };
  //       d.state.tableQuestionSettings ??= {};
  //       d.state.tableQuestionSettings[question.id] ??= {
  //         heights: {},
  //         widths: {},
  //         frozenColumnIds: []
  //       };
  //       d.state.tableQuestionSettings[question.id].frozenColumnIds ??= [];
  //       if (type === "freeze") {
  //         d.state.tableQuestionSettings[question.id].frozenColumnIds.push(
  //           column.id
  //         );
  //       } else {
  //         d.state.tableQuestionSettings[question.id].frozenColumnIds =
  //           d.state.tableQuestionSettings[question.id].frozenColumnIds.filter(
  //             id => id !== column.id
  //           );

  //         setTimeout(() => {
  //           // refresh
  //           const hot = hotRef.current?.hotInstance;
  //           // Get index
  //           const originalIndex = displayColumnQuestions?.findIndex(
  //             c => c.id === column.id
  //           );

  //           console.log(
  //             "originalIndex",
  //             originalIndex,
  //             "columnIndex",
  //             columnIndex
  //           );
  //           hot
  //             ?.getPlugin("manualColumnMove")
  //             .moveColumn(columnIndex, originalIndex);

  //           // hotRef.current?.hotInstance?.refreshDimensions();
  //           // setIsInitiallyRendered(false);
  //         }, 1000); // Buffer time for doc update
  //       }
  //     });

  //     return false;
  //   };

  return (
    <Stack className="question-table-view__container" gap="150">
      {showTabsAndChart && (
        <QuestionTableAnswerChartTabs
          chartsCanBeDisplayed={chartsCanBeDisplayed}
          tableViewPanelTab={tableViewPanelTab}
          setTableViewPanelTab={setTableViewPanelTab}
          chartAnswer={chartAnswer}
          parentQuestion={question}
          d={d}
        />
      )}
      {(!showTabsAndChart ||
        tableViewPanelTab === TableDefaultView.ALL ||
        tableViewPanelTab === TableDefaultView.TABLE) && (
        <Stack gap="100">
          {collectionFormId && (
            <TableFiltersAndActions
              question={question}
              collectionFormId={collectionFormId}
              questionDisabled={questionDisabled}
              fullScreenQuestion={fullScreenQuestion}
              setFullScreenQuestion={setFullScreenQuestion}
              hotRef={hotRef}
              handleResetDisplay={
                annotationDocChange && !fullScreenQuestion
                  ? () => {
                      annotationDocChange(
                        d =>
                          delete d.state?.tableQuestionSettings?.[question.id]
                      );
                      setIsInitiallyRendered(false);
                    }
                  : undefined
              }
            />
          )}

          <Box
            className="question-table-answer__container"
            width="100"
            overflow="auto"
            style={{
              zIndex: 0
            }}
            position="relative"
          >
            <Stack id={`question-table-answer-${question.id}`}>
              <HotTable
                style={{
                  visibility: isInitiallyRendered ? "visible" : "hidden",
                  opacity: isInitiallyRendered ? 1 : 0
                }}
                ref={hotRef}
                afterViewRender={() => {
                  setIsInitiallyRendered(true);
                }}
                // Until we have heights of rows saved
                renderAllColumns={true}
                autoRowSize={false}
                autoColumnSize={false}
                className="question-table-answer__content"
                themeName="ht-theme-main"
                data={rowValues}
                height={() => {
                  if (!hotRef.current || tableHeight < 440) {
                    return "auto";
                  } else if (fullScreenQuestion?.id === question.id) {
                    // If in full screen mode, return 70% of the screen height
                    return `75dvh`;
                  }
                  return tableHeight;
                }}
                rowHeaders
                licenseKey={licenseKey}
                manualColumnFreeze
                search
                minCols={columnProps?.length}
                maxCols={columnProps?.length}
                width="100%"
                contextMenu={contextMenu}
                beforeChange={handleChange}
                beforeCreateRow={handleCreateRow}
                afterRemoveRow={handleRemoveRows}
                manualRowMove={!questionDisabled}
                beforeRowMove={handleMoveRows}
                rowHeaderWidth={rowHeaderWidth}
                columnHeaderHeight={columnHeaderHeight}
                manualColumnResize={fullScreenQuestion ? true : columnWidths}
                colWidths={fullScreenQuestion ? columnWidths : undefined}
                manualRowResize={fullScreenQuestion ? true : rowHeights}
                rowHeights={fullScreenQuestion ? rowHeights : undefined}
                afterColumnResize={handleColumnResize}
                afterRowResize={handleRowResize}
                // TODO: add back when storing row heights
                // afterColumnFreeze={
                //   fullScreenQuestion ? undefined : handleColumnFreeze("freeze")
                // }
                // afterColumnUnfreeze={
                //   fullScreenQuestion ? undefined : handleColumnFreeze("unfreeze")
                // }
              >
                {columnProps.map(settings => (
                  <HotColumn key={settings.data} {...settings} />
                ))}
              </HotTable>
            </Stack>
            {!questionDisabled && hotRef.current && (
              <TableAddRowButton
                addNewRow={() => {
                  const newRowIndex = rowValues.length;
                  addRowAtIndex(newRowIndex);

                  // Scroll to the newly created row
                  setTimeout(() => {
                    const hotInstance = hotRef.current?.hotInstance;
                    if (hotInstance) {
                      hotInstance.selectCell(newRowIndex, 0);
                      hotInstance.scrollViewportTo({
                        row: newRowIndex,
                        col: 0
                      });
                    }
                  }, 500);
                }}
              />
            )}
          </Box>
        </Stack>
      )}
    </Stack>
  );
};

const TableFiltersAndActions = ({
  question,
  collectionFormId,
  questionDisabled,
  fullScreenQuestion,
  setFullScreenQuestion,
  hotRef,
  handleResetDisplay = () => {}
}: {
  question: Question<TableQuestionProperties>;
  collectionFormId: number;
  questionDisabled?: boolean;
  fullScreenQuestion?: Question;
  setFullScreenQuestion: (question?: Question) => void;
  hotRef: React.RefObject<HotTableRef>;
  handleResetDisplay?: () => void;
}) => {
  const [searchValue, setSearchValue] = React.useState("");
  const [currentSearchIndex, setCurrentSearchIndex] = React.useState(0);
  const [searchResultsCount, setSearchResultsCount] = React.useState<number>(0);

  const handleChangeSearch = useCallback(
    (value: string) => {
      const hot = hotRef.current?.hotInstance;
      const search = hot?.getPlugin("search");
      const queryResult = search?.query(value);
      setSearchValue(value);
      setSearchResultsCount(queryResult?.length || 0);
      setCurrentSearchIndex(0);
      if (hot && queryResult && queryResult.length > 0) {
        const firstResult = queryResult[0];
        hot?.scrollViewportTo(firstResult.row, firstResult.col);
      }
      hot?.render();
    },
    [hotRef]
  );

  useEffect(() => {
    if (!searchResultsCount) {
      return;
    }
    const hot = hotRef.current?.hotInstance;
    const search = hot?.getPlugin("search");
    const queryResult = search?.query(searchValue);

    if (queryResult && queryResult.length > 0) {
      const result = queryResult[currentSearchIndex];
      hot?.scrollViewportTo(result.row, result.col);
    }
    hot?.render();
  }, [currentSearchIndex, hotRef, searchResultsCount, searchValue]);

  return (
    <Inline width="100" spaceBetween alignment="left">
      <Inline gap="050" alignment="left">
        <Inline style={{ width: "120px" }}>
          <SearchBar
            debounceTime={200}
            onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
              if (event.key === "Enter") {
                event.preventDefault();
                event.stopPropagation();
                setCurrentSearchIndex(current => {
                  if (current === searchResultsCount - 1) {
                    return 0;
                  }
                  return Math.min(current + 1, searchResultsCount - 1);
                });
              }
            }}
            placeholder="Search"
            width="100"
            value={searchValue}
            handleChange={handleChangeSearch}
          />
        </Inline>
        {searchResultsCount > 0 && (
          <Inline>
            <IconButton
              name="chevron_left"
              size="s"
              onClick={() =>
                setCurrentSearchIndex(current => {
                  return Math.max(0, current - 1);
                })
              }
              disabled={currentSearchIndex === 0}
            />
            <Inline alignment="center" gap="025" paddingHorizontal="050">
              <Text color="text-secondary" weight="medium">
                {String(currentSearchIndex + 1)} / {searchResultsCount}
              </Text>
            </Inline>
            <IconButton
              name="chevron_right"
              size="s"
              onClick={() =>
                setCurrentSearchIndex(current => {
                  return Math.min(current + 1, searchResultsCount - 1);
                })
              }
              disabled={currentSearchIndex === searchResultsCount - 1}
            />
          </Inline>
        )}
      </Inline>
      <Inline gap="150">
        <IconButton
          size="s"
          onClick={() => {
            if (fullScreenQuestion?.id === question.id) {
              setFullScreenQuestion(undefined);
              return;
            }
            setFullScreenQuestion(question);
          }}
          name={
            fullScreenQuestion?.id === question.id
              ? "fullscreen_exit"
              : "fullscreen"
          }
        />

        <QuestionTableAnswerKebabMenu
          collectionFormId={collectionFormId}
          question={question}
          questionDisabled={questionDisabled}
          handleResetDisplay={handleResetDisplay}
        />
      </Inline>
    </Inline>
  );
};

const TableAddRowButton = ({ addNewRow }: { addNewRow: () => void }) => {
  return (
    <Box
      className="question-table-answer__add-row-button"
      style={{
        width: `${rowHeaderWidth}px`
      }}
      alignment="center"
      position="absolute"
    >
      <Box
        style={{
          backgroundColor: "var(--color-surface-secondary)",
          borderRadius: "50%"
        }}
      >
        <IconButton
          name="add_circle"
          fillStyle="filled"
          color="text-disabled"
          size="s"
          onClick={addNewRow}
        />
      </Box>
    </Box>
  );
};
