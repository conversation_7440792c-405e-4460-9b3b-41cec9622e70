import React, { useEffect } from "react";
import { useOutletContext } from "react-router-dom";

import { useConfigurationOptions } from "@src/hooks/formConfiguration/select/useConfigurationOptions";
import { DynamicOptionsReturnType } from "@src/types/DynamicSelectOptions.ts";
import { SelectQuestionProperties } from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

interface OTAIFormFieldSelectOptionsProps {
  dynamicOptions: SelectQuestionProperties["dynamicOptions"];
  onOptionsFetched: (options: DynamicOptionsReturnType) => void;
}

const OTAIFormFieldSelectOptionsFromConfiguration: React.FC<
  OTAIFormFieldSelectOptionsProps
> = ({ dynamicOptions, onOptionsFetched }) => {
  const { document } = useOutletContext<{ document: WorkspaceDocument }>();

  const options = useConfigurationOptions(dynamicOptions, document);

  useEffect(() => {
    if (options) {
      onOptionsFetched(options);
    }
  }, [options, onOptionsFetched]);

  return null;
};

export default OTAIFormFieldSelectOptionsFromConfiguration;
