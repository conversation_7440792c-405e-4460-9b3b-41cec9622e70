package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MinTest {
    private val fn = Min

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName([])", BigDecimal(0), null, "Returns 0 for an empty array"),
            Spec("$$functionName(1)", BigDecimal(1), null, ""),
            Spec("$$functionName(2, 3)", BigDecimal(2), null, "Returns the minimum of two numbers"),
            Spec("$$functionName(3, 2, 'a')", BigDecimal(2), null, "Ignores non-numeric values"),
            Spec("$$functionName(2, \"1\")", BigDecimal(1), null, "Handles numbers as strings and returns the minimum"),
            Spec("$$functionName([1, 2, 3, 4, 5])", BigDecimal(1), null, "Returns the minimum value in an array"),
// TODO need to standardise on BigDecimals in another pr
//            Spec(
//                "$$functionName([[1, 2, 3]])",
//                listOf(listOf(BigDecimal(1), BigDecimal(2), BigDecimal(3))),
//                "Returns the minimum value in an array"
//            ),
            Spec(
                "$$functionName([1, 2, 'a', 4, 5])",
                BigDecimal(1), null,
                "Returns the minimum value in an array, ignoring non-numeric values"
            ),
            Spec(
                "$$functionName([\"1\", 2, \"3\", 4, 5])",
                BigDecimal(1), null,
                "Handles mixed types in an array and returns the minimum"
            ),
            Spec(
                "$$functionName([1, 2, 3], 4)",
                listOf(BigDecimal(1), BigDecimal(2), BigDecimal(3)), null,
                "Performs pairwise comparison with a larger number"
            ),
            Spec(
                "$$functionName([1, 2, 3], -4)",
                listOf(BigDecimal(-4), BigDecimal(-4), BigDecimal(-4)), null,
                "Performs pairwise comparison with a smaller number"
            ),
            Spec(
                "$$functionName([4, 5], [[1, 2, 3], [5, 4, 3]])",
                listOf(
                    listOf(BigDecimal(1), BigDecimal(2), BigDecimal(0)),
                    listOf(BigDecimal(4), BigDecimal(4), BigDecimal(0))
                ), null,
                "Handles nested arrays and returns the pairwise minimum"
            ),
            Spec("$$functionName(0.1, 0.2)", BigDecimal("0.1"), null, "Returns the minimum of two fractional numbers"),
            Spec(
                "$$functionName([[1,2,3]],[[4,-5,6]])",
                listOf(listOf(BigDecimal("1"), BigDecimal("-5"), BigDecimal("3"))), null,
                "Returns the minimum of two fractional numbers"
            ),
            Spec(
                "$$functionName('', \"2026-01-01\")",
                "2026-01-01",
                null,
                "Ignores empty string and returns the date string"
            ),
            Spec(
                "$$functionName([\"2026-01-01\", \"2025-01-01\", \"2024-01-01\"])",
                "2024-01-01",
                null,
                "Returns the earliest date from a list of date strings"
            ),
            Spec(
                "$$functionName(\"2026-01-01\", \"2025-01-01\", \"2024-01-01\")",
                "2024-01-01",
                null,
                "Returns the earliest date from a list of date strings"
            ),
            Spec(
                "$$functionName(['not-a-date', 'also-bad'])",
                BigDecimal(0),
                null,
                "Returns 0 for all invalid date strings"
            ),
            Spec(
                "$$functionName([true, false, {foo: 'bar'}, 7])",
                BigDecimal(7),
                null,
                "Ignores non-string, non-number types"
            ),
            Spec(
                "$$functionName('2023-01-01T12:00:00', '2023-01-01T13:00:00')",
                BigDecimal(0),
                null,
                "Returns 0 for all invalid date strings"
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

}