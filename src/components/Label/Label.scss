.label {
  position: relative;
  display: flex;
  gap: var(--spacing-050);
  justify-content: space-between;
  text-overflow: ellipsis;
  align-items: flex-start;

  &__container--with-error {
    & .text-field,
    .text-area-field,
    .color-picker,
    .select-field,
    .date-picker,
    .date-range-picker,
    .number-picker,
    .search-bar {
      &__value {
        background-color: var(--components-inputs-error-color-background);
        outline: 1px solid var(--components-inputs-error-color-border);
      }
    }
  }
  &__content {
    width: fit-content;
  }
  &__text {
    user-select: text;
    text-align: left;
    max-width: -webkit-fill-available;
    color: var(--components-label-color-text, #021429);
    font-size: var(--components-label-font-size, 14px);
    font-weight: var(--components-label-font-weight, 500);
    letter-spacing: var(--components-label-letter-spacing, 0px);
  }

  &__description {
    user-select: text;
    font-size: var(--components-label-description-font-size, 13px);
    color: var(--components-label-description-color-text, #4f5c70);
    font-weight: var(--components-label-description-font-weight, 400);
    letter-spacing: var(--components-label-letter-spacing, 0px);
  }

  &__required {
    z-index: 1;
    color: var(--components-label-required-symbol-color-text, #7738f1);
    font-size: var(--components-label-required-symbol-font-size, 14px);
    font-weight: var(--components-label-required-symbol-font-weight, 600);
    align-self: center;
  }

  &__error .text {
    font-size: var(--components-label-error-font-size, 12px);
    font-weight: var(--components-label-error-font-weight, 400);
  }

  &--disabled {
    & .label__text {
      color: var(--components-label-disabled-color-text, #bdc3d6);
    }
    & .label__description {
      color: var(--components-label-disabled-description-color-text, #bdc3d6);
    }
    & .label__required {
      color: var(
        --components-label-required-symbol-disabled-color-text,
        #bdc3d6
      );
    }
  }

  &--textPosition {
    &-top {
      flex-direction: column;
      gap: 1px;
    }
    &-left {
      align-items: center;
      justify-content: flex-start;
    }
    &-right {
      align-items: center;
      flex-direction: row-reverse;
      justify-content: flex-end;
    }
  }

  &--on-top {
    & .label__content {
      z-index: 1;
      position: absolute;
      top: -12px;
      left: 2px;
      margin-bottom: -var(--spacing-100);
      padding: 0 2px;
      border-radius: 2px;
      background-color: white;
      width: fit-content;
    }
  }
}
