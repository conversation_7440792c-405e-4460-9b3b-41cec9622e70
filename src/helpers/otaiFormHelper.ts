import { InternalOTAIFormField } from "@components/shared/OTAIForm/OTAIFormType";

export const internalOtaiFormField = ({
  id,
  identifier,
  ...field
}: InternalOTAIFormField): InternalOTAIFormField => {
  return {
    ...field,
    id: id ?? identifier,
    identifier
  };
};

export const internalOtaiFormFields = (
  content: InternalOTAIFormField[]
): InternalOTAIFormField[] =>
  content.map(field => internalOtaiFormField(field));
