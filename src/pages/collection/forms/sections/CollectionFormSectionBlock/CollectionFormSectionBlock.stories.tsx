import React from "react";

import { Form } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { z } from "zod";

import { sectionSchema } from "@src/types/FormConfiguration.ts";

import { CollectionFormSectionBlock } from "./CollectionFormSectionBlock.tsx";

const meta: Meta<typeof CollectionFormSectionBlock> = {
  component: CollectionFormSectionBlock,
  title: "collection/CollectionFormSectionBlock"
};

export default meta;

type Story = StoryObj<typeof CollectionFormSectionBlock>;

const mockSection = {
  id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
  name: "Section Name",
  level: 1,
  content: []
};
const defaultArgs: Story["args"] = {};

const defaultRenderer = (args: Story["args"]) => {
  return (
    <Form
      schema={z.object({ section: sectionSchema((key: string) => key) })}
      d={(key: string) => key}
      handleSubmit={() => console.log("Submit")}
      defaultValues={{
        section: mockSection
      }}
      hideFormButtons
    >
      <CollectionFormSectionBlock {...args} section={mockSection}>
        Content
      </CollectionFormSectionBlock>
    </Form>
  );
};

export const Section: Story = {
  args: defaultArgs,
  render: defaultRenderer
};

const mockSubSection = {
  id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
  name: "Sub-Section Name",
  level: 2,
  content: []
};

const subSectionRenderer = (args: Story["args"]) => {
  return (
    <Form
      schema={z.object({ section: sectionSchema((key: string) => key) })}
      d={(key: string) => key}
      handleSubmit={() => console.log("Submit")}
      defaultValues={{
        section: mockSubSection
      }}
      hideFormButtons
    >
      <CollectionFormSectionBlock {...args} section={mockSubSection}>
        Content
      </CollectionFormSectionBlock>
    </Form>
  );
};

export const SubSection: Story = {
  args: defaultArgs,
  render: subSectionRenderer
};

const mockSectionSubSection = {
  id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
  name: "Section Name",
  level: 1,
  content: [
    {
      id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
      name: "General",
      level: 2,
      content: []
    },
    {
      id: "9ec436c1-cf91-4e23-a58a-bfb2e5c5dc62",
      name: "General 2",
      level: 2,
      content: []
    }
  ]
};

const sectionSubSectionRenderer = (args: Story["args"]) => {
  return (
    <Form
      schema={z.object({ section: sectionSchema((key: string) => key) })}
      d={(key: string) => key}
      handleSubmit={() => console.log("Submit")}
      defaultValues={{
        section: mockSectionSubSection
      }}
      hideFormButtons
    >
      <CollectionFormSectionBlock {...args} section={mockSectionSubSection}>
        <CollectionFormSectionBlock section={mockSectionSubSection.content[0]}>
          Content
        </CollectionFormSectionBlock>

        <CollectionFormSectionBlock section={mockSectionSubSection.content[1]}>
          Content
        </CollectionFormSectionBlock>
      </CollectionFormSectionBlock>
    </Form>
  );
};

export const SectionSubSection: Story = {
  args: defaultArgs,
  render: sectionSubSectionRenderer
};
