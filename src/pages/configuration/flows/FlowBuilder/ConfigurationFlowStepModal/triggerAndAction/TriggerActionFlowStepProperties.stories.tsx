import React from "react";

import { Form } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";

import { commonIcons } from "@src/constants/iconConstants.ts";

import { TriggerActionFlowStepProperties } from "./TriggerActionFlowStepProperties.tsx";

const meta: Meta<typeof TriggerActionFlowStepProperties> = {
  component: TriggerActionFlowStepProperties,
  title: "collection/ConfigurationFlowStepModal/triggerAndAction"
};

export default meta;

type Story = StoryObj<typeof TriggerActionFlowStepProperties>;

const mockTriggerStepType = {
  primaryIdentifier: "manualTriggerFromFoundation",
  name: "Manual trigger from a foundation",
  description: "Trigger the flow manually from a foundation in collection",
  properties: {
    icon: {
      name: commonIcons.foundations
    },
    isLocked: true,
    isHidden: false,
    configuration: {
      content: [
        {
          text: "Foundation level to appear on",
          type: "select", // or select for dynamic select
          identifier: "foundationConfigurationId",
          properties: {
            required: true,
            options: undefined,
            dynamicOptions: {
              tag: "foundationConfigurationId"
            }
          }
        },
        {
          text: "Button label",
          type: "string",
          identifier: "buttonLabel",
          properties: {
            required: true
          }
        }
      ]
    }
  }
};

const mockTrigger = {
  step: {
    // stepId (trigger)
    id: 13,
    name: "Manual trigger from a foundation", // default to the name of the trigger type
    properties: {
      // flowStepTypeId
      typePrimaryIdentifier: 1, // manualTriggerFromForm
      inputs: {
        // user answers to the answerChanged.content
        buttonLabel: "Run foundation flow",
        foundationConfigurationId: "21312"
      }
    }
  }
};

const triggerArgs: Story["args"] = {
  step: mockTrigger.step
};

export const Trigger: Story = {
  args: triggerArgs,
  render: (args: Story["args"]) => {
    return (
      <Form
        defaultValues={mockTrigger.step.properties.inputs}
        handleSubmit={() => {}}
        hideFormButtons
        d={d => d}
      >
        <TriggerActionFlowStepProperties
          step={args?.step}
          stepType={mockTriggerStepType}
        />
      </Form>
    );
  }
};

const mockActionStepType = {
  primaryIdentifier: "createForm",
  name: "Create a form",
  description: "Create a form for a foundation",
  properties: {
    icon: {
      name: "add_box",
      fillStyle: "outlined"
    },
    isLocked: true,
    isHidden: false,
    configuration: {
      content: [
        {
          text: "Foundation",
          type: "foundationId", // or select for dynamic select
          identifier: "foundationId",
          properties: {
            // if it's visible it will be required
            required: true
          }
        },
        {
          text: "Form",
          type: "formConfigurationId", // or select for dynamic select
          identifier: "formConfigurationId",
          properties: {
            required: true
          }
        },
        {
          text: "Series interval",
          type: "{{thisStep.formConfiguration}}.intervalId", // or select for dynamic select
          identifier: "intervalId",
          properties: {
            required: true
          }
        }
      ]
    }
  }
};

const mockAction = {
  // stepId (action)
  id: 13,
  variant: "action",
  name: "Create a form", // default to the name of the action type
  properties: {
    // flowStepTypeId
    typePrimaryIdentifier: "createForm", // createForm
    inputs: {
      // user answers to the answerChanged.content
      foundationId: 100,
      formConfigurationId: "2312",
      // may not be here depending on form configuration
      intervalId: "1231"
    }
  }
};

const actionArgs: Story["args"] = {
  step: mockAction
};

export const Action: Story = {
  args: actionArgs,
  render: (args: Story["args"]) => {
    return (
      <Form
        defaultValues={mockTrigger.step.properties.inputs}
        handleSubmit={() => {}}
        d={d => d}
        hideFormButtons
      >
        <TriggerActionFlowStepProperties
          step={args?.step}
          stepType={mockActionStepType}
        />
      </Form>
    );
  }
};
