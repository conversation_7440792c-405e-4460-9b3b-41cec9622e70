import { Prop } from "@automerge/automerge-repo";

import { DocNav } from "@helpers/configurationFormHelper.ts";

import { AutomergeDocument } from "@src/types/documentTypes.ts";

export function getElementByPath(obj: DocNav, path: Prop[]) {
  return path.reduce((acc: DocNav, key: Prop) => {
    // @ts-expect-error - TS doesn't like this, but it's fine
    const result = acc?.[key];
    if (result === undefined) {
      // @ts-expect-error - TS doesn't like this, but it's fine
      return acc?.find?.(item => item?.id === key);
    }
    return result;
  }, obj);
}

export function getByPath<T>(obj: AutomergeDocument, path: Prop[]) {
  return getElementByPath(obj as unknown as DocNav, path) as T;
}
