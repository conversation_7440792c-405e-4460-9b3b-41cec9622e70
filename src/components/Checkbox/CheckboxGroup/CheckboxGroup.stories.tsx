import React, { useState } from "react";

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { CheckboxGroup, CheckboxGroupOptionValue } from "./CheckboxGroup.tsx";

const meta: Meta<typeof CheckboxGroup> = {
  component: CheckboxGroup,
  title: "components/Checkbox/CheckboxGroup"
};

export default meta;

type Story = StoryObj<typeof CheckboxGroup>;

const defaultArgs: Story["args"] = {
  options: [
    { value: "1", label: "Option 1" },
    { value: "2", label: "Option 2" },
    { value: "3", label: "Option 3" }
  ],
  value: ["1"],
  label: "Label"
};

const defaultRender = (args: Story["args"]) => {
  const [value, setValue] = useState<CheckboxGroupOptionValue | undefined>(
    args?.value ?? []
  );
  return (
    <CheckboxGroup
      {...args}
      id="test"
      value={value}
      onChange={value => setValue(value)}
      options={args?.options ?? []}
    />
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRender
};

export const Column: Story = {
  args: { ...defaultArgs, direction: "column" },
  render: defaultRender
};

export const Disabled: Story = {
  args: { ...defaultArgs, disabled: true },
  render: defaultRender
};

export const WithoutLabel: Story = {
  args: { ...defaultArgs, label: undefined },
  render: defaultRender
};
