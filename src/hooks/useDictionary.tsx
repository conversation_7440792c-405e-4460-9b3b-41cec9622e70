import { useCallback } from "react";

import get from "lodash/get";

import { useTenantConfig } from "@src/hooks/useTenantConfig.tsx";

import dictionary from "@assets/dictionary.json";

type Replacements = { [key: string]: string | number | undefined };

export type Dictionary = (
  key: string,
  replacements?: Replacements | undefined,
  locale?: string
) => string;

type DictionaryTransformerFunction = (
  value: string,
  locale: string,
  replacements?: Replacements | undefined
) => string;

// Custom functions
const lowercase: DictionaryTransformerFunction = (
  str,
  locale,
  replacements
) => {
  const value = get(replacements, str, str) as string; // if not found, it returns original value
  return value.toLocaleLowerCase(locale);
};

const uppercase: DictionaryTransformerFunction = (
  str,
  locale,
  replacements
) => {
  const value = get(replacements, str, str) as string; // if not found, it returns original value
  return value.toLocaleUpperCase(locale);
};

const capitalize: DictionaryTransformerFunction = (
  str,
  locale,
  replacements
) => {
  const value = get(replacements, str, str) as string;
  return value.charAt(0).toLocaleUpperCase(locale) + value.slice(1);
};

const customFunctions: { [name: string]: DictionaryTransformerFunction } = {
  lowercase,
  uppercase,
  capitalize
};

const getDictionaryValue: Dictionary = (key, replacements, locale) => {
  let value = get(dictionary, key, key);

  // Interpolation
  value = value?.replace?.(/{{(.*?)}}/gm, (value: string) => {
    let [valueWithoutBrackets, customFunction] = value
      .replace(/{{|}}/gm, "")
      .split(",")
      .map(v => v.trim());

    // If value without brackets is nested then getDictionaryValue
    if (valueWithoutBrackets.match(/\$d\((.*?)\)/gm)) {
      valueWithoutBrackets = getDictionaryValue(
        valueWithoutBrackets,
        replacements,
        locale
      );
    }

    if (customFunction && customFunctions[customFunction]) {
      valueWithoutBrackets = customFunctions[customFunction](
        valueWithoutBrackets,
        locale ?? "en-AU",
        replacements
      );
    }

    return get(
      replacements,
      valueWithoutBrackets,
      valueWithoutBrackets
    ) as string;
  });

  // Nesting $d(key)
  value = value?.replace(/\$d\((.*?)\)/gm, (value: string) => {
    const valueWithoutBrackets = value.replace(/\$d\(|\)/gm, "");
    return getDictionaryValue(valueWithoutBrackets, replacements, locale);
  });

  return value;
};

export const useDictionary = (): Dictionary => {
  const tenantConfig = useTenantConfig();
  const locale = tenantConfig?.locale;
  return useCallback(
    (key, replacements) => getDictionaryValue(key, replacements, locale),
    [locale]
  );
};
