import { FlowStepVariant } from "@src/types/FlowConfiguration/FlowStep/FlowStep.ts";
import {
  WorkspaceDocument,
  emptyWorkspaceDocument
} from "@src/types/documentTypes.ts";

export const MockFlowStep = {
  "3": {
    id: "3",
    variant: FlowStepVariant.CONDITION,
    properties: {
      // evaluated in order, like an if, else if, else if, else (fallbackNext)
      branches: [
        {
          name: "Check if x",
          // check if any or or and have brackets
          condition: {
            AND: [
              {
                lhs: 5000,
                operator: ">",
                rhs: 0
              },
              {
                lhs: "",
                operator: "isEmpty"
              },
              {
                OR: [
                  {
                    lhs: "hello world",
                    operator: "contains",
                    rhs: "hello"
                  },
                  {
                    lhs: "2025-01-01",
                    operator: ">",
                    rhs: "2020-01-01"
                  },
                  {
                    AND: [
                      {
                        lhs: "false",
                        operator: "=",
                        rhs: "false"
                      },
                      {
                        lhs: true,
                        operator: "!=",
                        rhs: false
                      }
                    ]
                  }
                ]
              },
              {
                AND: [
                  {
                    lhs: 5000,
                    operator: "=",
                    rhs: 5000
                  },
                  {
                    lhs: "hello world",
                    operator: "doesNotContain",
                    rhs: "goodbye"
                  }
                ]
              }
            ]
          },
          next: "4" // raise a warning
        },
        {
          name: "Check if y",
          condition: {
            lhs: 5000,
            operator: ">",
            rhs: 0
          },
          next: undefined // no next step
        }
      ],
      // else // we don't do anything if there is no warning
      typePrimaryIdentifier: "MockCondition"
    },
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  },
  "4": {
    id: "4",
    name: "Sum all amounts",
    variant: FlowStepVariant.SET_VARIABLES,
    properties: {
      typePrimaryIdentifier: "MockSetVariables",
      variables: [
        {
          // points to the variable ID on the flow configurat
          type: "number", // we could determine this (or worst case the user)
          identifier: "taxAmount",
          value: "={{formA.income}} * {{formA.taxRate}}"
        },
        {
          type: "number", // we could determine this (or worst case the user)
          identifier: "netIncome",
          value: "={{formA.income}} - {{taxAmount}}"
        },
        {
          type: "number",
          identifier: "totalAmount",
          value: "5000"
        },
        {
          type: "text",
          identifier: "helloWorld",
          value: "hello world"
        }
      ]
    },
    next: "6",
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }
};

export const MockFlowForStory = {
  steps: MockFlowStep,
  name: "Mock Flow",
  description: "Mock Flow",
  id: "1",
  metadata: {
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  start: "3",
  triggers: {
    "13": {
      id: "13",
      properties: {
        typePrimaryIdentifier: "manualTriggerFromFoundation",
        inputs: { buttonLabel: "Run foundation flow" }
      },
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }
  }
};

export const sampleWorkspaceDocumentForFlow: WorkspaceDocument = {
  ...emptyWorkspaceDocument,
  id: 1,
  name: "Workspace",
  key: "WORKSPACE",
  metadata: {
    createdAt: "2021-08-20T06:00:00Z",
    updatedAt: "2021-08-20T06:00:00Z"
  },
  flows: {
    entities: {
      "flow-id-1": MockFlowForStory
    },
    order: ["flow-id-1"]
  }
};
