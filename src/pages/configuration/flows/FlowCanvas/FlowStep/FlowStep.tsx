import React, { useMemo, useRef } from "react";
import { useOutletContext } from "react-router-dom";

import { Doc } from "@automerge/automerge-repo";
import {
  Box,
  Divider,
  Heading,
  HeadingSize,
  Icon,
  Inline,
  Stack,
  Tooltip,
  WhiteboardBlock,
  getClassNames
} from "@oneteam/onetheme";

import { getVariablesForStep } from "@helpers/flows/flowVariables/flowVariableHelpers";

import { WhiteboardStatusCircle } from "@components/shared/WhiteboardStatusCircle.tsx";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { ConfigurationFlowVariable } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowVariable/ConfigurationFlowVariable";
import { useConfigurationFlowErrorCount } from "@pages/configuration/flows/hooks/useConfigurationFlowErrorCount";
import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { MockVariable } from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStep as FlowStepType,
  FlowStepVariant,
  flowStepVariantsWithTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { CommonTypedStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { TriggerFlowStepTypeConfigurationPropertiesConfiguration } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { FlowStepVariantPill } from "../../../../../components/flows/FlowStepVariantPill/FlowStepVariantPill";
import { CanvasType } from "../CanvasType";
import { useFlowCanvas } from "../FlowCanvasContext";
import { ExecutionStatus } from "./ExecutionStatus";
import "./FlowStep.scss";
import { FlowStepKebabMenu } from "./FlowStepKebabMenu/FlowStepKebabMenu";
import { StartingVariablesConfiguration } from "./StartingVariablesConfiguration/StartingVariablesConfiguration";

interface FlowStepProps {
  flowStep: FlowStepType;
  onClick?: () => void;
  handleDelete?: () => void;
  handleDuplicate?: () => void;
  onNameChange?: (name: string) => void;
  isHighlighted?: boolean;
  isSelected?: boolean;
  isDisabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

export const FlowStep = ({
  flowStep,
  onClick,
  handleDelete,
  handleDuplicate,
  // onNameChange,
  isHighlighted = false,
  isSelected = false,
  isDisabled = false,
  style = {},
  className = ""
}: FlowStepProps) => {
  const {
    flowStepTypeConfigByPrimaryIdentifier,
    mockFlowContext,
    configurationFlow
  } = useConfigurationFlowContext();

  const { document, docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const flowStepTypeConfig = useMemo(
    () =>
      flowStepVariantsWithTypeConfiguration.includes(
        flowStep?.variant as FlowStepVariant
      )
        ? flowStepTypeConfigByPrimaryIdentifier?.[
            (flowStep?.properties as CommonTypedStepProperties)
              ?.typePrimaryIdentifier
          ]
        : undefined,
    [flowStep, flowStepTypeConfigByPrimaryIdentifier]
  );

  const { variablesToDisplay } = useMemo(() => {
    const { variables, stepOutputVariables } = getVariablesForStep({
      step: flowStep,
      workspaceDocument: document,
      flowStepTypeConfig,
      flowContext: mockFlowContext
    });

    const variablesToDisplay = getVariablesToDisplay(
      (flowStep?.variant as FlowStepVariant) ?? FlowStepVariant.TRIGGER,
      variables,
      stepOutputVariables ?? []
    );

    if (
      flowStep.variant === FlowStepVariant.TRIGGER &&
      flowStepTypeConfig?.primaryIdentifier &&
      (
        flowStepTypeConfig?.properties
          ?.configuration as TriggerFlowStepTypeConfigurationPropertiesConfiguration
      ).userConfiguredStartingVariables
    ) {
      configurationFlow?.startingVariables?.forEach(variable => {
        if (
          !variablesToDisplay.some(v => v.identifier === variable.identifier)
        ) {
          variablesToDisplay.push({ ...variable, isStartingVariable: true });
        }
      });
    }
    return { variablesToDisplay };
  }, [
    configurationFlow?.startingVariables,
    document,
    flowStep,
    flowStepTypeConfig,
    mockFlowContext
  ]);

  const { canvasType } = useFlowCanvas();

  const d = useDictionary();
  const errorCount = useConfigurationFlowErrorCount(flowStep);

  const warning = useMemo(() => {
    if (flowStepTypeConfig?.properties?.deprecated) {
      return d("ui.configuration.flows.stepType.deprecatedTypeWarning.summary");
    }
  }, [d, flowStepTypeConfig]);

  const ref = useRef<HTMLDivElement>(null);

  if (!flowStep?.variant) {
    return null;
  }

  return (
    <Stack
      className="flow-step-container"
      position="relative"
      alignment="center"
    >
      <WhiteboardBlock
        onClick={onClick}
        className={getClassNames(["flow-step", className])}
        style={style}
        isHighlighted={isHighlighted}
        isSelected={isSelected}
        isDisabled={isDisabled}
      >
        {/* TODO: OA-1110 Add ref onto WhiteboardBlock and declare it there instead */}
        <Stack className="flow-step__content" gap="100" ref={ref}>
          {warning && errorCount === 0 && (
            <WhiteboardStatusCircle tooltip={warning} variant="warning" />
          )}
          {errorCount > 0 && (
            <WhiteboardStatusCircle
              tooltip={d("errors.configurationFlow.tooltip")}
            />
          )}
          <ExecutionStatus stepId={flowStep.id} parentRef={ref} />

          <Inline className="flow-step__top" width="100" spaceBetween>
            <Inline width="100" gap="025" alignment="left">
              <FlowStepVariantPill variant={flowStep.variant} />
              {flowStepTypeConfig && (
                <Tooltip content={flowStepTypeConfig?.name} position="top-left">
                  <Inline gap="025">
                    {(flowStepTypeConfig?.properties?.category?.icon ||
                      flowStepTypeConfig?.properties?.icon) && (
                      <Icon
                        {...(flowStepTypeConfig?.properties?.category?.icon ??
                          flowStepTypeConfig?.properties?.icon)}
                        size="s"
                        color="text-secondary"
                      />
                    )}
                  </Inline>
                </Tooltip>
              )}
            </Inline>
            {canvasType === CanvasType.CONFIGURATION && (
              <FlowStepKebabMenu
                handleDuplicate={
                  flowStep.variant === FlowStepVariant.TRIGGER ||
                  flowStep.variant === FlowStepVariant.CONDITION
                    ? undefined
                    : handleDuplicate
                }
                handleEdit={!isSelected ? onClick : undefined}
                handleDelete={
                  flowStep.variant === FlowStepVariant.TRIGGER
                    ? undefined
                    : handleDelete
                }
                flowStep={flowStep}
              />
            )}
          </Inline>
          <Stack gap="050">
            <Heading
              size={HeadingSize.XS}
              truncate
              maxLines={3}
              weight="medium"
            >
              {flowStep.name}
            </Heading>
          </Stack>
        </Stack>
      </WhiteboardBlock>
      {flowStep.variant === FlowStepVariant.TRIGGER &&
        flowStepTypeConfig?.primaryIdentifier &&
        (
          flowStepTypeConfig.properties
            ?.configuration as TriggerFlowStepTypeConfigurationPropertiesConfiguration
        ).userConfiguredStartingVariables && (
          <>
            <Box
              style={{
                height: "var(--spacing-300)"
              }}
            >
              <Divider direction="vertical" />
            </Box>
            <StartingVariablesConfiguration
              step={flowStep}
              flowStepTypeConfig={flowStepTypeConfig}
              configurationFlow={configurationFlow}
              document={document}
              docChange={docChange}
            />
          </>
        )}
      {variablesToDisplay.length > 0 && (
        <>
          <Box
            className="flow-step__internal-divider"
            style={{ height: "var(--spacing-100)" }}
            alignment="center"
          >
            <Divider direction="vertical" />
          </Box>
          <Inline
            className="flow-step__variables"
            alignment="center"
            gap="050"
            style={{
              maxWidth: "400px"
            }}
            wrap
          >
            {variablesToDisplay.map(variable => {
              return (
                <ConfigurationFlowVariable
                  variable={variable}
                  variant="default"
                  key={`iterator-variable-${variable.id ?? variable.identifier}-${flowStep.id}`}
                />
              );
            })}
          </Inline>
        </>
      )}
    </Stack>
  );
};

const getVariablesToDisplay = (
  variant: FlowStepVariant,
  variables: MockVariable[],
  stepOutputVariables: string[]
): MockVariable[] => {
  const variablesToDisplay: MockVariable[] = [];
  if (variant !== FlowStepVariant.SET_VARIABLES) {
    return variables.filter(variable =>
      stepOutputVariables?.includes(variable.identifier)
    );
  }

  const identifiers = new Set<string>();
  variables.forEach(variable => {
    if (!identifiers.has(variable.identifier)) {
      identifiers.add(variable.identifier);
      variablesToDisplay.push(variable);
    }
  });

  return variablesToDisplay;
};
