import React, { useC<PERSON>back, useMemo } from "react";
import { useOutletContext } from "react-router-dom";

import { Doc, Prop } from "@automerge/automerge-repo";
import {
  ChartType,
  Checkbox,
  Icon,
  Inline,
  MultiSelect,
  MultiSelectValue,
  Select,
  SelectOptionType,
  Stack
} from "@oneteam/onetheme";

import { getByPath } from "@helpers/configurationFormHelper";

import { LabelledDivider } from "@components/shared/LabelledDivider";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { ChartSubTypes, Question } from "@src/types/Question.ts";
import {
  CartesianChartConfig,
  ChartConfig,
  ChartQuestionProperties,
  PieChartConfig,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { chartOnChange } from "./ChartHelper";

export const ChartTypeProperties = ({
  question,
  parentQuestion,
  path,
  d,
  disabled
}: {
  question: Question<ChartQuestionProperties>;
  parentQuestion?: Question<TableQuestionProperties>;
  path: Prop[];
  d: Dictionary;
  disabled?: boolean;
}) => {
  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const questionAccessor = useCallback(
    (accessor: string) => {
      return `${path.join(".")}.${accessor}`;
    },
    [path]
  );

  const chartType = question.properties?.chartConfig?.type;
  const subTypeOptions = useMemo(() => {
    const subTypes =
      chartType && ChartSubTypes[chartType] ? ChartSubTypes[chartType] : [];
    return (
      subTypes?.map(subType => ({
        label: d(
          `ui.configuration.forms.question.type.chart.type.${chartType}.subType.${subType}.label`
        ),
        value: subType,
        description: d(
          `ui.configuration.forms.question.type.chart.type.${chartType}.subType.${subType}.description`
        )
      })) ?? []
    );
  }, [chartType, d]);

  // Get all columns from the parent table question for now
  // Will need to be updated to limit to specific type of columns
  const getAllColumns = useMemo(() => {
    const columns = parentQuestion?.properties?.columns ?? [];
    return columns?.map(column => ({
      label: column.text,
      value: column.id
    }));
  }, [parentQuestion]);

  const renderSelectOrMultiSelect = useCallback(
    function <C extends ChartConfig>({
      field,
      isMultiSelect,
      value,
      options
    }: {
      field: keyof C;
      isMultiSelect: boolean;
      value: C[keyof C];
      options?: SelectOptionType[];
    }) {
      const onChange = chartOnChange<C>({
        field,
        path,
        docChange
      }) as (v: typeof value) => void;

      const commonProps = {
        label: d(`ui.configuration.forms.question.chart.${String(field)}`),
        name: questionAccessor(`properties.chartConfig.${String(field)}`),
        disabled,
        options,
        onlyTriggerChangeWhenBlur: true
      };

      const isArrayValue = Array.isArray(value);

      if (isMultiSelect) {
        return (
          <MultiSelect
            {...commonProps}
            value={value as MultiSelectValue}
            onChange={v => onChange(v as typeof value)}
          />
        );
      }

      return (
        <Select
          {...commonProps}
          value={isArrayValue ? value?.[0] : value}
          onChange={v => {
            if (isArrayValue) {
              onChange((v ? [v] : []) as typeof value);
            } else {
              onChange(v as typeof value);
            }
          }}
        />
      );
    },
    [path, docChange, d, questionAccessor, disabled]
  );

  const renderContentByChartType = useCallback(() => {
    const { chartConfig } = question.properties ?? {};
    if (!chartConfig) {
      return null;
    }

    const { type, swapColumns } = chartConfig;

    if (type === ChartType.LINE || type === ChartType.BAR) {
      return (
        <>
          {renderSelectOrMultiSelect<
            | CartesianChartConfig<ChartType.LINE>
            | CartesianChartConfig<ChartType.BAR>
          >({
            field: "xAxis",
            isMultiSelect: !!swapColumns,
            value: chartConfig.xAxis,
            options: getAllColumns
          })}

          {renderSelectOrMultiSelect<
            | CartesianChartConfig<ChartType.LINE>
            | CartesianChartConfig<ChartType.BAR>
          >({
            field: "series",
            isMultiSelect: !swapColumns,
            value: chartConfig.series,
            options: getAllColumns
          })}
        </>
      );
    } else {
      return (
        <>
          {renderSelectOrMultiSelect<PieChartConfig>({
            field: "groupBy",
            isMultiSelect: !!swapColumns,
            value: chartConfig.groupBy,
            options: getAllColumns
          })}

          {renderSelectOrMultiSelect<PieChartConfig>({
            field: "series",
            isMultiSelect: !swapColumns,
            value: chartConfig.series,
            options: getAllColumns
          })}
        </>
      );
    }
  }, [question, getAllColumns, renderSelectOrMultiSelect]);

  const handleSwapColumnsChange = useCallback(
    (v: boolean) => {
      docChange(d => {
        const q = getByPath<Question<ChartQuestionProperties>>(d, path);
        if (!q?.properties) {
          return;
        }

        q.properties.chartConfig.swapColumns = v;

        if (
          q.properties.chartConfig.type === ChartType.LINE ||
          q.properties.chartConfig.type === ChartType.BAR
        ) {
          const prevXAxis = q.properties.chartConfig.xAxis;
          const prevSeries = q.properties.chartConfig.series;
          q.properties.chartConfig.xAxis = prevSeries ? [...prevSeries] : [];
          q.properties.chartConfig.series = prevXAxis ? [...prevXAxis] : [];
        } else {
          const prevGroupBy = q.properties.chartConfig.groupBy;
          const prevSeries = q.properties.chartConfig.series;
          q.properties.chartConfig.groupBy = prevSeries ? [...prevSeries] : [];
          q.properties.chartConfig.series = prevGroupBy ? [...prevGroupBy] : [];
        }
      });
    },
    [docChange, path]
  );

  return (
    <Stack gap="150" className="chart-type-properties">
      {renderSelectOrMultiSelect<ChartConfig>({
        field: "subType",
        isMultiSelect: false,
        value: question.properties?.chartConfig?.subType,
        options: subTypeOptions
      })}
      {renderContentByChartType()}

      <LabelledDivider
        label={d("ui.configuration.forms.question.chart.advanced.title")}
      />
      <Inline gap="050" alignment="left">
        <Checkbox
          label={d("ui.configuration.forms.question.chart.swapColumns")}
          name={questionAccessor("properties.chartConfig.swapColumns")}
          isChecked={question.properties?.chartConfig?.swapColumns}
          disabled={disabled}
          onChange={handleSwapColumnsChange}
        />
        <Icon name="pivot_table_chart" size="m" color="text-secondary" />
      </Inline>
    </Stack>
  );
};

ChartTypeProperties.displayName = "ChartTypeProperties";
