import { describe, expect, test } from "vitest";

import { Dictionary } from "../../../hooks/useDictionary";
import { Question, QuestionTypes } from "../../../types/Question.ts";
import { SelectQuestionProperties } from "../../../types/QuestionProperties.ts";
import { ZodAnswerSchemaBuilder } from "./ZodAnswerSchemaBuilder";

describe("generateZodSchema for select questions", () => {
  test("should generate schema for required single select question", () => {
    const question: Question<SelectQuestionProperties> = {
      id: "1",
      type: QuestionTypes.SELECT,
      text: "Favorite Color",
      identifier: "identifier",
      properties: {
        required: true,
        options: [
          { label: "Red", value: "red" },
          { label: "Green", value: "green" },
          { label: "Blue", value: "blue" }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generateZodSchema(question);
    const result = schema.safeParse("red");

    expect(result.success).toBe(true);
  });

  test("should generate schema for optional single select question", () => {
    const question: Question<SelectQuestionProperties> = {
      id: "2",
      type: QuestionTypes.SELECT,
      text: "Favorite Color",
      identifier: "identifier",
      properties: {
        required: false,
        options: [
          { label: "Red", value: "red" },
          { label: "Green", value: "green" },
          { label: "Blue", value: "blue" }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generateZodSchema(question);
    const result = schema.safeParse(undefined);

    expect(result.success).toBe(true);
  });

  test("should fail validation for single select question with invalid option", () => {
    const question: Question<SelectQuestionProperties> = {
      id: "3",
      type: QuestionTypes.SELECT,
      text: "Favorite Color",
      identifier: "identifier",
      properties: {
        required: true,
        options: [
          { label: "Red", value: "red" },
          { label: "Green", value: "green" },
          { label: "Blue", value: "blue" }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generateZodSchema(question);
    const result = schema.safeParse("yellow");

    expect(result.success).toBe(false);
  });

  test("should generate schema for required multiSelect question", () => {
    const question: Question<SelectQuestionProperties> = {
      id: "4",
      type: QuestionTypes.SELECT,
      text: "Favorite Colors",
      identifier: "identifier",
      properties: {
        required: true,
        isMultiSelect: true,
        options: [
          { label: "Red", value: "red" },
          { label: "Green", value: "green" },
          { label: "Blue", value: "blue" }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generateZodSchema(question);
    const result = schema.safeParse(["red", "green"]);

    expect(result.success).toBe(true);
  });

  test("should generate schema for optional multiSelect question", () => {
    const question: Question<SelectQuestionProperties> = {
      id: "5",
      type: QuestionTypes.SELECT,
      text: "Favorite Colors",
      identifier: "identifier",
      properties: {
        required: false,
        isMultiSelect: true,
        options: [
          { label: "Red", value: "red" },
          { label: "Green", value: "green" },
          { label: "Blue", value: "blue" }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generateZodSchema(question);
    const result = schema.safeParse(undefined);

    expect(result.success).toBe(true);
  });

  test("should fail validation for multiSelect question with invalid option", () => {
    const question: Question<SelectQuestionProperties> = {
      id: "6",
      type: QuestionTypes.SELECT,
      text: "Favorite Colors",
      identifier: "identifier",
      properties: {
        required: true,
        isMultiSelect: true,
        options: [
          { label: "Red", value: "red" },
          { label: "Green", value: "green" },
          { label: "Blue", value: "blue" }
        ]
      }
    };

    const schema = new ZodAnswerSchemaBuilder(
      [question],
      (value => value) as Dictionary
    ).generateZodSchema(question);
    const result = schema.safeParse(["red", "yellow"]);

    expect(result.success).toBe(false);
  });
});
