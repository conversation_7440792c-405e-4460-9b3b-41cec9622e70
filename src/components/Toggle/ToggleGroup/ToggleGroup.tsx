import React from "react";

import { Box, FermionProps, Inline } from "../../../fermions/index.ts";
import { composeComponentClassNames } from "../../../helpers/index.ts";
import { Label, LabelTextPosition } from "../../Label/index.ts";
import { Toggle } from "../Toggle.tsx";
import "./ToggleGroup.scss";

export type ToggleGroupOptionSingleValue = string | boolean | number;
export type ToggleGroupOptionValue = Array<ToggleGroupOptionSingleValue>;

export interface ToggleGroupOption {
  name?: string;
  value: ToggleGroupOptionSingleValue;
  label?: string;
  disabled?: boolean;
}

export interface ToggleGroupProps {
  id?: string;
  name?: string;
  value?: ToggleGroupOptionValue;
  required?: boolean;
  options: ToggleGroupOption[];
  onChange?: (value?: ToggleGroupOptionValue) => void;
  defaultValue?: ToggleGroupOptionValue;
  description?: string;
  tooltip?: string;
  error?: string;
  disabled?: boolean;
  label?: string;
  direction?: "row" | "column";
  labelTextPosition?: LabelTextPosition;
  width?: FermionProps["width"];
  skipFocus?: boolean;
}

export const ToggleGroup = ({
  id,
  name,
  value,
  options,
  required,
  onChange,
  description,
  tooltip,
  error,
  disabled,
  label = "",
  direction = "row",
  labelTextPosition = LabelTextPosition.TOP,
  width = "fit",
  skipFocus = false
}: ToggleGroupProps) => {
  return (
    <Box className="toggle-group" width={width}>
      <Label
        label={label}
        htmlFor={id}
        textPosition={labelTextPosition}
        disabled={disabled}
        description={description}
        tooltip={tooltip}
        error={error}
        required={required}
      >
        <Inline gap="150">
          <fieldset
            id={id}
            className={composeComponentClassNames("toggle-group__items", {
              direction
            })}
            role="radiogroup"
            name={name}
          >
            {options.map(option => (
              <Toggle
                name={option.name}
                id={String(option.value)}
                key={String(option.value)}
                value={String(option.value)}
                label={option.label}
                disabled={disabled || option.disabled}
                isChecked={value?.includes(option.value)}
                onChange={() => {
                  if (value?.includes(option.value)) {
                    onChange?.(value.filter(v => v !== option.value) ?? []);
                  } else {
                    onChange?.([...(value ?? []), option.value]);
                  }
                }}
                skipFocus={skipFocus}
              />
            ))}
          </fieldset>
        </Inline>
      </Label>
    </Box>
  );
};
