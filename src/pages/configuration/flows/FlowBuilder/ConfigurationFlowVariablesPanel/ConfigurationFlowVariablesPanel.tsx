import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useOutletContext } from "react-router-dom";

import { updateText } from "@automerge/automerge-repo";
import {
  Accordion,
  Box,
  DropdownItem,
  DropdownItemGroup,
  FloatingContentPanel,
  FloatingWithParentPosition,
  Icon,
  Inline,
  KebabMenu,
  OpenCloseIcon,
  PillSelect,
  SearchBar,
  Stack,
  TabGroup,
  Text,
  getClassNames
} from "@oneteam/onetheme";

import { getByPath } from "@helpers/automergeDocumentHelper.ts";
import { getQuestionTypeOptions } from "@helpers/configurationFormHelper";
import { getStepIdPathToStepId } from "@helpers/flows/flowHelpers";
import {
  WorkspaceScopeNamespace,
  WorkspaceScopeType
} from "@helpers/flows/flowVariables/workspaceScopeHelpers";

import { getQuestionTypeDetail } from "@components/flows/VariableField/variableFieldHelpers";
import { QuestionType } from "@components/forms/QuestionType/QuestionType";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useDictionary } from "@src/hooks/useDictionary";
import {
  FlowConfiguration,
  MockVariable
} from "@src/types/FlowConfiguration/FlowConfiguration.ts";
import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  VariableConfiguration,
  VariableTypeDefinition
} from "@src/types/FlowConfiguration/Variables";
import { ExtendedQuestionTypes } from "@src/types/Question";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { useConfigurationFlowContext } from "../../ConfigurationFlowContext";
import { ConfigurationFlowVariable } from "../ConfigurationFlowVariable/ConfigurationFlowVariable";
import {
  findVariableReferences,
  replaceVariableReference
} from "../ConfigurationFlowVariable/ConfigurationFlowVariableHelper";
import "./ConfigurationFlowVariablesPanel.scss";

const commonQuestionTypes = getQuestionTypeOptions();

const isVariableAvailable = ({
  selectedStepId,
  availableFromStepId,
  pathToSelectedStep,
  iteratorParentId,
  isAggregateOutput
}: {
  selectedStepId: FlowStepId | undefined;
  availableFromStepId: FlowStepId | undefined;
  pathToSelectedStep: string[];
  iteratorParentId: FlowStepId | undefined;
  isAggregateOutput: boolean | undefined;
}): boolean => {
  if ((!selectedStepId || !availableFromStepId) && !iteratorParentId) {
    return true;
  }
  if (selectedStepId === availableFromStepId) {
    return true;
  }
  if (availableFromStepId && pathToSelectedStep.includes(availableFromStepId)) {
    return true;
  }

  if (
    iteratorParentId &&
    isAggregateOutput &&
    pathToSelectedStep.includes(iteratorParentId)
  ) {
    return true;
  }

  if (selectedStepId === iteratorParentId) {
    return true;
  }
  return false;
};

enum FlowVariablePanelTab {
  FLOW = "flow",
  WORKSPACE = "workspace"
}

export const ConfigurationFlowVariablesPanel = () => {
  const d = useDictionary();

  return (
    <FloatingContentPanel
      className="configuration-flow-context-panel"
      heading={d("ui.configuration.flows.variables.panel.title")}
    >
      <ConfigurationFlowVariablesPanelContent />
    </FloatingContentPanel>
  );
};

export const ConfigurationFlowVariablesPanelContent = () => {
  const d = useDictionary();
  const {
    configurationFlow,
    selectedStepId,
    setSelectedStepId,
    variableDefinitions,
    variablesByName,
    settings
  } = useConfigurationFlowContext();
  // TODO: later add to useContext
  const [searchValue, setSearchValue] = React.useState<string>("");
  const [typeFilter, setTypeFilter] = React.useState<string | undefined>();
  const [variablePanelTab, setVariablePanelTab] =
    useState<FlowVariablePanelTab>(FlowVariablePanelTab.FLOW);

  const pathToSelectedStep = useMemo(() => {
    if (!selectedStepId || !configurationFlow) {
      return [];
    }
    return getStepIdPathToStepId({ stepId: selectedStepId, configurationFlow });
  }, [configurationFlow, selectedStepId]);

  const filteredVariableNames = useMemo(() => {
    if (!searchValue?.length && !typeFilter) {
      return Object.keys(variablesByName ?? {});
    }
    return Object.entries(variablesByName ?? {})
      .filter(([variableName, value]) => {
        const type = getQuestionTypeDetail(value.__type).type;
        if (typeFilter && type !== typeFilter) {
          return false;
        }
        return (
          !searchValue ||
          variableName.toLowerCase().includes(searchValue.toLowerCase()) ||
          value.__type?.toLowerCase().includes(searchValue.toLowerCase()) ||
          value.__path?.toLowerCase().includes(searchValue.toLowerCase()) ||
          value.__description?.toLowerCase().includes(searchValue.toLowerCase())
        );
      })
      .map(([variableName]) => variableName);
  }, [searchValue, typeFilter, variablesByName]);

  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();

  const renameVariable = useCallback(
    (currentName: string, newName: string) => {
      if (!configurationFlow) {
        return;
      }
      const pathsToReferences = findVariableReferences(
        configurationFlow,
        currentName
      );

      if (!pathsToReferences.length) {
        return;
      }

      docChange(document => {
        for (const path of pathsToReferences) {
          const splitPath = path.split(".");
          const pathToVariable = [
            "flows",
            "entities",
            configurationFlow.id,
            "steps",
            ...splitPath
          ];
          // Update where the variable is defined
          if (pathToVariable[pathToVariable.length - 1] === "identifier") {
            updateText(document, pathToVariable, newName.trim());
            continue;
          }
          const field: string = getByPath(document, pathToVariable);
          const newValue = replaceVariableReference(
            field,
            currentName,
            newName.trim()
          );
          updateText(document, pathToVariable, newValue);
        }
      });
    },
    [configurationFlow, docChange]
  );

  const handleGoToSource = useCallback(
    (sourceStepId?: string, iteratorParentId?: string) => {
      if (iteratorParentId) {
        setSelectedStepId(iteratorParentId);
      } else if (sourceStepId) {
        setSelectedStepId(sourceStepId);
      }
    },
    [setSelectedStepId]
  );

  const isWorkspaceScope = variablePanelTab === FlowVariablePanelTab.WORKSPACE;

  const relevantVariableDefinitions = useMemo(() => {
    if (!variableDefinitions) {
      return [];
    }

    return Object.entries(variableDefinitions).filter(([, itemDefinition]) => {
      const showWorkspaceVariables =
        variablePanelTab === FlowVariablePanelTab.WORKSPACE;
      const isWorkspaceVariable = itemDefinition?.__path?.startsWith(
        WorkspaceScopeNamespace
      );
      if (showWorkspaceVariables !== isWorkspaceVariable) {
        return false;
      }
      return true;
    });
  }, [variableDefinitions, variablePanelTab]);

  const variablesDisplay = useMemo(() => {
    return relevantVariableDefinitions.map(([key, itemDefinition]) => {
      const matchingVariables = filteredVariableNames.filter(name =>
        name.startsWith(key)
      );

      if (!matchingVariables.length) {
        return <></>;
      }

      const sourceStepId: FlowStepId | undefined =
        itemDefinition.__sourceStepId;
      const availableFromStepId: FlowStepId | undefined =
        itemDefinition.__availableFromStepId;
      const iteratorParentId: FlowStepId | undefined =
        itemDefinition.__iteratorParentId;
      const isAggregateOutput: boolean | undefined =
        itemDefinition.__isAggregateOutput;
      return (
        <VariableItem
          level={0}
          itemKey={key}
          key={key}
          definition={itemDefinition}
          path={key}
          debugMode={settings?.debugMode}
          handleGoToSource={
            sourceStepId
              ? () => handleGoToSource(sourceStepId, iteratorParentId)
              : undefined
          }
          handleRename={
            isWorkspaceScope
              ? undefined
              : newName => renameVariable(key, newName)
          }
          isAvailable={isVariableAvailable({
            selectedStepId,
            availableFromStepId,
            pathToSelectedStep,
            iteratorParentId,
            isAggregateOutput
          })}
          filteredVariableNames={matchingVariables}
          filter={
            searchValue || typeFilter
              ? {
                  searchValue,
                  typeFilter
                }
              : undefined
          }
          isWorkspaceScope={isWorkspaceScope}
        />
      );
    });
  }, [
    filteredVariableNames,
    pathToSelectedStep,
    searchValue,
    selectedStepId,
    typeFilter,
    relevantVariableDefinitions,
    renameVariable,
    settings?.debugMode,
    handleGoToSource,
    isWorkspaceScope
  ]);

  return (
    <Stack className="configuration-flow-context-panel__content" gap="100">
      <TabGroup
        options={[
          {
            value: FlowVariablePanelTab.FLOW,
            label: d("ui.configuration.flows.variables.panel.variablesTab")
          },
          {
            value: FlowVariablePanelTab.WORKSPACE,
            label: d(
              "ui.configuration.flows.variables.panel.workspaceVariablesTab"
            )
          }
        ]}
        value={variablePanelTab}
        handleChange={(tab: string) => {
          setVariablePanelTab(tab as FlowVariablePanelTab);
        }}
      />
      <Box
        gap="050"
        alignment="left"
        style={{
          flexDirection: typeFilter ? "column" : "row"
        }}
      >
        <Box width="fill">
          <SearchBar
            autoFocus
            placeholder={d("ui.common.search")}
            value={searchValue}
            handleChange={setSearchValue}
          />
        </Box>
        <PillSelect
          label={d("ui.configuration.flows.variables.type.label")}
          value={typeFilter}
          handleChange={value => setTypeFilter(value as string)}
          options={[
            ...commonQuestionTypes.map(type => {
              return {
                value: type,
                label: ""
              };
            }),
            ...Object.values(ExtendedQuestionTypes).map((type: string) => {
              return {
                value: type,
                label: ""
              };
            })
          ]}
          renderOptionLeftElement={({ option }) => (
            <QuestionType type={option?.value as string} size="regular" />
          )}
          leftElement={
            typeFilter ? (
              <QuestionType type={typeFilter} size="regular" />
            ) : undefined
          }
        />
      </Box>

      <Box height="100" overflow="auto">
        <Stack>{variablesDisplay}</Stack>
      </Box>
      <Inline
        alignment="left"
        gap="050"
        padding="050"
        style={{
          borderRadius: "var(--border-radius-rounded-more, 12px)",
          backgroundColor:
            "var(--components-pill-prominent-neutral-color-background)"
        }}
      >
        <Icon
          name="lightbulb"
          fillStyle="filled"
          color="text-tertiary"
          size="s"
        />
        <Text size="s" color="text-secondary">
          {d("ui.configuration.flows.variables.panel.copyVariableTip")}
        </Text>
      </Inline>
    </Stack>
  );
};

export type VariableForPanel = {
  level: number;
  handleGoToSource?: () => void;
  handleRename?: (newName: string) => void;
  itemKey: string;
  definition: VariableTypeDefinition;
  path: string;
  isAvailable?: boolean;
  filteredVariableNames: string[];
  filter?: {
    searchValue?: string;
    typeFilter?: string;
  };
  debugMode?: boolean;
  isWorkspaceScope?: boolean;
};

const VariableItem = ({
  level,
  itemKey,
  definition,
  path,
  handleRename,
  handleGoToSource,
  isAvailable = true,
  filteredVariableNames,
  filter,
  debugMode,
  isWorkspaceScope
}: VariableForPanel) => {
  const nextDefinitions: VariableTypeDefinition =
    typeof definition === "object"
      ? Object.fromEntries(
          Object.keys(definition)
            .filter(k => !k.startsWith("__") && definition[k])
            .map(k => [k, definition[k] as VariableTypeDefinition]) ?? []
        )
      : {};

  const showKebabMenu = level === 0 && !isWorkspaceScope;

  if (Object.keys(nextDefinitions).length > 0) {
    return (
      <ExpandableVariablePanel
        level={level}
        itemKey={itemKey}
        definition={definition}
        path={path}
        handleGoToSource={handleGoToSource}
        isAvailable={isAvailable}
        filteredVariableNames={filteredVariableNames}
        filter={filter}
        debugMode={debugMode}
        isWorkspaceScope={isWorkspaceScope}
      />
    );
  }

  const variable: MockVariable = {
    identifier: `${definition?.__identifier ?? itemKey}`,
    type: definition?.__type ?? "unknown",
    description: definition?.__description ?? "",
    sourceStepVariant: definition?.__sourceStepVariant,
    sourceStepId: definition?.__sourceStepId ?? "unknown",
    availableFromStepId: definition?.__availableFromStepId ?? "unknown",
    isStartingVariable: (definition?.__isStartingVariable ?? false) as boolean,
    isEndingVariable: (definition?.__isEndingVariable ?? false) as boolean,
    iteratorParentId: definition?.__iteratorParentId,
    isAggregateOutput: definition?.__isAggregateOutput,
    configuration: definition?.__configuration
  };

  return (
    <ConfigurationFlowVariable
      variant="line-item"
      variable={variable}
      handleGoToSource={handleGoToSource}
      handleRename={handleRename}
      copyText={debugMode ? `{{${definition.__path}}}` : `{{${path}}}`}
      isAvailable={isAvailable}
      kebabMenu={
        showKebabMenu ? <VariableKebabMenu variable={variable} /> : undefined
      }
    />
  );
};

const ExpandableVariablePanel = ({
  level,
  itemKey,
  path,
  definition,
  handleGoToSource,
  isAvailable = true,
  filteredVariableNames,
  filter,
  debugMode,
  isWorkspaceScope
}: {
  level: number;
  itemKey: string;
  path: string;
  definition: VariableTypeDefinition;
  handleGoToSource?: () => void;
  isAvailable?: boolean;
  filteredVariableNames: string[];
  filter?: {
    searchValue?: string;
    typeFilter?: string;
  };
  debugMode?: boolean;
  isWorkspaceScope?: boolean;
}) => {
  const { variablesByPath } = useConfigurationFlowContext();
  const [isOpen, setIsOpen] = React.useState(false);

  const isMatch: boolean | undefined = useMemo(() => {
    if (!filter?.searchValue && !filter?.typeFilter) {
      return undefined;
    }

    const type = getQuestionTypeDetail(definition?.__type).type;
    return (
      (filter?.typeFilter && type === filter.typeFilter) ||
      (!!filter?.searchValue &&
        filter?.searchValue?.length > 0 &&
        itemKey.toLowerCase().includes(filter?.searchValue?.toLowerCase()))
    );
  }, [definition.__type, filter?.searchValue, filter?.typeFilter, itemKey]);

  useEffect(() => {
    if (isMatch === undefined) {
      return;
    }

    setIsOpen(!isMatch);
  }, [isMatch]);

  const canBeCopied = !(
    level === 0 && definition?.__type === WorkspaceScopeType.WORKSPACE_VARIABLES
  );

  const showKebabMenu = level === 0 && !isWorkspaceScope;

  const pathToCopy = useMemo(() => {
    if (debugMode || path === definition.__path) {
      return `{{${definition.__path}}}`;
    }

    const variableName =
      definition.__path && variablesByPath
        ? variablesByPath?.[definition.__path].__name
        : path;
    return `{{${variableName}}}`;
  }, [debugMode, definition.__path, path, variablesByPath]);

  return (
    <Accordion
      isOpen={isOpen}
      onOpenChange={() => setIsOpen(!isOpen)}
      className="flow-variable-expandable"
      trigger={({ onClick }) => {
        const variable = {
          identifier: `${definition?.__identifier ?? itemKey}`,
          type: definition?.__type ?? "unknown",
          description: definition?.__description ?? "",
          isStartingVariable: (definition?.__isStartingVariable ??
            false) as boolean,
          isEndingVariable: (definition?.__isEndingVariable ??
            false) as boolean,
          configuration: definition?.__configuration
        };

        return (
          <Inline gap="050" alignment="left" width="100">
            <Box
              onClick={onClick}
              style={{
                width: "var(--spacing-100, 8px)",
                marginLeft: "calc(var(--spacing-050, 2px) * -1)"
              }}
            >
              <OpenCloseIcon isOpen={isOpen} />
            </Box>
            <ConfigurationFlowVariable
              variant="line-item"
              variable={variable}
              handleGoToSource={handleGoToSource}
              handleRename={undefined}
              isAvailable={isAvailable}
              copyText={pathToCopy}
              onClick={onClick}
              kebabMenu={
                showKebabMenu ? (
                  <VariableKebabMenu variable={variable} />
                ) : undefined
              }
              canBeCopied={canBeCopied}
            />
          </Inline>
        );
      }}
    >
      <Box
        style={{
          paddingLeft: "var(--spacing-200, 4px)"
        }}
      >
        {Object.entries(definition).map(([key, itemDefinition]) => {
          if (!itemDefinition || typeof itemDefinition === "string") {
            return <></>;
          }
          const nextDefinitions = Object.fromEntries(
            Object.entries(itemDefinition).filter(
              ([k, v]) => !k.startsWith("__") && v
            )
          );

          const newPath = `${path}.${key}`;
          const matchingVariables = filteredVariableNames.filter(name =>
            name.startsWith(newPath)
          );
          if (!matchingVariables.length) {
            return <></>;
          }
          if (Object.keys(nextDefinitions).length > 0) {
            return (
              <ExpandableVariablePanel
                level={level + 1}
                itemKey={key}
                path={newPath}
                definition={itemDefinition as VariableTypeDefinition}
                isAvailable={isAvailable}
                filteredVariableNames={matchingVariables}
                key={newPath}
                handleGoToSource={handleGoToSource}
                filter={filter}
                debugMode={debugMode}
                isWorkspaceScope={isWorkspaceScope}
              />
            );
          }

          return (
            <VariableItem
              level={level + 1}
              path={newPath}
              itemKey={key}
              key={newPath}
              debugMode={debugMode}
              definition={itemDefinition as VariableTypeDefinition}
              isAvailable={isAvailable}
              filteredVariableNames={matchingVariables}
              handleGoToSource={handleGoToSource}
              filter={filter}
              isWorkspaceScope={isWorkspaceScope}
            />
          );
        })}
      </Box>
    </Accordion>
  );
};

const VariableKebabMenu = ({ variable }: { variable: MockVariable }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { configurationFlow } = useConfigurationFlowContext();
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();

  const getEndingVariableFromDoc = useCallback(
    (
      d: WorkspaceDocument
    ): {
      endingVariables?: VariableConfiguration[];
      existingEndingVariable?: VariableConfiguration;
    } => {
      if (!configurationFlow) {
        return {};
      }
      const flowConfiguration = getByPath<FlowConfiguration>(d, [
        "flows",
        "entities",
        configurationFlow.id
      ]);
      if (!flowConfiguration) {
        return {};
      }
      flowConfiguration.endingVariables ??= [];
      const existingEndingVariable = flowConfiguration.endingVariables.find(
        item => item.identifier === variable.identifier
      );
      return {
        endingVariables: flowConfiguration.endingVariables,
        existingEndingVariable
      };
    },
    [configurationFlow, variable.identifier]
  );

  const handleMakeEndingVariable = useCallback(() => {
    docChange(d => {
      const { endingVariables, existingEndingVariable } =
        getEndingVariableFromDoc(d);
      if (!endingVariables) {
        return;
      }
      if (existingEndingVariable) {
        return;
      } else {
        endingVariables.push({
          type: variable.type,
          identifier: variable.identifier,
          ...(variable.properties ? { properties: variable.properties } : {})
        });
      }
    });
  }, [docChange, getEndingVariableFromDoc, variable]);

  const handleRemoveAsEndingVariable = useCallback(() => {
    docChange(d => {
      const { endingVariables, existingEndingVariable } =
        getEndingVariableFromDoc(d);
      if (!endingVariables) {
        return;
      }
      if (existingEndingVariable) {
        endingVariables.splice(
          endingVariables.indexOf(existingEndingVariable),
          1
        );
      }
    });
  }, [docChange, getEndingVariableFromDoc]);

  const callWithClose = (callback: () => void) => () => {
    setIsOpen(false);
    callback();
  };

  return (
    <KebabMenu
      className={getClassNames([
        "nodrag",
        "nopan",
        "flow-variable-kebab-menu",
        `flow-variable__kebab--${isOpen ? "open" : "closed"}`
      ])}
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      position={FloatingWithParentPosition.BOTTOM_RIGHT}
    >
      <DropdownItemGroup>
        {!variable.isEndingVariable && handleMakeEndingVariable && (
          <DropdownItem
            leftElement={<Icon name="chip_extraction" />}
            onClick={callWithClose(handleMakeEndingVariable)}
            description={"For flows in flows"}
          >
            Make ending variable
          </DropdownItem>
        )}
        {variable.isEndingVariable && handleRemoveAsEndingVariable && (
          <DropdownItem
            leftElement={<Icon name="chip_extraction" />}
            onClick={callWithClose(handleRemoveAsEndingVariable)}
            description={"For flows in flows"}
          >
            Remove as ending variable
          </DropdownItem>
        )}
      </DropdownItemGroup>
    </KebabMenu>
  );
};
