package services.oneteam.ai.shared.domains

import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.*
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.otTypeDescrim
import java.math.BigDecimal


//the reason this is not an enum is the CUSTOM type. enums cannot have a 'typeDef' construct.
//we need both constants (eg text and json) and a dynamic case (custom) that carries its own string.
@Serializable(with = DataTypeSerializer::class)
sealed class VariableDataType {
    abstract val typeDef: String

    @SerialName("text")
    @Serializable
    object TEXT : VariableDataType() {
        override val typeDef = "text"
    }

    @SerialName("select")
    @Serializable
    object SELECT : VariableDataType() {
        override val typeDef = "select"
    }

    @SerialName("date")
    @Serializable
    object DATE : VariableDataType() {
        override val typeDef = "date"
    }

    @SerialName("number")
    @Serializable
    object NUMBER : VariableDataType() {
        override val typeDef = "number"
    }

    @SerialName("boolean")
    @Serializable
    object BOOLEAN : VariableDataType() {
        override val typeDef = "boolean"
    }

    @SerialName("list")
    @Serializable
    object LIST : VariableDataType() {
        override val typeDef = "list"
    }

    @SerialName("table")
    @Serializable
    object TABLE : VariableDataType() {
        override val typeDef = "table"
    }

    @SerialName("json")
    @Serializable
    object JSON : VariableDataType() {
        override val typeDef = "json"
    }

    @SerialName("files")
    @Serializable
    object FILES : VariableDataType() {
        override val typeDef = "files"
    }

    @SerialName("section")
    @Serializable
    object SECTION : VariableDataType() {
        override val typeDef = "section"
    }

    @SerialName("multiSelect")
    @Serializable
    object MULTISELECT : VariableDataType() {
        override val typeDef = "multiSelect"
    }

    @SerialName("variable")
    @Serializable
    object VARIABLE : VariableDataType() {
        override val typeDef = "variable"
    }

    @SerialName("schema")
    @Serializable
    object SCHEMA : VariableDataType() {
        override val typeDef = "schema"
    }

    /** dynamic case carrying its own string */
    @SerialName("custom")
    @Serializable
    data class CUSTOM(override val typeDef: String) : VariableDataType()

    override fun toString(): String {
        return this.typeDef
    }

    companion object Companion {
        fun fromString(type: String): VariableDataType = when (type.lowercase()) {
            TEXT.typeDef -> TEXT
            SELECT.typeDef -> SELECT
            DATE.typeDef -> DATE
            NUMBER.typeDef -> NUMBER
            BOOLEAN.typeDef -> BOOLEAN
            LIST.typeDef -> LIST
            TABLE.typeDef -> TABLE
            JSON.typeDef -> JSON
            FILES.typeDef -> FILES
            SECTION.typeDef -> SECTION
            else -> CUSTOM(type)
        }
    }
}


object DataTypeSerializer : KSerializer<VariableDataType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor(otTypeDescrim, PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: VariableDataType) {
        encoder.encodeString(value.typeDef)
    }

    override fun deserialize(decoder: Decoder) = VariableDataType.fromString(decoder.decodeString())
}

object TypeToJsonElementConverter {

    fun convert(
        type: VariableDataType, value: Any?, defaultStrategy: DefaultStrategy = UseDefaultByType
    ): JsonElement {

        if (value == null || value is JsonNull) {
            return defaultStrategy.toDefault(type)
        }

        val stringValue = value.toString().trim('"')

        return when (type) {
            is VariableDataType.TEXT -> JsonPrimitive(stringValue)
            is VariableDataType.SELECT -> JsonPrimitive(stringValue)
            is VariableDataType.DATE -> JsonPrimitive(stringValue)
            is VariableDataType.NUMBER -> JsonPrimitive(stringValue.toBigDecimal())
            is VariableDataType.BOOLEAN -> JsonPrimitive(stringValue.toBoolean())
            is VariableDataType.LIST -> {
                JsonArray((value as List<*>).map { toJsonElement(it!!) }.toList())
            }
            is VariableDataType.VARIABLE -> toJsonElement(value)
            is VariableDataType.TABLE, VariableDataType.JSON -> toJsonElement(value)
            is VariableDataType.MULTISELECT -> toJsonElement(value)
            is VariableDataType.SECTION -> toJsonElement(value)
            is VariableDataType.FILES -> toJsonElement(value)
            is VariableDataType.SCHEMA -> toJsonElement(value)
            is VariableDataType.CUSTOM -> throw IllegalArgumentException("Unsupported custom type")
        }
    }

    fun toJsonElement(value: Any?): JsonElement {
        return when (value) {
            null -> JsonNull
            is JsonPrimitive, JsonObject, JsonArray -> value as JsonElement
            is String -> JsonPrimitive(value)
            is Int -> JsonPrimitive(BigDecimal(value).convertToNiceBigDecimal())
            is Long -> JsonPrimitive(BigDecimal(value).convertToNiceBigDecimal())
            is Double -> JsonPrimitive(BigDecimal(value).convertToNiceBigDecimal())
            is BigDecimal -> JsonPrimitive(value.convertToNiceBigDecimal())
            is Boolean -> JsonPrimitive(value)
            is List<*> -> JsonArray(value.map { toJsonElement(it) }.toList())
            is Map<*, *> -> JsonObject(value.map { (k, v) -> k.toString() to toJsonElement(v) }.toMap())
            else -> throw IllegalArgumentException("Unsupported type ${value.javaClass}")
        }
    }

    fun defaultByType(type: VariableDataType): JsonElement {
        return UseDefaultByType.toDefault(type)
    }
}

fun interface DefaultStrategy {
    fun toDefault(type: VariableDataType): JsonElement
}

object DefaultToNull : DefaultStrategy {
    override fun toDefault(type: VariableDataType): JsonElement {
        return JsonNull
    }
}

object UseDefaultByType : DefaultStrategy {
    override fun toDefault(type: VariableDataType): JsonElement {
        return when (type) {
            VariableDataType.TEXT -> JsonPrimitive("")
            VariableDataType.DATE -> JsonNull
            VariableDataType.NUMBER -> JsonPrimitive(0)
            VariableDataType.BOOLEAN -> JsonPrimitive(false)
            VariableDataType.JSON -> JsonNull
            VariableDataType.TABLE -> JsonNull
            VariableDataType.LIST -> JsonNull
            else -> JsonNull
        }
    }
}