import React, { useEffect, useMemo, useState } from "react";

import {
  Box,
  Chart,
  ChartConfig,
  ChartType,
  Inline,
  PillSelect,
  SelectValue,
  Stack,
  Text
} from "@oneteam/onetheme";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import {
  ChartQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";

import "./QuestionChartAnswer.scss";
import {
  generateMockData,
  mapConfigIdsToNames
} from "./questionChartAnswerHelper";

const mockDataRowNumber = 3;

export const QuestionChartAnswer = ({
  chartAnswerAccessor = "",
  question,
  parentQuestion
}: {
  chartAnswerAccessor?: string;
  question: Question<ChartQuestionProperties>;
  parentQuestion?: Question<TableQuestionProperties>;
}) => {
  const [pieFilter, setPieFilter] = useState<SelectValue | undefined>("");
  const d = useDictionary();

  const formattedData = useMemo(() => {
    if (!chartAnswerAccessor) {
      return generateMockData(
        parentQuestion?.properties?.columns || [],
        mockDataRowNumber
      );
    } else {
      // Get real data from table answer
      return [];
    }
  }, [parentQuestion?.properties?.columns, chartAnswerAccessor]);

  const formattedConfig = useMemo(() => {
    const rawConfig = question?.properties?.chartConfig;
    if (!rawConfig) {
      return undefined;
    }
    const chartConfigWithNames = mapConfigIdsToNames(
      rawConfig,
      parentQuestion?.properties?.columns
    );

    const chartConfig = { ...chartConfigWithNames } as ChartConfig;

    if (formattedData && pieFilter && chartConfig.type === ChartType.PIE) {
      if (chartConfig.swapColumns) {
        chartConfig.rowIndex = Number(pieFilter);
        chartConfig.series =
          formattedData[Number(pieFilter)][chartConfig.series[0]];
      } else {
        chartConfig.rowIndex = undefined;
        chartConfig.series = pieFilter as string;
      }
    }

    return chartConfig;
  }, [
    question?.properties?.chartConfig,
    pieFilter,
    formattedData,
    parentQuestion?.properties?.columns
  ]);

  const pieChartFilterOptions = useMemo(() => {
    if (question?.properties?.chartConfig.type !== ChartType.PIE) {
      return [];
    }

    if (question?.properties?.chartConfig.swapColumns) {
      const seriesByKey = question?.properties?.chartConfig.series?.[0];
      if (!seriesByKey) {
        return [];
      }
      return formattedData.map((row, index) => ({
        label: row[seriesByKey],
        value: String(index)
      }));
    } else {
      return (question?.properties?.chartConfig.series || []).map(
        seriesKey => ({
          label: seriesKey,
          value: seriesKey
        })
      );
    }
  }, [question?.properties?.chartConfig, formattedData]);

  useEffect(() => {
    if (pieChartFilterOptions.length > 0) {
      setPieFilter(pieChartFilterOptions[0].value);
    } else {
      setPieFilter("");
    }
  }, [pieChartFilterOptions]);

  const handlePieChartFilterChange = (value: SelectValue | undefined) => {
    setPieFilter(value);
  };

  // Check if chart has enough configurations to render
  const shouldRenderChart = useMemo(() => {
    const config = question?.properties?.chartConfig;
    if (!config?.type) {
      return false;
    }

    if (config.type === ChartType.LINE || config.type === ChartType.BAR) {
      return (
        config.xAxis &&
        config.xAxis.length > 0 &&
        config.series &&
        config.series.length > 0
      );
    } else if (config.type === ChartType.PIE) {
      return (
        config.groupBy &&
        config.groupBy.length > 0 &&
        config.series &&
        config.series.length > 0
      );
    }
    return false;
  }, [question?.properties?.chartConfig]);

  // Return empty fragment if chart configuration is incomplete
  if (!shouldRenderChart || !formattedConfig) {
    return (
      <Stack style={{ overflow: "hidden" }} gap="100">
        <Text size="m" color="text-tertiary">
          {question.text}
        </Text>
        {d("ui.configuration.forms.question.chart.unablePreviewMessage")}
      </Stack>
    );
  }
  return (
    <Stack gap="100">
      <Inline width="100" spaceBetween>
        <Text size="m" color="text-tertiary">
          {question.text}
        </Text>
        {formattedConfig?.type === ChartType.PIE && (
          <PillSelect
            label="Filter"
            value={pieFilter}
            options={pieChartFilterOptions}
            handleChange={value => handlePieChartFilterChange(value)}
          />
        )}
      </Inline>
      <Box className="chart-preview-container">
        <Chart
          data={formattedData}
          config={formattedConfig}
          width="100"
          height="100"
        />
      </Box>
    </Stack>
  );
};
