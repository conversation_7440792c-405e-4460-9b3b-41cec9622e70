import { useCallback } from "react";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { useFlowExecutionContext } from "../FlowExecutionContext";
import { getNestedFlowExecution } from "../helpers/ navigationHelper";

export const useFlowExecutionNavigationHelper = () => {
  const {
    pathToCurrentExecution,
    setPathToCurrentExecution,
    setExecutionFlowForModal,
    executionFlow,
    rootExecutionFlow
  } = useFlowExecutionContext();
  const { setSelectedStepId } = useConfigurationFlowContext();
  const navigateToPreviousExecution = useCallback(() => {
    const truncatedPath = pathToCurrentExecution?.slice(0, -4);
    setPathToCurrentExecution?.(truncatedPath);
    setSelectedStepId(undefined);
    setExecutionFlowForModal?.(undefined);
  }, [
    pathToCurrentExecution,
    setPathToCurrentExecution,
    setSelectedStepId,
    setExecutionFlowForModal
  ]);

  const navigateToSubFlow = useCallback(
    (stepId: FlowStepId, pathToExecution: string[] = []) => {
      const newPathToExecution = [
        ...pathToExecution,
        "steps",
        stepId,
        "subFlows",
        stepId
      ];
      setPathToCurrentExecution?.(newPathToExecution);
      setSelectedStepId(undefined);

      if (!executionFlow) {
        return;
      }
      const nestedExecution = getNestedFlowExecution(
        executionFlow,
        newPathToExecution
      );
      setExecutionFlowForModal?.(nestedExecution);
    },
    [
      setPathToCurrentExecution,
      setSelectedStepId,
      executionFlow,
      setExecutionFlowForModal
    ]
  );

  const navigateToSubFlowViaPath = useCallback(
    (path: string[]) => {
      if (
        !rootExecutionFlow ||
        !setPathToCurrentExecution ||
        !setExecutionFlowForModal
      ) {
        return;
      }

      setPathToCurrentExecution(path);
      const nestedExecution = getNestedFlowExecution(rootExecutionFlow, path);
      setExecutionFlowForModal(nestedExecution);
    },
    [rootExecutionFlow, setExecutionFlowForModal, setPathToCurrentExecution]
  );

  return {
    navigateToPreviousExecution,
    navigateToSubFlow,
    navigateToSubFlowViaPath
  };
};
