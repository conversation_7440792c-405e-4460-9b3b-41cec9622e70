import express, { Request, Response } from "express";
import path from "path";

import AppConfig from "./config/AppConfig";

// Load environment variables

const app = express();
const PORT = AppConfig.PORT.value;
const otaiPath = AppConfig.OTAI_STORYBOOK_PATH.value;
const oneteamPath = AppConfig.ONETEAM_STORYBOOK_PATH.value;

app.get("/", (req: Request, res: Response) => {
  res.sendFile(path.join(__dirname, "../public/index.html"));
});

// Serve static files for app1 and app2 from their respective paths
if (otaiPath) {
  app.use("/otai", express.static(otaiPath));
}
if (oneteamPath) {
  app.use("/oneteam", express.static(oneteamPath));
}

// Handle 404 errors
app.use((req: Request, res: Response) => {
  res.status(404).send("Page not found");
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running at http://localhost:${PORT}`);
});
