import React from "react";

import { ChartType, Form } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react";
import { z } from "zod";

import { QuestionTypes } from "@src/types/Question.ts";

import { QuestionTypeSelect } from "./QuestionTypeSelect.tsx";

const meta: Meta<typeof QuestionTypeSelect> = {
  component: QuestionTypeSelect,
  title: "configuration/QuestionTypeSelect"
};

export default meta;

type Story = StoryObj<typeof QuestionTypeSelect>;

const defaultArgs: Story["args"] = {
  disabled: false
};

const defaultRenderer = (args: Story["args"]) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [type, setType] = React.useState(QuestionTypes.TEXT);
  return (
    <Form
      schema={z.object({ type: z.string() })}
      d={() => ""}
      handleSubmit={() => console.log("Submit")}
      defaultValues={{ type: QuestionTypes.TEXT }}
      hideFormButtons
    >
      <QuestionTypeSelect
        {...args}
        type={type}
        path={["type"]}
        handleChange={(value: QuestionTypes | ChartType) =>
          setType(value as QuestionTypes)
        }
      />
    </Form>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: defaultRenderer
};
