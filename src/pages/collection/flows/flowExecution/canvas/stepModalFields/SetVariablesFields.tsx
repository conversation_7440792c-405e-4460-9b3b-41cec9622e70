import React from "react";

import { Stack } from "@oneteam/onetheme";

import {
  ExecutionStep,
  FlowExecutionDocument
} from "@src/types/FlowConfiguration/FlowExecution";
import { SetVariablesStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

import { getResolvedVariableValue } from "../../helpers/flowExecutionVariableHelper";
import { FlowExecutionVariable } from "../components/FlowExecutionVariable";

export const SetVariablesFields = ({
  executionFlow,
  executionStep
}: {
  executionStep?: ExecutionStep;
  executionFlow?: FlowExecutionDocument;
}) => {
  if (!executionStep || !executionFlow) {
    return <></>;
  }
  return (
    <Stack gap="100">
      {(
        executionStep as ExecutionStep<SetVariablesStepProperties>
      ).properties.variables.map((variable, index) => {
        const resolvedVariable = getResolvedVariableValue(
          variable.identifier,
          executionFlow
        );

        return (
          <FlowExecutionVariable
            key={variable.identifier + index}
            index={index}
            configurationVariable={variable}
            outputValue={resolvedVariable}
            variableFieldValue={
              variable.value !== undefined
                ? (variable.value as string)
                : undefined
            }
          />
        );
      })}
    </Stack>
  );
};
