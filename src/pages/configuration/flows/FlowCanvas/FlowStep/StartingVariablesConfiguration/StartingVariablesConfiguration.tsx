import React, { useCallback, useEffect, useMemo } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Box,
  DropdownItem,
  DropdownItemGroup,
  DropdownItemNested,
  Heading,
  Icon,
  Inline,
  Stack,
  Text,
  WhiteboardBlock
} from "@oneteam/onetheme";

import {
  getLocalStepContext,
  populateRealValuesUsingMockFlowContext
} from "@helpers/flows/flowHelpers";
import { getVariableMappingsFromStepTypeConfiguration } from "@helpers/flows/flowVariables/flowVariableHelpers";

import { ConfigurationFormAddLine } from "@components/forms/configuration/ConfigurationFormAddLine/ConfigurationFormAddLine";
import { ConfigurationQuestionBlock } from "@components/forms/configuration/question/ConfigurationQuestionBlock/ConfigurationQuestionBlock";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { commonIcons } from "@src/constants/iconConstants";
import { createUniqueIdentifier } from "@src/hooks/uniqueIdentifier/question";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import {
  DisplayQuestionType,
  useQuestionTypeDisplay
} from "@src/hooks/useQuestionTypeDisplay";
import {
  FlowStep,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { triggeredByAnotherFlowStepType } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { FlowStepTypeConfiguration } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import {
  Variable,
  VariableConfiguration
} from "@src/types/FlowConfiguration/Variables";
import { ExtendedQuestionTypes, QuestionTypes } from "@src/types/Question";
import { WorkspaceDocument } from "@src/types/documentTypes";

type CreateStartingVariableIdentifier = (
  text: string,
  currentIdentifier?: string
) => string;

export const StartingVariablesConfiguration = ({
  step,
  configurationFlow,
  flowStepTypeConfig,
  document,
  docChange
}: {
  step?: FlowStep;
  configurationFlow?: WorkspaceDocument["flows"]["entities"][string];
  flowStepTypeConfig?: FlowStepTypeConfiguration;
  document: Doc<WorkspaceDocument>;
  docChange: DocChange;
}) => {
  const d = useDictionary();
  const { mockFlowContext, refreshFlowCanvas } = useConfigurationFlowContext();
  const { displayQuestionType } = useQuestionTypeDisplay();

  const [isRenamingIndex, setIsRenamingIndex] = React.useState<number | null>(
    null
  );

  const isMounted = React.useRef(false);

  useEffect(() => {
    isMounted.current = true;

    if (isMounted.current && refreshFlowCanvas) {
      refreshFlowCanvas();
    }

    return () => {
      isMounted.current = false;
    };
  }, [refreshFlowCanvas]);

  const createStartingVariableIdentifier: CreateStartingVariableIdentifier =
    useCallback(
      (text, currentIdentifier) =>
        createUniqueIdentifier({
          text,
          existingIdentifiers:
            configurationFlow?.startingVariables
              ?.filter(variable => variable.identifier !== currentIdentifier)
              ?.map(variable => variable.identifier) ?? []
        }),
      [configurationFlow?.startingVariables]
    );

  const variableMappings = useMemo(() => {
    if (!flowStepTypeConfig || !step) {
      return [];
    }

    const stepContext = getLocalStepContext({
      step,
      flowContext: mockFlowContext
    });

    const variableMappingIdentifiers =
      getVariableMappingsFromStepTypeConfiguration({
        variant: FlowStepVariant.TRIGGER,
        flowStepTypeConfig
      }).map((variableMapping: Variable) => {
        const result = populateRealValuesUsingMockFlowContext(
          variableMapping,
          stepContext
        );
        return result.identifier;
      });
    return variableMappingIdentifiers;
  }, [flowStepTypeConfig, mockFlowContext, step]);

  return (
    <WhiteboardBlock className="iterator-start-step">
      <Stack gap="050">
        <Inline gap="050" alignment="left">
          <Icon size="m" name="play_circle" color="color" />
          <Heading size="xs">
            {d("ui.configuration.flows.variables.startingVariable.title")}(s)
          </Heading>
        </Inline>
        {configurationFlow?.startingVariables?.map((variable, index) => {
          const isVariableMapping = variableMappings.includes(
            variable.identifier
          );

          return (
            <ConfigurationQuestionBlock
              key={variable.identifier}
              isRenaming={isRenamingIndex === index}
              // TODO: cleanup multi-level so it isn't all tied to a form
              hideMultiLevel
              question={{
                id: variable.identifier,
                identifier: variable.identifier,
                type: variable.type as `${QuestionTypes}`,
                text: variable.text ?? variable.identifier
              }}
              // TODO: open modal
              onClick={() => setIsRenamingIndex(index)}
              path={[]}
              mode={isVariableMapping ? "view" : "edit"}
              isDisabled={isVariableMapping}
              handleDelete={() => {
                if (!configurationFlow?.id) {
                  return;
                }
                docChange(doc => {
                  const flow = doc.flows.entities[configurationFlow.id];
                  if (!flow.startingVariables) {
                    return;
                  }
                  flow.startingVariables.splice(index, 1);
                });
              }}
              handleRename={text => {
                if (!configurationFlow?.id) {
                  return;
                }
                docChange(doc => {
                  const flow = doc.flows.entities[configurationFlow.id];
                  if (!flow.startingVariables) {
                    return;
                  }
                  const variableToUpdate = flow.startingVariables[index];
                  variableToUpdate.text = text;
                  const identifier = createStartingVariableIdentifier(
                    variableToUpdate.text,
                    variableToUpdate.identifier
                  );
                  variableToUpdate.identifier =
                    identifier.toLowerCase() === "flowConfiguration"
                      ? // Cannot use "flowConfiguration" as an identifier, so we use a different one
                        createStartingVariableIdentifier(
                          `${variableToUpdate.text}_1`
                        )
                      : identifier;
                });
              }}
            />
          );
        })}
        <ConfigurationFormAddLine
          questionTypes={[
            QuestionTypes.TEXT,
            QuestionTypes.NUMBER,
            QuestionTypes.DATE,
            QuestionTypes.BOOLEAN,
            QuestionTypes.FILES,
            QuestionTypes.LIST,
            QuestionTypes.JSON
          ]}
          addQuestion={questionType => {
            if (!configurationFlow?.id) {
              return;
            }
            docChange(doc => {
              const flow = doc.flows.entities[configurationFlow.id];
              flow.startingVariables ??= [];

              const newVariable: VariableConfiguration = {
                // TODO: use autogenerate to make sure it's unique
                text: "Untitled",
                identifier: createStartingVariableIdentifier("Untitled"),
                type: questionType
              };

              if (newVariable.type === QuestionTypes.TABLE) {
                newVariable.properties = {
                  columns: []
                };
              } else if (
                [QuestionTypes.LIST, QuestionTypes.JSON].includes(
                  newVariable.type as QuestionTypes
                )
              ) {
                newVariable.properties = {
                  items: []
                };
              }
              flow.startingVariables.push(newVariable);
              setIsRenamingIndex(flow.startingVariables.length - 1); // Set the index of the newly added variable to be renamed
            });
          }}
          additionalDropdownItems={
            flowStepTypeConfig?.primaryIdentifier ===
            triggeredByAnotherFlowStepType
              ? ({ handleClose }) => {
                  return (
                    <DropdownItemGroup hasDivider title="Data">
                      <DropdownItemForm
                        handleClose={handleClose}
                        configurationFlow={configurationFlow}
                        document={document}
                        docChange={docChange}
                        d={d}
                        displayQuestionType={displayQuestionType}
                        createStartingVariableIdentifier={
                          createStartingVariableIdentifier
                        }
                      />
                      <DropdownItemFoundation
                        handleClose={handleClose}
                        configurationFlow={configurationFlow}
                        document={document}
                        docChange={docChange}
                        d={d}
                        displayQuestionType={displayQuestionType}
                        createStartingVariableIdentifier={
                          createStartingVariableIdentifier
                        }
                      />
                      <DropdownItemSeriesInterval
                        handleClose={handleClose}
                        configurationFlow={configurationFlow}
                        document={document}
                        docChange={docChange}
                        d={d}
                        displayQuestionType={displayQuestionType}
                        createStartingVariableIdentifier={
                          createStartingVariableIdentifier
                        }
                      />
                    </DropdownItemGroup>
                  );
                }
              : undefined
          }
        />
      </Stack>
    </WhiteboardBlock>
  );
};

const DropdownItemForm = ({
  handleClose,
  configurationFlow,
  document,
  docChange,
  d,
  displayQuestionType,
  createStartingVariableIdentifier
}: {
  handleClose: () => void;
  configurationFlow?: WorkspaceDocument["flows"]["entities"][string];
  document: Doc<WorkspaceDocument>;
  docChange: DocChange;
  d: Dictionary;
  displayQuestionType: DisplayQuestionType;
  createStartingVariableIdentifier: CreateStartingVariableIdentifier;
}) => {
  return (
    <DropdownItemNested
      title={d("ui.terminology.form")}
      leftElement={<Icon {...commonIcons.forms} />}
    >
      <Box
        style={{
          width: "164px"
        }}
      >
        <DropdownItemGroup>
          <DropdownItem
            key="any"
            onClick={() => {
              handleClose();
              if (!configurationFlow?.id) {
                return;
              }

              const questionTypeDisplay = displayQuestionType(
                ExtendedQuestionTypes.FORM
              );
              docChange(doc => {
                const flow = doc.flows.entities[configurationFlow.id];
                flow.startingVariables ??= [];

                const newVariable: VariableConfiguration = {
                  text: questionTypeDisplay.label,
                  identifier: createStartingVariableIdentifier(
                    questionTypeDisplay.label
                  ),
                  type: ExtendedQuestionTypes.FORM
                };

                flow.startingVariables.push(newVariable);
              });
            }}
          >
            <Text color="text-secondary">Any</Text>
          </DropdownItem>
        </DropdownItemGroup>
        <DropdownItemGroup
          hasDivider
          title={d("ui.terminology.formConfiguration")}
        >
          {Object.values(document.forms)?.map(formConfiguration => {
            const questionTypeDisplay = displayQuestionType(
              `${ExtendedQuestionTypes.FORM}.${formConfiguration.id}`
            );
            return (
              <DropdownItem
                key={formConfiguration.id}
                onClick={() => {
                  handleClose();
                  if (!configurationFlow?.id) {
                    return;
                  }
                  docChange(doc => {
                    const flow = doc.flows.entities[configurationFlow.id];
                    flow.startingVariables ??= [];

                    const newVariable: VariableConfiguration = {
                      text: questionTypeDisplay.label,
                      identifier: createStartingVariableIdentifier(
                        questionTypeDisplay.label
                      ),
                      type: `${ExtendedQuestionTypes.FORM}.${formConfiguration.id}`
                    };

                    flow.startingVariables.push(newVariable);
                  });
                }}
                description={questionTypeDisplay.description}
              >
                {questionTypeDisplay.label}
              </DropdownItem>
            );
          })}
        </DropdownItemGroup>
      </Box>
    </DropdownItemNested>
  );
};

const DropdownItemFoundation = ({
  handleClose,
  configurationFlow,
  document,
  docChange,
  d,
  displayQuestionType,
  createStartingVariableIdentifier
}: {
  handleClose: () => void;
  configurationFlow?: WorkspaceDocument["flows"]["entities"][string];
  document: Doc<WorkspaceDocument>;
  docChange: DocChange;
  d: Dictionary;
  displayQuestionType: DisplayQuestionType;
  createStartingVariableIdentifier: CreateStartingVariableIdentifier;
}) => {
  return (
    <DropdownItemNested
      title={d("ui.terminology.foundation")}
      leftElement={<Icon {...commonIcons.foundations} />}
    >
      <Box
        style={{
          width: "164px"
        }}
      >
        <DropdownItemGroup>
          <DropdownItem
            key={ExtendedQuestionTypes.FOUNDATION}
            onClick={() => {
              handleClose();
              if (!configurationFlow?.id) {
                return;
              }

              const questionTypeDisplay = displayQuestionType(
                ExtendedQuestionTypes.FOUNDATION
              );
              docChange(doc => {
                const flow = doc.flows.entities[configurationFlow.id];
                flow.startingVariables ??= [];

                const newVariable: VariableConfiguration = {
                  text: questionTypeDisplay.label,
                  identifier: createStartingVariableIdentifier(
                    questionTypeDisplay.label
                  ),
                  type: ExtendedQuestionTypes.FOUNDATION
                };

                flow.startingVariables.push(newVariable);
              });
            }}
          >
            <Text color="text-secondary">Any</Text>
          </DropdownItem>
        </DropdownItemGroup>
        <DropdownItemGroup
          hasDivider
          title={d("ui.terminology.foundationConfiguration")}
        >
          {document.foundations?.order?.map(foundationConfigurationId => {
            const foundationConfiguration =
              document.foundations?.entities[foundationConfigurationId];
            if (!foundationConfiguration) {
              return null;
            }
            const questionTypeDisplay = displayQuestionType(
              `${ExtendedQuestionTypes.FOUNDATION}.${foundationConfigurationId}`
            );
            return (
              <DropdownItem
                key={foundationConfigurationId}
                onClick={() => {
                  handleClose();
                  if (!configurationFlow?.id) {
                    return;
                  }
                  docChange(doc => {
                    const flow = doc.flows.entities[configurationFlow.id];
                    flow.startingVariables ??= [];

                    const newVariable: VariableConfiguration = {
                      text: questionTypeDisplay.label,
                      identifier: createStartingVariableIdentifier(
                        questionTypeDisplay.label
                      ),
                      type: `${ExtendedQuestionTypes.FOUNDATION}.${foundationConfigurationId}`
                    };

                    flow.startingVariables.push(newVariable);
                  });
                }}
                description={questionTypeDisplay.description}
              >
                {questionTypeDisplay.label}
              </DropdownItem>
            );
          })}
        </DropdownItemGroup>
      </Box>
    </DropdownItemNested>
  );
};

const DropdownItemSeriesInterval = ({
  handleClose,
  configurationFlow,
  document,
  docChange,
  d,
  displayQuestionType,
  createStartingVariableIdentifier
}: {
  handleClose: () => void;
  configurationFlow?: WorkspaceDocument["flows"]["entities"][string];
  document: Doc<WorkspaceDocument>;
  docChange: DocChange;
  d: Dictionary;
  displayQuestionType: DisplayQuestionType;
  createStartingVariableIdentifier: CreateStartingVariableIdentifier;
}) => {
  return (
    <DropdownItemNested
      title={d("ui.terminology.seriesInterval")}
      leftElement={<Icon {...commonIcons.series} />}
    >
      <Box
        style={{
          width: "164px"
        }}
      >
        <DropdownItemGroup>
          <DropdownItem
            key={ExtendedQuestionTypes.SERIES_INTERVAL}
            onClick={() => {
              handleClose();
              if (!configurationFlow?.id) {
                return;
              }

              const questionTypeDisplay = displayQuestionType(
                ExtendedQuestionTypes.SERIES_INTERVAL
              );
              docChange(doc => {
                const flow = doc.flows.entities[configurationFlow.id];
                flow.startingVariables ??= [];

                const newVariable: VariableConfiguration = {
                  text: questionTypeDisplay.label,
                  identifier: createStartingVariableIdentifier(
                    questionTypeDisplay.label
                  ),
                  type: ExtendedQuestionTypes.SERIES_INTERVAL
                };

                flow.startingVariables.push(newVariable);
              });
            }}
          >
            <Text color="text-secondary">Any</Text>
          </DropdownItem>
        </DropdownItemGroup>
        <DropdownItemGroup
          hasDivider
          title={d("ui.terminology.seriesInterval")}
        >
          {Object.values(document.series)?.map(seriesConfiguration => {
            if (!seriesConfiguration) {
              return null;
            }
            const questionTypeDisplay = displayQuestionType(
              `${ExtendedQuestionTypes.SERIES_INTERVAL}.${seriesConfiguration.id}`
            );
            return (
              <DropdownItem
                key={seriesConfiguration.id}
                onClick={() => {
                  handleClose();
                  if (!configurationFlow?.id) {
                    return;
                  }
                  docChange(doc => {
                    const flow = doc.flows.entities[configurationFlow.id];
                    flow.startingVariables ??= [];

                    const newVariable: VariableConfiguration = {
                      text: questionTypeDisplay.label,
                      identifier: createStartingVariableIdentifier(
                        questionTypeDisplay.label
                      ),
                      type: `${ExtendedQuestionTypes.SERIES_INTERVAL}.${seriesConfiguration.id}`
                    };

                    flow.startingVariables.push(newVariable);
                  });
                }}
                description={questionTypeDisplay.description}
              >
                {questionTypeDisplay.label}
              </DropdownItem>
            );
          })}
        </DropdownItemGroup>
      </Box>
    </DropdownItemNested>
  );
};
