import { type Monaco } from "@monaco-editor/react";
import { Thenable, type editor, type languages } from "monaco-editor";

const LANGUAGE_NAME = "otaiVariableLanguage";
const THEME_NAME = "otaiVariableTheme";

const regexToFindVariables = /\{\{([a-zA-Z0-9._]+)\}\}/g;

const editorOptions: editor.IStandaloneEditorConstructionOptions = {
  formatOnPaste: true,
  formatOnType: true,
  wordWrap: "on",
  lineNumbers: "on",
  lineHeight: 20,
  minimap: {
    enabled: true
  },
  selectOnLineNumbers: true,
  readOnly: false,
  padding: {
    bottom: 0
  },
  fixedOverflowWidgets: true
};

const characterPairs: languages.IAutoClosingPair[] = [
  { open: "{{", close: "}}" },
  { open: "{", close: "}" },
  { open: "[", close: "]" },
  { open: "(", close: ")" },
  { open: '"', close: '"' },
  { open: "'", close: "'" },
  { open: "`", close: "`" }
];

const getOnEnterRules: (monaco: Monaco) => languages.OnEnterRule[] = monaco => [
  {
    beforeText: /^\s*\(\s*\w*\s*$/,
    afterText: /^\s*\)\s*$/,
    action: {
      indentAction: monaco.languages.IndentAction.IndentOutdent
    }
  },
  {
    beforeText: /^\s*\[\s*\w*\s*$/,
    afterText: /^\s*\]\s*$/,
    action: {
      indentAction: monaco.languages.IndentAction.IndentOutdent
    }
  },
  {
    beforeText: /^\s*\{\s*\w*\s*$/,
    afterText: /^\s*\}\s*$/,
    action: {
      indentAction: monaco.languages.IndentAction.IndentOutdent
    }
  }
];

const languageDef:
  | languages.IMonarchLanguage
  | Thenable<languages.IMonarchLanguage> = {
  tokenizer: {
    root: [
      [/\{\{[^}]*\}\}/, "custom-variable"],
      [/\$[a-zA-Z]+/, "custom-function"],
      [/\d+/, "custom-number"],
      [/"[^"]*"|'[^']*'|`[^`]*`/, "custom-string"],
      [/\/\*[\s\S]*?\*\//, "custom-comment"],
      [/\/\/.*/, "custom-comment"]
    ]
  }
};

const themeData: editor.IStandaloneThemeData = {
  base: "vs",
  inherit: true,
  rules: [
    {
      token: "custom-comment",
      fontStyle: "italic",
      foreground: "888888" // grey
    },
    {
      token: "custom-variable",
      foreground: "4710E1", // dark purple
      fontStyle: "bold"
    },
    {
      token: "custom-function",
      foreground: "ff9900", // orange
      fontStyle: "bold"
    },
    {
      token: "custom-number",
      foreground: "008b8b" // teal
    },
    {
      token: "custom-string",
      foreground: "A31515" // dark red
    }
  ],
  colors: {}
};

export const variableCodeEditorConstants = {
  editorOptions,
  languageDef,
  characterPairs,
  getOnEnterRules,
  themeData,
  LANGUAGE_NAME,
  THEME_NAME,
  regexToFindVariables
};
