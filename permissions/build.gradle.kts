plugins {
    id("project.conventions")
}

group = "services.oneteam.ai.permissions"
version = "0.0.1"

dependencies {
    api("com.authzed.api:authzed:1.3.1")
    api("io.grpc:grpc-api:1.73.0")
    api("io.grpc:grpc-stub:1.73.0")
    implementation(ktorLibs.serialization.kotlinx.json)

    api(libs.logback.classic)

    testImplementation(kotlin("test"))
    testImplementation(libs.bundles.kotest)
    testImplementation(libs.mockito)
    testImplementation(libs.mockk)
    testImplementation(libs.assertj.core)
    testImplementation(libs.junit.jupiter.params)

}