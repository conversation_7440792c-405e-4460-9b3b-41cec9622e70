## Sample Data

We have a concept of sample data - workspaces which contain forms, flows, foundations, answers and annotations which
developers can use to test things out.

This consists of:

- a workspace configuration document
- multiple answer documents and annotation documents (optional)
- a config file describing what foundations and forms to create, and linking to answer/annotation documents

Currently sample data contains:

- sgpit
    - unit tests - small workspace configurations which allow developers easy access to configurations (forms, flows,
      etc) which they can use to test things and confirm how things look and work. (it would have been good to rename
      sgpit to unittest) - this saves the developers time since they don't have to manually set up these forms, flows
      etc, they can have them easily at hand.
- testrunner
    - Testrunner is more of an integration test - bigger more complex forms and flows.

We have a bruno request which lets us load sample data in either local or innovation.

So a developer should:

- load sgpit into local to ensure they have the latest version
- make some changes (eg add a form or flow)
- copy the workspace document back into source control
- update any other config necessary (foundations, answers etc.)
- push to git so other devs have it available.

We copy testrunner down from innovation whenever there are changes so devs have very easy path to run it locally (useful
to check changes haven't broken anything).

To use bruno to load sample data:

- ensure you have run the `util/Set Vars request` to set up required variables
- then run `Sample Data/create sample data sgpit` or `Sample Data/create sample data testrunner`