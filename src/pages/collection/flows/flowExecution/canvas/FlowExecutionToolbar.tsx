import React from "react";
import {
  generatePath,
  useNavigate,
  useOutletContext,
  useParams
} from "react-router-dom";

import {
  Box,
  DropdownItem,
  DropdownItemCheckbox,
  DropdownItemGroup,
  DropdownMenu,
  Icon,
  WhiteboardTool,
  WhiteboardToolVariant,
  WhiteboardToolbar
} from "@oneteam/onetheme";
import useOnClickOutside from "use-onclickoutside";

import { getLinkToFEDDebugPage } from "@helpers/debug";

import { routeConstants } from "@src/constants/routeConstants";
import { useDictionary } from "@src/hooks/useDictionary";
import { Workspace } from "@src/types/workspace";

import { useFlowExecutionContext } from "../FlowExecutionContext";

export const FlowExecutionToolbar = ({
  flowExecutionId,
  handleUpdateSetting
}: {
  flowExecutionId: string;
  handleUpdateSetting: (key: string, value: boolean) => void;
}) => {
  const [openItem, setOpenItem] = React.useState<string | undefined>(undefined);
  const ref = React.useRef(null);
  const params = useParams();
  const navigate = useNavigate();

  const { settings } = useFlowExecutionContext();

  useOnClickOutside(ref, () => {
    setOpenItem(undefined);
  });

  const { workspace } = useOutletContext<{ workspace: Workspace }>();
  const d = useDictionary();
  // TODO: Make a FlowToolbarWrapper for less repitition
  return (
    <Box ref={ref}>
      <WhiteboardToolbar
        style={{
          padding: "var(--spacing-050, 4px)",
          gap: "var(--components-whiteboard-toolbar-gap, 8px)"
        }}
      >
        {/* File  */}
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="File"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "file" ? undefined : "file"
                );
              }}
            />
          )}
          isOpen={openItem === "file"}
        >
          <DropdownItemGroup>
            <DropdownItem
              leftElement={<Icon name="description" />}
              onClick={() => {
                const linkToFED = getLinkToFEDDebugPage(
                  String(workspace.id),
                  flowExecutionId
                );
                window.open(linkToFED, "_blank");
              }}
            >
              {d("ui.terminology.execution")}
            </DropdownItem>
            <DropdownItem
              leftElement={<Icon name="arrow_back" />}
              onClick={() => {
                navigate(generatePath(routeConstants.flowRunner, params));
                setOpenItem(undefined);
              }}
            >
              {d("ui.collection.flowExecution.toolbar.file.back")}
            </DropdownItem>
          </DropdownItemGroup>
        </DropdownMenu>
        {/* Edit */}
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="Edit"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "edit" ? undefined : "edit"
                );
              }}
            />
          )}
          isOpen={openItem === "edit"}
        >
          <DropdownItemGroup></DropdownItemGroup>
        </DropdownMenu>
        {/* View */}
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="View"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "view" ? undefined : "view"
                );
              }}
            />
          )}
          isOpen={openItem === "view"}
          width="maxContent"
        >
          <DropdownItemGroup>
            <DropdownItemCheckbox
              isChecked={settings?.displayJson ?? false}
              onChange={updated => handleUpdateSetting("displayJson", updated)}
            >
              {d("ui.collection.flowExecution.toolbar.view.displayJson")}
            </DropdownItemCheckbox>
            <DropdownItemCheckbox
              isChecked={settings?.showMiniMap ?? false}
              onChange={updated => handleUpdateSetting("showMiniMap", updated)}
            >
              {d("ui.collection.flowExecution.toolbar.view.miniMap")}
            </DropdownItemCheckbox>
          </DropdownItemGroup>
        </DropdownMenu>
      </WhiteboardToolbar>
    </Box>
  );
};
