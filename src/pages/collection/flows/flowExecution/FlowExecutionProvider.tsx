import React, { PropsWithChildren, useState } from "react";

import { FlowExecutionDocument } from "@src/types/FlowConfiguration/FlowExecution";
import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { FlowExecutionContext } from "./FlowExecutionContext";

export interface FlowExecutionProviderProps {
  workspaceDocument: WorkspaceDocument;
}

export const FlowExecutionProvider = ({
  workspaceDocument,
  children
}: PropsWithChildren<FlowExecutionProviderProps>) => {
  const [selectedStepId, setSelectedStepId] = useState<string | undefined>(
    undefined
  );

  const [rootExecutionFlow, setRootExecutionFlow] = useState<
    FlowExecutionDocument | undefined
  >(undefined);

  const [executionFlow, setExecutionFlow] = useState<
    FlowExecutionDocument | undefined
  >(undefined);

  const [pathToCurrentExecution, setPathToCurrentExecution] = useState<
    string[] | undefined
  >();

  const [executionFlowForModal, setExecutionFlowForModal] = useState<
    FlowExecutionDocument | undefined
  >(undefined);

  const [iteratorStepPageMap, setIteratorStepPageMap] = useState<
    Record<FlowStepId, number>
  >({});

  const updateIteratorStepPageMap = React.useCallback(
    (stepId: string, page: number) => {
      setIteratorStepPageMap(prev => ({
        ...prev,
        [stepId]: page
      }));
    },
    []
  );

  const [summaryModalOpen, setSummaryModalOpen] = useState(true);

  const [settings, setSettings] = useState({
    displayJson: false,
    showMiniMap: true
  });

  const contextValue = React.useMemo(
    () => ({
      workspaceDocument,
      selectedStepId,
      setSelectedStepId,
      executionFlow,
      setExecutionFlow,
      rootExecutionFlow,
      setRootExecutionFlow,
      executionFlowForModal,
      setExecutionFlowForModal,
      pathToCurrentExecution,
      setPathToCurrentExecution,
      iteratorStepPageMap,
      updateIteratorStepPageMap,
      summaryModalOpen,
      setSummaryModalOpen,
      settings,
      setSettings
    }),
    [
      selectedStepId,
      executionFlow,
      executionFlowForModal,
      rootExecutionFlow,
      pathToCurrentExecution,
      workspaceDocument,
      iteratorStepPageMap,
      updateIteratorStepPageMap,
      summaryModalOpen,
      settings
    ]
  );

  return (
    <FlowExecutionContext.Provider value={contextValue}>
      {children}
    </FlowExecutionContext.Provider>
  );
};
