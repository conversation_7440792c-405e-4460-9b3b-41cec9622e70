import { renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, test } from "vitest";

import { useDictionary } from "../hooks/useDictionary";
import { DocError, emptyWorkspaceDocument } from "../types/documentTypes";
import { Document<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorFieldKeyMap } from "./DocumentErrorHelper";

describe("DocumentErrorHelper", () => {
  let errors: DocError[];
  let helper: DocumentErrorHelper;

  beforeEach(() => {
    errors = [
      {
        constraintDetail: "2",
        key: "name",
        message: "test1",
        path: "$.foundations.1.name",
        type: "required"
      },
      {
        constraintDetail: '["id","metadata","name"]',
        key: "name",
        path: "$.series.baCJtAYjm62Kqaycryoo3",
        type: "required"
      },
      {
        key: "name",
        type: "minLength",
        path: "$.series.baCJtAYjm62Kqaycryoo3.intervals.entities.intervalId1.name",
        constraintDetail: "2",
        message:
          "$.series.baCJtAYjm62Kqaycryoo3.intervals.entities.intervalId1.name: must be at least 2 characters long"
      },
      {
        key: "name",
        type: "minLength",
        path: "$.series.baCJtAYjm62Kqaycryoo3.intervals.entities.intervalId2.name",
        constraintDetail: "2",
        message:
          "$.series.baCJtAYjm62Kqaycryoo3.intervals.entities.intervalId2.name: must be at least 2 characters long"
      },
      {
        constraintDetail: "2",
        key: "key",
        path: "$.flows[2].key",
        type: "minLength"
      },
      {
        constraintDetail: "2",
        key: "key",
        path: "$.flows[1].key",
        type: "minLength"
      },
      {
        constraintDetail: "2",
        key: "description",
        path: "$.foundations.2.description", // proof of concept
        type: "minLength"
      },
      {
        constraintDetail: "2",
        key: "key",
        path: "$.forms.1.key",
        type: "minLength"
      },
      {
        constraintDetail: "2",
        key: "key",
        path: "$.forms.0.key",
        type: "minLength"
      },
      {
        constraintDetail: "2",
        key: "name",
        path: "$.forms.0.name",
        type: "minLength"
      },
      {
        key: "name",
        type: "duplicate",
        path: "$.foundations.HHWCYzyNq9uVbtZsLnqEl.name",
        constraintDetail: "Duplicate value: New foundation"
      }
    ];
    const { result } = renderHook(() => useDictionary());
    helper = new DocumentErrorHelper(
      {
        ...emptyWorkspaceDocument,
        errors,
        id: 1,
        name: "Testing",
        key: "TEST",
        metadata: {
          createdAt: "2025-01-01T00:00:00.000Z",
          updatedAt: "2025-01-01T00:00:00.000Z"
        }
      },
      result.current
    );
  });
  test("should return the correct error count by prefix", () => {
    expect(helper.getErrorCountByPrefix("$.forms")).toBe(3);
  });

  test("should return the correct error count by prefix with index", () => {
    expect(helper.getErrorCountByPrefix("$.forms.0")).toBe(2);
  });

  test("should return the correct error count by prefix with series id", () => {
    expect(helper.getErrorCountByPrefix("$.series.baCJtAYjm62Kqaycryoo3")).toBe(
      3
    );
  });

  test("should return zero if no errors match the prefix", () => {
    expect(helper.getErrorCountByPrefix("nonexistent")).toBe(0);
  });

  test("should return zero if no errors match the prefix", () => {
    expect(helper.getErrorCountByPrefix("")).toBe(0);
  });

  test("should return dictionary message for the foundations name error", () => {
    const actualErrors = helper.getDictionaryMessagesForErrors({
      prefix: "$.foundations.1",
      resourceName: "foundations",
      index: 1
    });
    const expectedErrors: ErrorFieldKeyMap[] = [
      {
        accessor: "foundations.1.name",
        message: "Name is required",
        field: "name"
      }
    ];
    expect(actualErrors?.sort()).to.deep.equal(expectedErrors.sort());
  });

  test("should return dictionary message for the forms key error", () => {
    const actualErrors = helper.getDictionaryMessagesForErrors({
      prefix: "$.forms.1",
      resourceName: "forms",
      index: 1
    });
    const expectedErrors: ErrorFieldKeyMap[] = [
      {
        accessor: "forms.1.key",
        message: "Key must be between 2 and 20 characters",
        field: "key"
      }
    ];
    expect(actualErrors?.sort()).to.deep.equal(expectedErrors.sort());
  });

  test("should return dictionary message for the series name error", () => {
    const actualErrors = helper.getDictionaryMessagesForErrors({
      prefix: "$.series.baCJtAYjm62Kqaycryoo3",
      resourceName: "series"
    });
    const expectedErrors: ErrorFieldKeyMap[] = [
      {
        accessor: "",
        message: "Name is required",
        field: "name"
      }
    ];
    expect(actualErrors?.sort()).to.deep.equal(expectedErrors.sort());
  });

  test("should return duplicate dictionary message for the foundations name error", () => {
    const actualErrors = helper.getDictionaryMessagesForErrors({
      prefix: "$.foundations.HHWCYzyNq9uVbtZsLnqEl",
      resourceName: "foundations",
      index: 3
    });
    const expectedErrors: ErrorFieldKeyMap[] = [
      {
        accessor: "foundations.3.name",
        message: "Name already in use. Choose another name",
        field: "name"
      }
    ];
    expect(actualErrors?.sort()).to.deep.equal(expectedErrors.sort());
  });

  test("should return duplicate dictionary message for the intervals name error", () => {
    const actualErrors = helper.getDictionaryMessagesForErrors({
      prefix: "$.series.baCJtAYjm62Kqaycryoo3.intervals.entities.intervalId1",
      resourceName: "series"
    });
    const expectedErrors: ErrorFieldKeyMap[] = [
      {
        accessor: "",
        message: "Name must be between 2 and 100 characters",
        field: "name"
      }
    ];
    expect(actualErrors?.sort()).to.deep.equal(expectedErrors.sort());
  });
});

describe("getErrorCountOfErroneousEntitiesByPrefix", {}, () => {
  let errors: DocError[];
  let helper: DocumentErrorHelper;
  beforeEach(() => {
    errors = [
      {
        path: "$.foundations.entities.abc.name",
        key: "name",
        type: "required",
        constraintDetail: "2"
      },
      {
        path: "$.foundations.entities.abc.name",
        key: "name",
        type: "required",
        constraintDetail: "2"
      },
      {
        path: "$.foundations.entities.abcd.name",
        key: "name",
        type: "required",
        constraintDetail: "2"
      },
      {
        path: "$.flows.entities",
        key: "name",
        type: "required",
        constraintDetail: "2"
      },
      {
        path: "$.series.abc.name",
        key: "name",
        type: "required",
        constraintDetail: "2"
      },
      {
        path: "$.series.abc.name2",
        key: "name",
        type: "required",
        constraintDetail: "2"
      },
      {
        path: "$.forms.abc.name",
        key: "name",
        type: "required",
        constraintDetail: "2"
      },
      {
        path: "$.forms.abc1.name",
        key: "name",
        type: "required",
        constraintDetail: "2"
      },
      {
        path: "$.forms.abc2.name",
        key: "name",
        type: "required",
        constraintDetail: "2"
      }
    ];
    const { result } = renderHook(() => useDictionary());
    helper = new DocumentErrorHelper(
      {
        ...emptyWorkspaceDocument,
        errors,
        id: 1,
        name: "Testing",
        key: "TEST",
        metadata: {
          createdAt: "2025-01-01T00:00:00.000Z",
          updatedAt: "2025-01-01T00:00:00.000Z"
        }
      },
      result.current
    );
  });

  test("should return correct erroneous entities by foundations prefix", () => {
    expect(
      helper.getErrorCountOfErroneousEntitiesByPrefix("$.foundations")
    ).toBe(2);
  });
  test("should should return correct erroneous entities by series prefix", () => {
    expect(helper.getErrorCountOfErroneousEntitiesByPrefix("$.series")).toBe(1);
  });
  test("should should return correct erroneous entities by forms prefix", () => {
    expect(helper.getErrorCountOfErroneousEntitiesByPrefix("$.forms")).toBe(3);
  });
  test("should should return zero erroneous entities by wrong flows prefix", () => {
    expect(helper.getErrorCountOfErroneousEntitiesByPrefix("$.flows")).toBe(0);
  });
});
