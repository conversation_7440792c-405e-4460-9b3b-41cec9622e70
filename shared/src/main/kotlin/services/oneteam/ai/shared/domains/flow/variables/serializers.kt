package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.DeserializationStrategy
import kotlinx.serialization.json.JsonContentPolymorphicSerializer
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject

object VariablePropertiesSerializer : JsonContentPolymorphicSerializer<VariableProperties>(VariableProperties::class) {
    override fun selectDeserializer(element: JsonElement): DeserializationStrategy<VariableProperties> {
        require(element is JsonObject) { "Unsupported type" }

        return when {
            element.containsKey("items") -> VariableProperties.JsonVariableProperties.serializer()
            element.containsKey("operation") -> VariableProperties.TableVariableProperties.serializer()
            element.containsKey("listOperation") -> VariableProperties.ListVariableProperties.serializer()
            element.containsKey("fileOperation") -> VariableProperties.FileVariableProperties.serializer()
            else -> VariableProperties.OtherVariableProperties.serializer()
        }
    }
}

//enums are not a compile time constant, need to use constants for use within attributes.
// think of this as an enum
sealed class VariableType {
    object VARIABLE : VariableType() {
        const val NAME = "VARIABLE"
    }

    object SECURED_VARIABLE : VariableType() {
        const val NAME = "SECURED_VARIABLE"
    }

    object CONFIGURATION : VariableType() {
        const val NAME = "CONFIGURATION"
    }

    companion object {
        fun fromString(name: String): VariableType? {
            return when (name) {
                VARIABLE.NAME -> VARIABLE
                SECURED_VARIABLE.NAME -> SECURED_VARIABLE
                CONFIGURATION.NAME -> CONFIGURATION
                else -> null
            }
        }
    }
}