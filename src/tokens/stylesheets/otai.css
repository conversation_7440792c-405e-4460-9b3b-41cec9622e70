/* WARNING: This file is auto-generated from Figma design tokens, do not manually update this file */

:root {
  --border-radius-on: true;
  --invert-on: false;
  /* primitives-* variables are not for direct use */
  --primitives-color-palette-grey-000: #ffffff;
  --primitives-color-palette-grey-000-transparent-80: #ffffffcc;
  --primitives-color-palette-grey-primary-lightest: #f4f2ff;
  --primitives-color-palette-grey-050: #f4f5f7;
  --primitives-color-palette-grey-050-transparent-80: #f4f5f7cc;
  --primitives-color-palette-grey-075: #f1f3f9;
  --primitives-color-palette-grey-100: #eceef4;
  --primitives-color-palette-grey-150: #eef2f6;
  --primitives-color-palette-grey-200: #dde3eb;
  --primitives-color-palette-grey-300: #cfd3e0;
  --primitives-color-palette-grey-350: #bdc3d6;
  --primitives-color-palette-grey-400: #848fa1;
  --primitives-color-palette-grey-500: #848fa1;
  --primitives-color-palette-grey-600: #4f5c70;
  --primitives-color-palette-grey-700: #091426;
  --primitives-color-palette-grey-700-transparent-80: #091426cc;
  --primitives-color-palette-color: #683edf;
  --primitives-color-palette-onColor: #ffffff;
  --primitives-color-palette-colorLight: #9f7cfe;
  --primitives-color-palette-onColorLight: #ffffff;
  --primitives-color-palette-colorLightTransparent50: #9f7cfe80;
  --primitives-color-palette-colorLighter: #e1d8fc;
  --primitives-color-palette-onColorLighter: var(
    --primitives-color-palette-grey-700
  );
  --primitives-color-palette-colorLightest: #f4f2ff;
  --primitives-color-palette-onColorLightest: var(
    --primitives-color-palette-grey-700
  );
  --primitives-color-palette-colorDark: #4710e1;
  --primitives-color-palette-onColorDark: #ffffff;
  --primitives-color-palette-other-1-color-background: #e3faeb;
  --primitives-color-palette-colorDarker: #200173;
  --primitives-color-palette-sticky-notes-default: #fff171;
  --primitives-color-palette-onColorDarker: #ffffff;
  --primitives-color-palette-traffic-info-light: #eef4ff;
  --primitives-color-palette-traffic-info-mid: #4795f2;
  --primitives-color-palette-traffic-info-dark: #1376ea;
  --primitives-color-palette-traffic-success-light: #e5f7e7;
  --primitives-color-palette-traffic-success-mid: #42bb4d;
  --primitives-color-palette-traffic-success-dark: #007300;
  --primitives-color-palette-traffic-warning-light: #ffefdf;
  --primitives-color-palette-traffic-warning-mid: #ef9849;
  --primitives-color-palette-traffic-warning-dark: #c35e00;
  --primitives-color-palette-primary: var(--primitives-color-palette-grey-700);
  --primitives-color-palette-traffic-danger-light: #ffebef;
  --primitives-color-palette-sticky-notes-default-fold: #ffd861;
  --primitives-color-palette-onPrimary: #ffffff;
  --primitives-color-palette-secondary: var(
    --primitives-color-palette-grey-600
  );
  --primitives-color-palette-onSecondary: #ffffff;
  --primitives-color-palette-accent: var(--primitives-color-palette-grey-600);
  --primitives-color-palette-onAccent: #ffffff;
  --primitives-color-palette-focus: #1a7ef4;
  --primitives-color-palette-sticky-notes-blue: #aefaff;
  --primitives-color-palette-sticky-notes-green: #c0ffb1;
  --primitives-color-palette-sticky-notes-pink: #ffdffe;
  --primitives-color-palette-sticky-notes-grey: #ececec;
  --primitives-color-palette-traffic-danger-mid: #ee4f6d;
  --primitives-color-palette-other-1-color-text: #168556;
  --primitives-color-palette-graph-1-light: #a7ede1;
  --primitives-color-palette-graph-1-mid: #27dbbb;
  --primitives-color-palette-graph-1-dark: #0f977f;
  --primitives-color-palette-traffic-danger-dark: #af002c;
  --primitives-color-palette-other-2-color-background: #eef4ff;
  --primitives-color-palette-other-2-color-text: #5456d5;
  --primitives-color-palette-other-3-color-background: #ffedfc;
  --primitives-color-palette-other-3-color-text: #bd21a3;
  --primitives-color-palette-other-4-color-background: #fceac7;
  --primitives-color-palette-other-4-color-text: #a57d2f;
  --primitives-color-palette-other-5-color-background: #ffffff00;
  --primitives-color-palette-other-5-color-border: #ffffff00;
  /* Usable variables from here */
  --font-family: "Noto Sans";
  --font-family-heading: "Noto Sans";
  --font-size-body-xs: 11px;
  --font-size-body-s: 12px;
  --font-size-body-m: 14px;
  --font-size-body-l: 16px;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semi-bold: 600;
  --font-weight-bold: 700;
  --icon-size-s: 14px;
  --icon-size-xs: 11px;
  --border-radius-rounded-full: var(--spacing-1000);
  --border-radius-always-rounded-full: var(--spacing-1000);
  --border-radius-rounded-most: var(--spacing-250);
  --border-radius-always-rounded-most: var(--spacing-250);
  --border-radius-rounded-more: var(--spacing-150);
  --border-radius-always-rounded-more: var(--spacing-150);
  --border-radius-rounded: var(--spacing-100);
  --border-radius-always-rounded: var(--spacing-100);
  --border-radius-rounded-slightly: var(--spacing-050);
  --border-radius-always-rounded-slightly: var(--spacing-050);
  --border-radius-rounded-slightest: var(--spacing-025);
  --border-radius-always-rounded-slightest: var(--spacing-025);
  --border-radius-rounded-not: var(--spacing-000);
  --border-radius-always-rounded-not: var(--spacing-000);
  --border-width-none: 0px;
  --border-width-small: 1px;
  --border-width-medium: 2px;
  --icon-size-m: 18px;
  --icon-size-l: 22px;
  --icon-size-xl: 28px;
  --font-size-heading-xxl: 36px;
  --font-size-heading-xl: 29px;
  --font-size-heading-l: 24px;
  --font-size-heading-m: 20px;
  --font-size-heading-s: 16px;
  --font-size-heading-xs: 14px;
  --font-size-heading-xxs: 12px;
  --theme-default: true;
  --border-radius-on: true;
  --color-primary: var(--primitives-color-palette-primary);
  --color-onPrimary: var(--primitives-color-palette-onPrimary);
  --color-secondary: var(--primitives-color-palette-secondary);
  --color-onSecondary: var(--primitives-color-palette-onSecondary);
  --color-accent: var(--primitives-color-palette-accent);
  --color-onAccent: var(--primitives-color-palette-onAccent);
  --color-color: var(--primitives-color-palette-color);
  --color-onColor: var(--primitives-color-palette-onColor);
  --color-colorLight: var(--primitives-color-palette-colorLight);
  --color-onColorLight: var(--primitives-color-palette-onColorLight);
  --color-colorLighter: var(--primitives-color-palette-colorLighter);
  --color-onColorLighter: var(--primitives-color-palette-onColorLighter);
  --color-colorLightest: var(--primitives-color-palette-colorLightest);
  --color-onColorLightest: var(--primitives-color-palette-onColorLightest);
  --color-colorDark: var(--primitives-color-palette-colorDark);
  --color-onColorDark: var(--primitives-color-palette-onColorDark);
  --color-colorDarker: var(--primitives-color-palette-colorDarker);
  --color-onColorDarker: var(--primitives-color-palette-onColorDarker);
  --color-disabledSurface: #f2f2f2;
  --color-disabled: var(--primitives-color-palette-grey-300);
  --color-onDisabled: var(--primitives-color-palette-grey-400);
  --color-invert: var(--primitives-color-palette-grey-000);
  --color-onInvert: var(--primitives-color-palette-grey-700);
  --color-border: var(--primitives-color-palette-grey-200);
  --color-highlight: var(--primitives-color-palette-grey-150);
  --color-transparent: #ffffff00;
  --color-link: #1a7ef4;
  --color-focus: var(--primitives-color-palette-focus);
  --color-clickable: var(--color-primary);
  --color-clickable-hover: var(--color-accent);
  --color-traffic-infoSurface: var(
    --primitives-color-palette-traffic-info-light
  );
  --color-traffic-info: var(--primitives-color-palette-traffic-info-mid);
  --color-traffic-onInfo: var(--primitives-color-palette-traffic-info-dark);
  --color-traffic-successSurface: var(
    --primitives-color-palette-traffic-success-light
  );
  --color-traffic-success: var(--primitives-color-palette-traffic-success-mid);
  --color-traffic-onSuccess: var(
    --primitives-color-palette-traffic-success-dark
  );
  --color-traffic-warningSurface: var(
    --primitives-color-palette-traffic-warning-light
  );
  --color-traffic-warning: var(--primitives-color-palette-traffic-warning-mid);
  --color-traffic-onWarning: var(
    --primitives-color-palette-traffic-warning-dark
  );
  --color-traffic-dangerSurface: var(
    --primitives-color-palette-traffic-danger-light
  );
  --color-traffic-danger: var(--primitives-color-palette-traffic-danger-mid);
  --color-traffic-onDanger: var(--primitives-color-palette-traffic-danger-dark);
  --color-surface-primary: var(--primitives-color-palette-grey-050);
  --color-surface-primary-overlay: var(
    --primitives-color-palette-grey-050-transparent-80
  );
  --color-surface-onPrimary: var(--primitives-color-palette-grey-700);
  --color-surface-secondary: var(--primitives-color-palette-grey-000);
  --color-surface-secondary-overlay: var(
    --primitives-color-palette-grey-000-transparent-80
  );
  --color-surface-onSecondary: var(--primitives-color-palette-grey-700);
  --color-surface-invert-primary: var(--primitives-color-palette-grey-600);
  --color-surface-invert-primary-overlay: #4f5c70cc;
  --color-surface-invert-onPrimary: var(--primitives-color-palette-grey-000);
  --color-surface-invert-secondary: var(--primitives-color-palette-grey-700);
  --color-surface-invert-secondary-overlay: var(
    --primitives-color-palette-grey-700-transparent-80
  );
  --color-surface-invert-onSecondary: var(--primitives-color-palette-grey-000);
  --color-border-primary: var(--primitives-color-palette-grey-200);
  --color-border-secondary: var(--primitives-color-palette-grey-150);
  --color-border-tertiary: var(--primitives-color-palette-grey-000);
  --color-text-primary: var(--primitives-color-palette-grey-700);
  --color-text-secondary: var(--primitives-color-palette-grey-600);
  --color-text-tertiary: var(--primitives-color-palette-grey-500);
  --color-text-disabled: var(--primitives-color-palette-grey-350);
  --color-text-invert-primary: var(--primitives-color-palette-grey-000);
  --color-text-invert-secondary: var(--primitives-color-palette-grey-100);
  --color-text-invert-tertiary: var(--primitives-color-palette-grey-200);
  --color-text-invert-disabled: var(--primitives-color-palette-grey-350);
  --spacing-000: 0px;
  --spacing-025: 2px;
  --spacing-050: 4px;
  --spacing-075: 6px;
  --spacing-100: 8px;
  --spacing-150: 12px;
  --spacing-200: 16px;
  --spacing-250: 20px;
  --spacing-300: 24px;
  --spacing-350: 28px;
  --spacing-400: 32px;
  --spacing-500: 40px;
  --spacing-600: 48px;
  --spacing-700: 56px;
  --spacing-800: 64px;
  --spacing-900: 72px;
  --spacing-1000: 80px;
  --components-card-padding-vertical: var(--spacing-200);
  --components-card-padding-horizontal: var(--spacing-200);
  --components-card-gap: var(--spacing-100);
  --components-card-border-radius: var(--border-radius-rounded-more);
  --components-card-small-padding-vertical: var(--spacing-150);
  --components-card-color-border: var(--color-border);
  --components-card-color-background: var(--color-surface-secondary);
  --components-card-large-padding-vertical: var(--spacing-300);
  --components-card-large-padding-horizontal: var(--spacing-300);
  --components-card-large-gap: var(--spacing-200);
  --components-card-large-border-radius: var(--border-radius-rounded-more);
  --components-card-border-width: var(--border-width-small);
  --components-card-small-padding-horizontal: var(
    --components-card-small-padding-vertical
  );
  --components-card-small-gap: var(--components-card-gap);
  --components-card-small-border-radius: var(--border-radius-rounded);
  --components-pill-padding-vertical: var(--spacing-025);
  --components-pill-padding-horizontal: var(--spacing-050);
  --components-pill-border-radius: var(--border-radius-always-rounded-slightly);
  --components-progress-bar-padding-vertical: var(--spacing-000);
  --components-progress-bar-padding-horizontal: var(--spacing-000);
  --components-progress-bar-border-radius: var(--border-radius-rounded-full);
  --components-pill-prominent-neutral-color-background: var(
    --primitives-color-palette-grey-150
  );
  --components-pill-prominent-neutral-color-border: #cfd3e08f;
  --components-pill-prominent-neutral-color-indicator: var(
    --color-text-tertiary
  );
  --components-pill-prominent-neutral-color-text: var(--color-text-primary);
  --components-pill-gap: var(--spacing-025);
  --components-pill-subtle-color-background: var(
    --components-pill-prominent-neutral-color-background
  );
  --components-pill-subtle-color-border: var(--color-disabled);
  --components-pill-subtle-color-text: var(--color-text-tertiary);
  --components-pill-icon-size: 14px;
  --components-pill-dot-size: 8px;
  --components-pill-height: 18px;
  --components-pill-border-width: var(--border-width-small);
  --components-pill-strong-neutral-color-background: var(
    --components-pill-prominent-neutral-color-indicator
  );
  --components-pill-strong-colored-color-background: var(
    --components-pill-prominent-colored-color-indicator
  );
  --components-pill-strong-colored-color-border: var(
    --components-pill-strong-colored-color-background
  );
  --components-pill-strong-colored-color-indicator: var(
    --primitives-color-palette-onColor
  );
  --components-pill-strong-colored-color-text: var(
    --primitives-color-palette-onColor
  );
  --components-pill-strong-neutral-color-border: var(
    --components-pill-strong-neutral-color-background
  );
  --components-pill-strong-neutral-color-indicator: var(
    --color-text-invert-primary
  );
  --components-pill-strong-neutral-color-text: var(--color-text-invert-primary);
  --components-pill-transparent-color-background: var(--color-transparent);
  --components-pill-transparent-color-border: var(
    --components-pill-transparent-color-background
  );
  --components-pill-transparent-color-text: var(
    --components-pill-prominent-neutral-color-text
  );
  --components-pill-prominent-outlined-color-background: var(
    --color-surface-secondary
  );
  --components-pill-prominent-outlined-color-border: var(--color-border);
  --components-pill-prominent-outlined-color-indicator: var(
    --color-text-tertiary
  );
  --components-pill-prominent-outlined-color-text: var(--color-text-primary);
  --components-pill-prominent-colored-color-background: var(
    --primitives-color-palette-colorLightest
  );
  --components-pill-prominent-colored-color-border: #9f7cfe4d;
  --components-pill-prominent-colored-color-indicator: var(
    --primitives-color-palette-color
  );
  --components-pill-prominent-colored-color-text: var(
    --primitives-color-palette-colorDark
  );
  --components-pill-prominent-info-color-background: var(
    --primitives-color-palette-traffic-info-light
  );
  --components-pill-prominent-info-color-border: #4795f24d;
  --components-pill-prominent-info-color-indicator: var(
    --primitives-color-palette-traffic-info-mid
  );
  --components-pill-prominent-info-color-text: var(
    --primitives-color-palette-traffic-info-dark
  );
  --components-pill-strong-info-color-background: var(
    --components-pill-prominent-info-color-indicator
  );
  --components-pill-strong-info-color-border: var(
    --components-pill-strong-info-color-background
  );
  --components-pill-strong-info-color-indicator: var(
    --color-text-invert-primary
  );
  --components-pill-strong-info-color-text: var(--color-text-invert-primary);
  --components-pill-prominent-success-color-background: var(
    --primitives-color-palette-traffic-success-light
  );
  --components-pill-prominent-success-color-border: #42bb4d4d;
  --components-pill-prominent-success-color-indicator: var(
    --primitives-color-palette-traffic-success-mid
  );
  --components-pill-prominent-success-color-text: var(
    --primitives-color-palette-traffic-success-dark
  );
  --components-pill-strong-success-color-background: var(
    --components-pill-prominent-success-color-indicator
  );
  --components-pill-strong-success-color-border: var(
    --components-pill-strong-success-color-background
  );
  --components-pill-strong-success-color-indicator: var(
    --color-text-invert-primary
  );
  --components-pill-strong-success-color-text: var(--color-text-invert-primary);
  --components-pill-prominent-warning-color-background: var(
    --primitives-color-palette-traffic-warning-light
  );
  --components-pill-prominent-warning-color-border: #ef98494d;
  --components-pill-prominent-warning-color-indicator: var(
    --primitives-color-palette-traffic-warning-mid
  );
  --components-pill-prominent-warning-color-text: var(
    --primitives-color-palette-traffic-warning-dark
  );
  --components-pill-prominent-danger-color-background: var(
    --primitives-color-palette-traffic-danger-light
  );
  --components-pill-prominent-danger-color-border: #ee4f6d4d;
  --components-pill-prominent-danger-color-indicator: var(
    --primitives-color-palette-traffic-danger-mid
  );
  --components-pill-prominent-danger-color-text: var(
    --primitives-color-palette-traffic-danger-dark
  );
  --components-pill-strong-warning-color-background: var(
    --components-pill-prominent-warning-color-indicator
  );
  --components-pill-strong-warning-color-border: var(
    --components-pill-strong-warning-color-background
  );
  --components-pill-strong-warning-color-indicator: var(
    --color-text-invert-primary
  );
  --components-pill-strong-warning-color-text: var(--color-text-invert-primary);
  --components-pill-strong-danger-color-background: var(
    --components-pill-prominent-danger-color-indicator
  );
  --components-pill-strong-danger-color-border: var(
    --components-pill-strong-danger-color-background
  );
  --components-pill-strong-danger-color-indicator: var(
    --color-text-invert-primary
  );
  --components-pill-strong-danger-color-text: var(--color-text-invert-primary);
  --components-pill-font-size: var(--font-size-body-s);
  --components-pill-font-weight: var(--font-weight-semi-bold);
  --components-pill-icon-only-color-background: var(--color-transparent);
  --components-pill-icon-only-color-border: var(
    --components-pill-icon-only-color-background
  );
  --components-pill-icon-only-color-text: var(
    --components-pill-prominent-neutral-color-text
  );
  --components-progress-bar-height: var(--spacing-100);
  --components-progress-bar-color-background: var(--color-surface-primary);
  --components-progress-bar-progress-color-background: var(--color-color);
  --components-status-circle-info-color-icon: var(--color-traffic-info);
  --components-status-circle-info-color-border: var(
    --components-status-circle-info-color-icon
  );
  --components-status-circle-info-color-background: var(
    --color-traffic-infoSurface
  );
  --components-status-circle-success-color-icon: var(--color-traffic-success);
  --components-status-circle-success-color-border: var(
    --components-status-circle-success-color-icon
  );
  --components-status-circle-success-color-background: var(
    --color-traffic-successSurface
  );
  --components-status-circle-warning-color-icon: var(--color-traffic-warning);
  --components-status-circle-warning-color-border: var(
    --components-status-circle-warning-color-icon
  );
  --components-status-circle-warning-color-background: var(
    --color-traffic-warningSurface
  );
  --components-status-circle-danger-color-icon: var(--color-traffic-danger);
  --components-status-circle-danger-color-border: var(
    --components-status-circle-danger-color-icon
  );
  --components-status-circle-danger-color-background: var(
    --color-traffic-dangerSurface
  );
  --components-status-circle-size: var(--spacing-400);
  --components-status-circle-color-border: var(--color-border);
  --components-status-circle-color-background: var(--color-surface-secondary);
  --components-floating-with-parent-offset: 4px;
  --components-status-line-info-color-background: var(--color-traffic-info);
  --components-status-line-success-color-background: var(
    --color-traffic-success
  );
  --components-status-line-warning-color-background: var(
    --color-traffic-warning
  );
  --components-status-line-danger-color-background: var(--color-traffic-danger);
  --components-status-line-not-started-color-background: var(--color-disabled);
  --components-status-line-in-progress-color-background: var(--color-disabled);
  --components-status-line-completed-color-background: var(
    --components-status-line-success-color-background
  );
  --components-tab-group-gap: var(--spacing-100);
  --components-tab-group-item-gap: var(--spacing-050);
  --components-tab-group-item-padding-vertical: var(--spacing-100);
  --components-tab-group-item-padding-horizontal: var(--spacing-075);
  --components-tab-group-item-hover-color-background: var(
    --components-select-option-hover-color-background
  );
  --components-tab-group-item-inactive-border-width: var(--border-width-small);
  --components-tab-group-item-inactive-color-text: var(--color-text-secondary);
  --components-tab-group-item-inactive-color-background: var(
    --color-transparent
  );
  --components-tab-group-item-inactive-color-border: var(
    --color-border-primary
  );
  --components-tab-group-bottom-border-width: var(--border-width-small);
  --components-tab-group-color-border: var(--color-border-primary);
  --components-tab-group-item-inactive-font-weight: var(--font-weight-regular);
  --components-tab-group-height: 34px;
  --components-tab-group-item-active-border-width: var(--border-width-medium);
  --components-tab-group-item-active-color-text: var(--color-text-primary);
  --components-navigation-top-item-type: default;
  --components-tab-group-item-active-color-background: var(--color-transparent);
  --components-navigation-top-padding-horizontal: var(--spacing-100);
  --components-navigation-top-padding-vertical: var(--spacing-000);
  --components-navigation-top-gap: var(--spacing-100);
  --components-modal-color-background: var(--color-surface-secondary);
  --components-navigation-top-height: var(--spacing-600);
  --components-alert-gap: var(--spacing-075);
  --components-tab-group-item-active-color-border: var(--color-color);
  --components-badge-size: var(--spacing-200);
  --components-overlay-color-background: #4f5c7080;
  --components-modal-color-border: var(--color-border-primary);
  --components-alert-padding-vertical: var(--spacing-075);
  --components-alert-padding-horizontal: var(
    --components-alert-padding-vertical
  );
  --components-alert-border-radius: var(--border-radius-rounded-slightly);
  --components-alert-border-width: var(--border-width-none);
  --components-alert-font-size: var(--font-size-body-m);
  --components-modal-gap: var(--spacing-075);
  --components-modal-padding-vertical: var(--spacing-200);
  --components-modal-padding-horizontal: var(--spacing-200);
  --components-modal-border-radius: var(--border-radius-rounded-more);
  --components-modal-border-width: var(--border-width-none);
  --components-alert-icon-size: var(--icon-size-l);
  --components-alert-font-weight: var(--font-weight-regular);
  --components-badge-font-size: var(--font-size-body-s);
  --components-badge-font-weight: var(--font-weight-semi-bold);
  --components-badge-border-radius: var(--components-badge-size);
  --components-navigation-top-item-height: var(
    --components-navigation-top-height
  );
  --components-navigation-top-color-background: var(--color-surface-secondary);
  --components-tab-group-item-active-font-weight: var(--font-weight-semi-bold);
  --components-table-header-border-bottom-width: var(--border-width-medium);
  --components-navigation-top-color-border: var(
    --primitives-color-palette-grey-100
  );
  --components-navigation-top-border-width: var(--border-width-small);
  --components-navigation-top-item-group-gap: var(--spacing-000);
  --components-navigation-top-item-group-padding-horizontal: var(--spacing-050);
  --components-navigation-top-item-padding-horizontal: var(--spacing-150);
  --components-table-header-color-border: var(--color-border);
  --components-navigation-top-item-font-size: var(--font-size-body-m);
  --components-overlay-background-blur: 0px;
  --components-modal-background-blur: 6px;
  --components-badge-default-color-background: var(--color-color);
  --components-modal-heading-color-text: var(--color-text-primary);
  --components-modal-heading-font-size: var(--font-size-heading-m);
  --components-badge-color-text: var(--color-onColor);
  --components-table-color-border: var(--color-border);
  --components-table-color-background: var(--color-surface-secondary);
  --components-table-border-width: var(--border-width-small);
  --components-table-border-radius: var(--border-radius-rounded);
  --components-drag-and-drop-file-padding-vertical: var(--spacing-200);
  --components-drag-and-drop-file-padding-horizontal: var(--spacing-200);
  --components-drag-and-drop-file-min-height: var(--spacing-1000);
  --components-drag-and-drop-file-gap: var(--spacing-050);
  --components-drag-and-drop-file-color-placeholder-text: var(
    --color-text-secondary
  );
  --components-drag-and-drop-file-color-icon: var(
    --components-drag-and-drop-file-color-placeholder-text
  );
  --components-drag-and-drop-file-icon-size: var(--icon-size-l);
  --components-drag-and-drop-file-color-background: var(
    --color-surface-secondary
  );
  --components-drag-and-drop-file-color-border: var(--color-border-primary);
  --components-drag-and-drop-file-border-width: var(--border-width-small);
  --components-drag-and-drop-file-dash-size: var(--spacing-100);
  --components-drag-and-drop-file-active-color-placeholder-text: var(
    --color-text-secondary
  );
  --components-drag-and-drop-file-active-color-icon: var(--color-color);
  --components-drag-and-drop-file-active-color-background: var(
    --color-colorLightest
  );
  --components-drag-and-drop-file-active-color-border: var(--color-color);
  --components-breadcrumbs-padding-vertical: var(--spacing-025);
  --components-breadcrumbs-padding-horizontal: var(--spacing-000);
  --components-select-padding-vertical: var(--spacing-050);
  --components-select-padding-horizontal: var(--spacing-050);
  --components-select-gap: var(--spacing-050);
  --components-breadcrumbs-color-text: var(--color-text-secondary);
  --components-breadcrumbs-color-spacer: var(--color-text-tertiary);
  --components-breadcrumbs-active-color-text: var(
    --components-breadcrumbs-color-text
  );
  --components-breadcrumbs-active-font-weight: var(--font-weight-regular);
  --components-table-header-color-background: var(--color-surface-secondary);
  --components-breadcrumbs-spacer-font-size: 16px;
  --components-breadcrumbs-gap: var(--spacing-100);
  --components-breadcrumbs-font-size: var(--font-size-body-m);
  --components-breadcrumbs-font-weight: var(--font-weight-regular);
  --components-breadcrumbs-border-radius: var(--border-radius-rounded-slightly);
  --components-select-color-background: var(--color-surface-secondary);
  --components-select-option-color-background: var(
    --components-select-color-background
  );
  --components-select-option-color-text: var(--color-text-primary);
  --components-select-option-color-description-text: var(--color-text-tertiary);
  --components-select-option-border-radius: var(
    --border-radius-rounded-slightly
  );
  --components-select-option-padding-vertical: var(--spacing-100);
  --components-select-option-padding-horizontal: var(--spacing-100);
  --components-select-color-border: var(--color-border);
  --components-select-border-width: 1px;
  --components-select-border-radius: var(--border-radius-rounded);
  --components-select-gap-color-border: var(--color-transparent);
  --components-select-gap-border-width: 1px;
  --components-select-margin-top: var(--spacing-050);
  --components-select-max-height: 200px;
  --components-select-option-hover-color-background: var(--color-highlight);
  --components-select-option-hover-color-text: var(
    --components-select-option-color-text
  );
  --components-select-option-hover-color-description-text: var(
    --color-text-secondary
  );
  --components-select-option-selected-color-background: var(
    --primitives-color-palette-colorLightest
  );
  --components-select-option-selected-color-text: var(--color-colorDark);
  --components-select-option-selected-color-description-text: var(
    --color-color
  );
  --components-select-option-selected-hover-color-background: var(
    --primitives-color-palette-colorLighter
  );
  --components-select-option-selected-hover-color-text: var(
    --color-colorDarker
  );
  --components-select-option-selected-hover-color-description-text: var(
    --color-colorDark
  );
  --components-link-color-text: var(--color-link);
  --components-label-color-text: #3e4450;
  --components-label-disabled-color-text: var(--color-text-disabled);
  --components-label-description-disabled-color-text: var(
    --components-label-disabled-color-text
  );
  --components-label-font-size: var(--font-size-body-s);
  --components-label-description-color-text: var(--color-text-tertiary);
  --components-label-letter-spacing: 0.20000000298023224px;
  --components-label-font-weight: var(--font-weight-semi-bold);
  --components-label-required-symbol-disabled-color-text: var(
    --components-label-disabled-color-text
  );
  --components-label-description-font-weight: var(--font-weight-regular);
  --components-label-required-symbol-color-text: var(--color-color);
  --components-label-required-symbol-font-size: var(
    --components-label-font-size
  );
  --components-label-required-symbol-font-weight: var(
    --components-label-font-weight
  );
  --components-label-description-font-size: var(--font-size-body-xs);
  --components-button-padding-horizontal: var(--spacing-150);
  --components-button-height: var(--spacing-400);
  --components-button-border-radius: var(--border-radius-rounded-more);
  --components-button-border-width: var(--border-width-small);
  --components-button-gap: var(--spacing-025);
  --components-button-icon-size: var(--icon-size-s);
  --components-button-font-size: var(--font-size-body-m);
  --components-button-small-padding-horizontal: var(--spacing-100);
  --components-button-small-border-radius: var(--border-radius-rounded);
  --components-button-small-height: var(--spacing-300);
  --components-button-small-gap: var(--spacing-025);
  --components-button-small-icon-size: var(--icon-size-s);
  --components-button-small-font-size: var(--font-size-body-s);
  --components-button-primary-border-width: var(--border-width-small);
  --components-button-primary-color-background: var(--color-clickable);
  --components-button-primary-color-border: var(
    --components-button-primary-color-background
  );
  --components-button-primary-color-text: var(
    --primitives-color-palette-onPrimary
  );
  --components-button-primary-hover-color-background: var(
    --color-clickable-hover
  );
  --components-button-primary-hover-color-border: var(
    --components-button-primary-hover-color-background
  );
  --components-button-primary-hover-color-text: var(
    --primitives-color-palette-onSecondary
  );
  --components-button-primary-disabled-color-background: var(--color-disabled);
  --components-button-primary-disabled-color-border: var(
    --components-button-primary-disabled-color-background
  );
  --components-button-primary-disabled-color-text: var(
    --primitives-color-palette-onSecondary
  );
  --components-button-secondary-border-width: var(--border-width-small);
  --components-button-secondary-color-background: var(--color-invert);
  --components-button-secondary-color-border: var(--color-border);
  --components-button-secondary-color-text: var(--color-clickable);
  --components-button-secondary-hover-color-background: var(--color-highlight);
  --components-button-secondary-hover-color-border: var(
    --components-button-secondary-hover-color-background
  );
  --components-button-secondary-hover-color-text: var(
    --components-button-secondary-color-text
  );
  --components-button-secondary-disabled-color-background: var(--color-invert);
  --components-button-secondary-disabled-color-border: var(--color-disabled);
  --components-button-secondary-disabled-color-text: var(--color-disabled);
  --components-button-text-border-width: var(--border-width-none);
  --components-button-text-color-text: var(--color-clickable);
  --components-button-text-hover-color-text: var(--color-clickable-hover);
  --components-button-text-disabled-color-text: var(--color-disabled);
  --components-button-primary-danger-color-background: var(
    --components-pill-prominent-danger-color-indicator
  );
  --components-button-primary-danger-color-border: var(
    --components-button-primary-danger-color-background
  );
  --components-button-primary-danger-color-text: var(
    --color-text-invert-primary
  );
  --components-button-secondary-danger-color-background: var(--color-invert);
  --components-button-secondary-danger-color-border: var(
    --components-button-secondary-color-border
  );
  --components-button-secondary-danger-color-text: var(
    --components-pill-prominent-danger-color-indicator
  );
  --components-button-text-danger-color-text: var(
    --components-button-secondary-danger-color-text
  );
  --components-button-primary-warning-color-background: var(
    --components-pill-prominent-warning-color-indicator
  );
  --components-button-primary-warning-color-border: var(
    --components-button-primary-warning-color-background
  );
  --components-button-primary-warning-color-text: var(
    --color-text-invert-primary
  );
  --components-button-secondary-warning-color-background: var(--color-invert);
  --components-button-secondary-warning-color-border: var(
    --components-button-secondary-color-border
  );
  --components-button-secondary-warning-color-text: var(
    --components-pill-prominent-warning-color-indicator
  );
  --components-button-text-warning-color-text: var(
    --components-button-secondary-warning-color-text
  );
  --components-button-text-success-color-text: var(
    --components-button-secondary-success-color-text
  );
  --components-button-secondary-success-color-background: var(--color-invert);
  --components-button-secondary-success-color-border: var(
    --components-button-secondary-color-border
  );
  --components-button-secondary-success-color-text: var(
    --components-pill-prominent-success-color-text
  );
  --components-button-primary-success-color-background: var(
    --components-pill-prominent-success-color-indicator
  );
  --components-button-primary-success-color-border: var(
    --components-button-primary-success-color-background
  );
  --components-button-primary-success-color-text: var(
    --color-text-invert-primary
  );
  --components-tooltip-padding-vertical: var(--spacing-025);
  --components-tooltip-padding-horizontal: var(--spacing-050);
  --components-tooltip-border-radius: var(
    --border-radius-always-rounded-slightly
  );
  --components-tooltip-gap: var(--spacing-025);
  --components-tooltip-color-background: var(
    --color-surface-invert-secondary-overlay
  );
  --components-tooltip-color-text: var(--color-text-invert-primary);
  --components-tooltip-font-weight: var(--font-weight-regular);
  --components-tooltip-font-size: var(--font-size-body-s);
  --components-avatar-color-1: #546df2;
  --components-avatar-color-on1: var(--color-text-invert-primary);
  --components-avatar-color-2: #f658a4;
  --components-avatar-color-on2: var(--color-text-invert-primary);
  --components-avatar-color-3: #4491ec;
  --components-avatar-color-on3: var(--color-text-invert-primary);
  --components-avatar-color-4: var(--primitives-color-palette-graph-1-dark);
  --components-avatar-color-on4: var(--color-text-invert-primary);
  --components-avatar-color-5: #f18420;
  --components-avatar-color-on5: #ffffff;
  --components-avatar-xs-size: var(--spacing-200);
  --components-avatar-xs-font-size: var(--spacing-100);
  --components-avatar-s-size: var(--spacing-300);
  --components-avatar-s-font-size: var(--spacing-150);
  --components-avatar-m-size: var(--spacing-400);
  --components-avatar-m-font-size: var(--spacing-200);
  --components-avatar-l-size: var(--spacing-500);
  --components-avatar-l-font-size: var(--spacing-250);
  --components-avatar-xl-size: 96px;
  --components-avatar-xl-font-size: var(--spacing-600);
  --components-search-bar-color-background: var(--color-surface-secondary);
  --components-search-bar-color-border: var(--color-border);
  --components-dropdown-menu-color-background: var(
    --components-select-color-background
  );
  --components-dropdown-menu-item-color-background: var(
    --components-select-option-color-background
  );
  --components-dropdown-menu-item-color-text: var(
    --components-select-option-color-text
  );
  --components-dropdown-menu-item-color-description-text: var(
    --components-select-option-color-description-text
  );
  --components-dropdown-menu-item-border-radius: var(
    --components-select-option-border-radius
  );
  --components-dropdown-menu-item-padding-vertical: var(
    --components-select-option-padding-vertical
  );
  --components-dropdown-menu-item-padding-horizontal: var(
    --components-select-option-padding-horizontal
  );
  --components-dropdown-menu-color-border: var(
    --components-select-color-border
  );
  --components-dropdown-menu-border-width: var(
    --components-select-border-width
  );
  --components-dropdown-menu-border-radius: var(
    --components-select-border-radius
  );
  --components-dropdown-menu-gap-color-border: var(
    --components-select-gap-color-border
  );
  --components-dropdown-menu-gap-border-width: var(
    --components-select-gap-border-width
  );
  --components-dropdown-menu-margin-top: var(--components-select-margin-top);
  --components-dropdown-menu-max-height: var(--components-select-max-height);
  --components-dropdown-menu-item-hover-color-background: var(
    --components-select-option-hover-color-background
  );
  --components-dropdown-menu-item-hover-color-text: var(
    --components-select-option-hover-color-text
  );
  --components-dropdown-menu-item-hover-color-description-text: var(
    --components-select-option-hover-color-description-text
  );
  --components-pagination-padding-vertical: var(--spacing-025);
  --components-pagination-number-width: var(--spacing-400);
  --components-pagination-padding-horizontal: var(--spacing-025);
  --components-pagination-number-height: var(
    --components-pagination-number-width
  );
  --components-pagination-number-font-size: var(--font-size-body-m);
  --components-pagination-number-color-text: var(--color-text-secondary);
  --components-pagination-color-spacer: var(--color-text-tertiary);
  --components-pagination-number-hover-color-text: var(
    --components-select-option-hover-color-text
  );
  --components-pagination-number-hover-font-weight: var(--font-weight-regular);
  --components-pagination-number-hover-color-border: var(--color-transparent);
  --components-pagination-number-hover-color-background: var(
    --components-select-option-hover-color-background
  );
  --components-pagination-color-icon: var(--components-button-text-color-text);
  --components-pagination-number-active-color-text: var(
    --components-select-option-selected-color-text
  );
  --components-pagination-number-active-font-weight: var(--font-weight-regular);
  --components-pagination-number-active-color-border: var(--color-transparent);
  --components-pagination-spacer-font-size: 16px;
  --components-pagination-number-active-color-background: var(
    --primitives-color-palette-colorLighter
  );
  --components-pagination-gap: var(--spacing-100);
  --components-pagination-numbers-gap: var(--spacing-000);
  --components-pagination-number-font-weight: var(--font-weight-regular);
  --components-pagination-number-border-radius: var(
    --components-select-border-radius
  );
  --components-pagination-number-border-width: var(--border-width-none);
  --components-pagination-number-color-border: var(--color-transparent);
  --components-pagination-number-color-background: var(--color-transparent);
  --components-pagination-number-active-hover-color-text: var(
    --components-select-option-selected-hover-color-text
  );
  --components-pagination-number-active-hover-font-weight: var(
    --font-weight-regular
  );
  --components-pagination-number-active-hover-color-border: var(
    --color-transparent
  );
  --components-pagination-number-active-hover-color-background: var(
    --components-select-option-selected-hover-color-background
  );
  --components-inputs-border-width-left: var(--border-width-small);
  --components-inputs-border-width-top: var(
    --components-inputs-border-width-left
  );
  --components-inputs-border-width-right: var(
    --components-inputs-border-width-left
  );
  --components-inputs-border-width-bottom: var(
    --components-inputs-border-width-left
  );
  --components-inputs-placeholder-color-text: var(--color-text-disabled);
  --components-inputs-value-color-text: var(--color-text-primary);
  --components-inputs-value-font-size: var(--font-size-body-m);
  --components-inputs-placeholder-font-size: var(--font-size-body-m);
  --components-inputs-border-radius: var(--border-radius-rounded);
  --components-page-body-template-padding-vertical: var(--spacing-200);
  --components-page-body-template-padding-horizontal: var(--spacing-300);
  --components-page-body-template-gap: var(--spacing-200);
  --components-page-body-template-heading-gap: var(--spacing-050);
  --components-button-group-gap: var(--spacing-150);
  --components-input-group-gap: var(--spacing-150);
  --components-inputs-color-background: var(--color-surface-secondary);
  --components-inputs-color-border: var(--color-border);
  --components-inputs-focus-border-width-left: var(--border-width-small);
  --components-inputs-focus-border-width-top: var(
    --components-inputs-focus-border-width-left
  );
  --components-inputs-focus-border-width-right: var(
    --components-inputs-focus-border-width-left
  );
  --components-inputs-focus-border-width-bottom: var(
    --components-inputs-focus-border-width-left
  );
  --components-inputs-focus-color-background: var(
    --components-inputs-color-background
  );
  --components-inputs-height: var(--spacing-400);
  --components-inputs-padding-horizontal: var(--spacing-050);
  --components-inputs-padding-vertical: var(--spacing-050);
  --components-inputs-gap: var(--spacing-025);
  --components-inputs-icon-size: var(--icon-size-s);
  --components-inputs-focus-color-border: var(--color-focus);
  --components-inputs-color-icon: var(--color-text-tertiary);
  --components-inputs-hover-color-background: var(
    --components-inputs-color-background
  );
  --components-inputs-hover-color-border: var(--components-inputs-color-border);
  --components-inputs-error-color-background: var(
    --components-inputs-color-background
  );
  --components-inputs-error-color-border: var(--color-traffic-danger);
  --components-inputs-hover-border-width-left: var(
    --components-inputs-border-width-left
  );
  --components-inputs-error-color-value: var(--color-traffic-onDanger);
  --components-inputs-hover-border-width-top: var(
    --components-inputs-border-width-top
  );
  --components-inputs-hover-border-width-right: var(
    --components-inputs-border-width-right
  );
  --components-inputs-hover-border-width-bottom: var(
    --components-inputs-border-width-bottom
  );
  --components-inputs-error-border-width-left: var(
    --components-inputs-border-width-left
  );
  --components-inputs-error-border-width-top: var(
    --components-inputs-border-width-top
  );
  --components-inputs-error-border-width-right: var(
    --components-inputs-border-width-right
  );
  --components-inputs-error-border-width-bottom: var(
    --components-inputs-border-width-bottom
  );
  --components-inputs-disabled-color-background: var(--color-disabledSurface);
  --components-inputs-disabled-color-border: var(--color-border);
  --components-inputs-disabled-color-value: var(--color-onDisabled);
  --components-inputs-disabled-border-width-left: var(
    --components-inputs-border-width-left
  );
  --components-inputs-disabled-border-width-top: var(
    --components-inputs-border-width-top
  );
  --components-inputs-disabled-border-width-right: var(
    --components-inputs-border-width-right
  );
  --components-inputs-disabled-border-width-bottom: var(
    --components-inputs-border-width-bottom
  );
  --components-inputs-toggle-on-color-background: var(--color-primary);
  --components-inputs-toggle-on-color-border: var(
    --components-inputs-checkbox-on-color-background
  );
  --components-inputs-toggle-on-color-icon: var(--color-onPrimary);
  --components-inputs-checkbox-border-width: var(--border-width-small);
  --components-inputs-checkbox-border-radius: var(
    --border-radius-rounded-slightly
  );
  --components-inputs-checkbox-on-color-background: var(--color-primary);
  --components-inputs-checkbox-size: var(--spacing-200);
  --components-inputs-checkbox-on-color-border: var(
    --components-inputs-checkbox-on-color-background
  );
  --components-inputs-checkbox-on-color-icon: var(--color-onPrimary);
  --components-inputs-checkbox-on-disabled-color-background: var(
    --color-disabled
  );
  --components-inputs-checkbox-on-disabled-color-border: var(
    --components-inputs-checkbox-on-disabled-color-background
  );
  --components-inputs-checkbox-on-disabled-color-icon: var(
    --color-disabledSurface
  );
  --components-inputs-checkbox-off-color-background: var(
    --components-inputs-color-background
  );
  --components-inputs-checkbox-off-color-border: var(
    --primitives-color-palette-grey-350
  );
  --components-inputs-checkbox-off-disabled-color-background: var(
    --color-disabledSurface
  );
  --components-inputs-checkbox-off-disabled-color-border: var(--color-disabled);
  --components-inputs-toggle-border-width: var(--border-width-small);
  --components-inputs-toggle-border-radius: var(--spacing-300);
  --components-inputs-toggle-off-color-background: var(
    --primitives-color-palette-grey-400
  );
  --components-inputs-toggle-off-color-border: var(
    --components-inputs-toggle-off-color-background
  );
  --components-inputs-toggle-on-disabled-color-background: var(
    --color-disabled
  );
  --components-inputs-toggle-off-color-icon: var(
    --components-inputs-toggle-on-color-icon
  );
  --components-inputs-toggle-on-disabled-color-border: var(
    --components-inputs-checkbox-on-disabled-color-background
  );
  --components-inputs-toggle-height: var(--components-inputs-checkbox-size);
  --components-inputs-toggle-on-disabled-color-icon: var(
    --color-disabledSurface
  );
  --components-inputs-toggle-width: var(--spacing-350);
  --components-inputs-radio-border-width: var(--border-width-small);
  --components-inputs-radio-border-radius: var(--spacing-300);
  --components-inputs-radio-size: var(--components-inputs-checkbox-size);
  --components-inputs-toggle-off-disabled-color-background: var(
    --color-disabledSurface
  );
  --components-inputs-toggle-off-disabled-color-border: var(--color-disabled);
  --components-inputs-toggle-off-disabled-color-icon: var(--color-disabled);
  --components-inputs-radio-on-color-background: var(--color-onPrimary);
  --components-inputs-radio-on-color-border: var(
    --components-inputs-radio-on-color-icon
  );
  --components-inputs-radio-on-color-icon: var(--color-primary);
  --components-inputs-radio-off-color-background: var(
    --components-inputs-color-background
  );
  --components-inputs-radio-off-color-border: var(
    --components-inputs-toggle-off-color-background
  );
  --components-inputs-radio-on-disabled-color-background: var(
    --color-disabledSurface
  );
  --components-inputs-radio-on-disabled-color-border: var(
    --components-inputs-checkbox-on-disabled-color-background
  );
  --components-inputs-radio-on-disabled-color-icon: var(
    --components-inputs-radio-on-disabled-color-border
  );
  --components-inputs-radio-off-disabled-color-background: var(
    --color-disabledSurface
  );
  --components-inputs-radio-off-disabled-color-border: var(--color-disabled);
  --components-alert-prominent-info-color-background: var(
    --primitives-color-palette-traffic-info-light
  );
  --components-alert-prominent-info-color-border: var(
    --components-pill-prominent-info-color-background
  );
  --components-alert-prominent-info-color-indicator: var(
    --primitives-color-palette-traffic-info-mid
  );
  --components-alert-prominent-info-color-text: var(
    --primitives-color-palette-traffic-info-dark
  );
  --components-alert-prominent-success-color-background: var(
    --primitives-color-palette-traffic-success-light
  );
  --components-alert-prominent-success-color-border: var(
    --components-pill-prominent-success-color-background
  );
  --components-alert-prominent-success-color-indicator: var(
    --primitives-color-palette-traffic-success-mid
  );
  --components-alert-prominent-success-color-text: var(
    --primitives-color-palette-traffic-success-dark
  );
  --components-alert-prominent-warning-color-background: var(
    --primitives-color-palette-traffic-warning-light
  );
  --components-alert-prominent-warning-color-border: var(
    --components-pill-prominent-warning-color-background
  );
  --components-alert-prominent-warning-color-indicator: var(
    --primitives-color-palette-traffic-warning-mid
  );
  --components-alert-prominent-warning-color-text: var(
    --primitives-color-palette-traffic-warning-dark
  );
  --components-alert-prominent-danger-color-background: var(
    --primitives-color-palette-traffic-danger-light
  );
  --components-alert-prominent-danger-color-border: var(
    --components-pill-prominent-danger-color-background
  );
  --components-alert-prominent-danger-color-indicator: var(
    --primitives-color-palette-traffic-danger-mid
  );
  --components-alert-prominent-danger-color-text: var(
    --primitives-color-palette-traffic-danger-dark
  );
  --components-alert-strong-info-color-background: var(
    --components-pill-prominent-info-color-indicator
  );
  --components-alert-strong-info-color-border: var(
    --components-pill-strong-info-color-background
  );
  --components-alert-strong-info-color-indicator: var(
    --color-text-invert-primary
  );
  --components-alert-strong-info-color-text: var(--color-text-invert-primary);
  --components-alert-strong-success-color-background: var(
    --components-pill-prominent-success-color-indicator
  );
  --components-alert-strong-success-color-border: var(
    --components-pill-strong-success-color-background
  );
  --components-alert-strong-success-color-indicator: var(
    --color-text-invert-primary
  );
  --components-alert-strong-success-color-text: var(
    --color-text-invert-primary
  );
  --components-alert-strong-warning-color-background: var(
    --components-pill-prominent-warning-color-indicator
  );
  --components-alert-strong-warning-color-border: var(
    --components-pill-strong-warning-color-background
  );
  --components-alert-strong-warning-color-indicator: var(
    --color-text-invert-primary
  );
  --components-alert-strong-warning-color-text: var(
    --color-text-invert-primary
  );
  --components-alert-strong-danger-color-background: var(
    --components-pill-prominent-danger-color-indicator
  );
  --components-alert-strong-danger-color-border: var(
    --components-pill-strong-danger-color-background
  );
  --components-alert-strong-danger-color-indicator: var(
    --color-text-invert-primary
  );
  --components-alert-strong-danger-color-text: var(--color-text-invert-primary);
  --components-alert-subtle-color-background: var(
    --components-pill-prominent-neutral-color-background
  );
  --components-alert-subtle-color-border: var(
    --components-pill-subtle-color-background
  );
  --components-alert-subtle-color-text: var(--color-text-secondary);
  --components-alert-transparent-color-background: var(--color-transparent);
  --components-alert-transparent-color-border: var(
    --components-pill-transparent-color-background
  );
  --components-alert-transparent-color-text: var(
    --components-pill-prominent-neutral-color-text
  );
  --components-alert-icon-only-color-background: var(--color-transparent);
  --components-alert-icon-only-color-border: var(
    --components-pill-icon-only-color-background
  );
  --components-alert-icon-only-color-text: var(
    --components-pill-prominent-neutral-color-text
  );
  --components-table-header-color-text: var(--color-text-secondary);
  --components-table-header-font-family: var(--font-family);
  --components-table-header-font-size: var(--font-size-body-s);
  --components-table-header-font-weight: var(--font-weight-semi-bold);
  --components-table-row-border-bottom-width: var(--border-width-small);
  --components-table-cell-padding-horizontal: var(--spacing-075);
  --components-table-cell-padding-vertical: var(
    --components-table-cell-padding-horizontal
  );
  --components-table-row-color-border: var(--color-border);
  --components-table-row-color-background: var(--color-transparent);
  --components-table-row-color-text: var(--color-text-primary);
  --components-table-row-font-size: var(--font-size-body-m);
  --components-table-row-font-weight: var(--font-weight-regular);
  --components-table-row-hover-color-background: var(
    --components-select-option-hover-color-background
  );
  --components-table-row-hover-color-text: var(
    --components-select-option-hover-color-text
  );
  --components-table-row-selected-color-background: var(
    --components-select-option-selected-color-background
  );
  --components-table-row-selected-color-text: var(
    --components-select-option-selected-color-text
  );
  --components-navigation-top-item-font-weight: var(--font-weight-medium);
  --components-navigation-top-item-hover-color-text: var(
    --components-select-option-hover-color-text
  );
  --components-navigation-top-item-color-text: var(
    --components-select-option-color-text
  );
  --components-navigation-top-item-selected-color-text: var(
    --components-select-option-selected-color-text
  );
  --components-navigation-top-item-selected-hover-color-text: var(
    --components-select-option-selected-hover-color-text
  );
  --components-navigation-top-item-background-border-radius: var(
    --border-radius-rounded
  );
  --components-navigation-top-item-background-padding-top: var(--spacing-100);
  --components-navigation-top-item-background-padding-bottom: var(
    --components-navigation-top-item-background-padding-top
  );
  --components-navigation-top-item-background-padding-left: var(--spacing-050);
  --components-navigation-top-item-background-padding-right: var(
    --components-navigation-top-item-background-padding-left
  );
  --components-navigation-top-item-background-color-background: var(
    --color-transparent
  );
  --components-navigation-top-item-background-hover-color-background: var(
    --components-select-option-hover-color-background
  );
  --components-navigation-top-item-background-selected-color-background: var(
    --components-select-option-selected-color-background
  );
  --components-navigation-top-item-background-selected-hover-color-background: var(
    --components-select-option-selected-hover-color-background
  );
  --components-navigation-top-item-font-family: var(--font-family-heading);
  --components-navigation-side-padding-horizontal: var(--spacing-100);
  --components-navigation-side-padding-vertical: var(--spacing-100);
  --components-navigation-side-gap: var(--spacing-100);
  --components-navigation-side-width: 212px;
  --components-navigation-side-collapsed-width: 56px;
  --components-navigation-side-item-height: var(--spacing-400);
  --components-navigation-side-color-background: var(--color-surface-secondary);
  --components-navigation-side-color-shadow: #09142629;
  --components-navigation-top-color-shadow: #09142600;
  --components-navigation-side-color-border: var(
      --primitives-color-palette-grey-150
    )
    --components-navigation-side-border-width: var(--border-width-small);
  --components-navigation-side-item-group-gap: var(--spacing-100);
  --components-navigation-side-item-group-padding-vertical: var(--spacing-000);
  --components-navigation-side-item-group-padding-horizontal: var(
    --spacing-000
  );
  --components-navigation-side-item-padding-horizontal: var(--spacing-100);
  --components-navigation-side-item-font-size: var(--font-size-body-m);
  --components-navigation-side-item-font-weight: var(--font-weight-medium);
  --components-navigation-side-item-hover-color-text: var(
    --components-select-option-hover-color-text
  );
  --components-navigation-side-item-selected-font-weight: var(
    --components-navigation-side-item-font-weight
  );
  --components-navigation-top-item-selected-font-weight: var(
    --font-weight-medium
  );
  --components-navigation-side-item-selected-color-text: var(
    --components-select-option-selected-color-text
  );
  --components-navigation-side-item-color-text: var(
    --components-select-option-color-text
  );
  --components-navigation-side-item-selected-hover-color-text: var(
    --components-select-option-selected-hover-color-text
  );
  --components-navigation-side-item-background-border-radius: var(
    --border-radius-rounded
  );
  --components-navigation-side-item-background-padding-top: var(--spacing-000);
  --components-navigation-side-item-background-padding-bottom: var(
    --components-navigation-side-item-background-padding-top
  );
  --components-navigation-side-item-background-padding-left: var(--spacing-000);
  --components-navigation-side-item-background-padding-right: var(
    --components-navigation-side-item-background-padding-left
  );
  --components-navigation-side-item-background-color-background: var(
    --color-transparent
  );
  --components-navigation-side-item-background-hover-color-background: var(
    --components-select-option-hover-color-background
  );
  --components-navigation-side-item-background-selected-color-background: var(
    --components-select-option-selected-color-background
  );
  --components-navigation-side-item-background-selected-hover-color-background: var(
    --components-select-option-selected-hover-color-background
  );
  --components-navigation-side-item-font-family: var(--font-family);
  --components-navigation-side-item-gap: var(
    --components-navigation-side-item-padding-horizontal
  );
  --components-dropdown-menu-item-selected-color-background: var(
    --components-select-option-selected-color-background
  );
  --components-dropdown-menu-item-selected-color-text: var(
    --components-select-option-selected-color-text
  );
  --components-dropdown-menu-item-selected-color-description-text: var(
    --components-select-option-selected-color-description-text
  );
  --components-dropdown-menu-item-selected-hover-color-background: var(
    --components-select-option-selected-hover-color-background
  );
  --components-dropdown-menu-item-selected-hover-color-text: var(
    --components-select-option-selected-hover-color-text
  );
  --components-dropdown-menu-item-selected-hover-color-description-text: var(
    --components-select-option-selected-hover-color-description-text
  );
  --components-floating-content-panel-padding-vertical: var(--spacing-200);
  --components-floating-content-panel-padding-horizontal: var(--spacing-200);
  --components-floating-content-panel-trigger-gap: var(--spacing-050);
  --components-floating-content-panel-border-radius: var(
    --border-radius-rounded-more
  );
  --components-floating-content-panel-color-border: var(--color-border);
  --components-floating-content-panel-color-background: var(
    --color-surface-secondary-overlay
  );
  --components-floating-content-panel-border-width: var(--border-width-small);
  --components-whiteboard-tool-color-background: var(--color-surface-secondary);
  --components-whiteboard-tool-color-border: var(--color-border-primary);
  --components-whiteboard-tool-color-icon: var(--color-text-primary);
  --components-whiteboard-tool-border-width: var(--border-width-small);
  --components-whiteboard-tool-border-radius: var(
    --border-radius-always-rounded
  );
  --components-whiteboard-tool-rounded-border-radius: var(
    --border-radius-always-rounded-full
  );
  --components-whiteboard-tool-size: var(--spacing-400);
  --components-whiteboard-block-color-background: var(
    --color-surface-secondary
  );
  --components-whiteboard-line-color-background: var(--color-border-primary);
  --components-whiteboard-line-border-width: var(--border-width-medium);
  --components-whiteboard-line-border-radius: var(--border-radius-rounded-more);
  --components-whiteboard-line-hover-color-background: var(--color-color);
  --components-whiteboard-line-selected-color-background: var(--color-color);
  --components-whiteboard-block-color-border: var(--color-border-primary);
  --components-whiteboard-block-border-width: var(--border-width-small);
  --components-whiteboard-block-border-radius: var(
    --border-radius-rounded-more
  );
  --components-whiteboard-block-hover-color-background: var(
    --components-whiteboard-block-color-background
  );
  --components-whiteboard-block-hover-color-border: var(--color-color);
  --components-whiteboard-block-selected-color-background: var(
    --color-colorLightest
  );
  --components-whiteboard-block-selected-color-border: var(--color-color);
  --components-whiteboard-block-selected-hover-color-background: var(
    --color-colorLightest
  );
  --components-whiteboard-block-selected-hover-color-border: var(--color-color);
  --components-whiteboard-block-disabled-color-background: var(
    --color-disabledSurface
  );
  --components-whiteboard-block-disabled-color-border: var(
    --components-whiteboard-block-color-border
  );
  --components-whiteboard-add-button-color-background: var(
    --components-whiteboard-line-color-background
  );
  --components-whiteboard-sticky-note-color-1: var(
    --primitives-color-palette-sticky-notes-default
  );
  --components-whiteboard-sticky-note-color-1fold: var(
    --primitives-color-palette-sticky-notes-default
  );
  --components-whiteboard-sticky-note-color-on1: var(--color-text-primary);
  --components-whiteboard-sticky-note-color-2: var(
    --primitives-color-palette-sticky-notes-blue
  );
  --components-whiteboard-sticky-note-color-on2: var(--color-text-primary);
  --components-whiteboard-sticky-note-color-3: var(
    --primitives-color-palette-sticky-notes-green
  );
  --components-whiteboard-sticky-note-color-on3: var(--color-text-primary);
  --components-whiteboard-sticky-note-color-4: var(
    --primitives-color-palette-sticky-notes-pink
  );
  --components-whiteboard-sticky-note-color-on4: var(--color-text-primary);
  --components-whiteboard-sticky-note-color-5: var(
    --primitives-color-palette-sticky-notes-grey
  );
  --components-whiteboard-sticky-note-color-on5: var(--color-text-primary);
  --components-whiteboard-add-button-color-border: var(
    --components-whiteboard-add-button-color-background
  );
  --components-whiteboard-add-button-color-icon: var(
    --components-whiteboard-add-button-color-background
  );
  --components-whiteboard-add-button-size: var(--spacing-250);
  --components-whiteboard-add-button-border-width: var(--border-width-medium);
  --components-whiteboard-add-button-border-radius: var(
    --border-radius-always-rounded-full
  );
  --components-whiteboard-sticky-note-width: 160px;
  --components-whiteboard-add-button-hover-color-background: var(
    --components-whiteboard-block-selected-color-background
  );
  --components-whiteboard-toolbar-padding-vertical: var(--spacing-100);
  --components-whiteboard-toolbar-padding-horizontal: var(--spacing-100);
  --components-whiteboard-toolbar-gap: var(--spacing-100);
  --components-whiteboard-toolbar-color-background: var(
    --color-surface-secondary-overlay
  );
  --components-whiteboard-toolbar-color-border: var(--color-border-primary);
  --components-whiteboard-toolbar-border-width: var(--border-width-small);
  --components-whiteboard-toolbar-mode-border-radius: var(
    --border-radius-always-rounded
  );
  --components-whiteboard-sticky-note-height: 160px;
  --components-whiteboard-sticky-note-font-size: var(--font-size-body-s);
  --components-whiteboard-add-button-hover-color-border: var(
    --components-whiteboard-block-selected-color-border
  );
  --components-whiteboard-add-button-selected-color-background: var(
    --components-whiteboard-add-button-hover-color-background
  );
  --components-whiteboard-add-button-hover-color-icon: var(
    --components-whiteboard-add-button-hover-color-border
  );
  --components-whiteboard-add-button-selected-color-border: var(
    --components-whiteboard-add-button-hover-color-border
  );
  --components-whiteboard-add-button-selected-color-icon: var(
    --components-whiteboard-add-button-selected-color-border
  );
  --components-whiteboard-tool-hover-color-background: var(
    --components-whiteboard-block-color-background
  );
  --components-whiteboard-tool-hover-color-border: var(--color-color);
  --components-whiteboard-tool-hover-color-icon: var(--color-color);
  --components-whiteboard-tool-selected-color-background: var(
    --color-colorLightest
  );
  --components-whiteboard-tool-selected-color-border: var(--color-color);
  --components-whiteboard-tool-selected-color-icon: var(--color-color);
  --components-whiteboard-tool-selected-hover-color-background: var(
    --color-colorLightest
  );
  --components-whiteboard-tool-selected-hover-color-border: var(--color-color);
  --components-whiteboard-tool-selected-hover-color-icon: var(--color-color);
  --components-whiteboard-toolbar-sticky-note-padding-vertical: var(
    --spacing-075
  );
  --components-whiteboard-toolbar-sticky-note-padding-horizontal: var(
    --spacing-100
  );
  --components-whiteboard-toolbar-sticky-note-gap: var(--spacing-100);
  --components-whiteboard-toolbar-sticky-note-color-background: var(
    --color-surface-secondary-overlay
  );
  --components-whiteboard-toolbar-sticky-note-color-border: var(
    --color-border-primary
  );
  --components-whiteboard-toolbar-sticky-note-border-width: var(
    --border-width-small
  );
  --components-whiteboard-toolbar-border-radius: var(
    --border-radius-always-rounded-full
  );
  --components-whiteboard-toolbar-zoom-padding-vertical: var(--spacing-075);
  --components-whiteboard-toolbar-zoom-padding-horizontal: var(--spacing-100);
  --components-whiteboard-toolbar-zoom-gap: var(--spacing-100);
  --components-whiteboard-toolbar-zoom-color-background: var(
    --color-surface-secondary-overlay
  );
  --components-whiteboard-toolbar-zoom-color-border: var(
    --color-border-primary
  );
  --components-whiteboard-toolbar-zoom-border-width: var(--border-width-small);
  --components-whiteboard-toolbar-zoom-border-radius: var(
    --border-radius-rounded-full
  );
  --components-status-line-width: var(--spacing-075);
  --components-status-circle-pending-color-icon: var(--color-onDisabled);
  --components-status-circle-pending-color-border: var(
    --components-status-circle-pending-color-icon
  );
  --components-status-circle-pending-color-background: var(
    --color-disabledSurface
  );
  --components-status-circle-loading-color-icon: var(--color-border);
  --components-status-circle-loading-color-border: var(
    --components-status-circle-color-border
  );
  --components-status-circle-loading-color-background: var(
    --components-status-circle-color-background
  );
  --components-status-circle-comment-color-icon: var(--color-colorLight);
  --components-status-circle-comment-color-border: var(
    --components-status-circle-color-border
  );
  --components-status-circle-comment-color-background: var(
    --components-status-circle-color-background
  );
  --components-toast-notification-info-color-indicator: var(
    --primitives-color-palette-traffic-info-mid
  );
  --components-toast-notification-success-color-indicator: var(
    --primitives-color-palette-traffic-success-mid
  );
  --components-toast-notification-warning-color-indicator: var(
    --primitives-color-palette-traffic-warning-mid
  );
  --components-toast-notification-danger-color-indicator: var(
    --primitives-color-palette-traffic-danger-mid
  );
  --components-toast-notification-gap: var(--spacing-100);
  --components-toast-notification-padding-vertical: var(--spacing-100);
  --components-toast-notification-padding-horizontal: var(
    --components-toast-notification-padding-vertical
  );
  --components-toast-notification-border-radius: var(--border-radius-rounded);
  --components-toast-notification-border-width: var(--border-width-none);
  --components-toast-notification-font-size: var(--font-size-body-s);
  --components-toast-notification-icon-size: var(--icon-size-l);
  --components-toast-notification-font-weight: var(--font-weight-regular);
  --components-toast-notification-heading-font-size: var(--font-size-body-m);
  --components-toast-notification-heading-font-weight: var(
    --font-weight-medium
  );
  --components-toast-notification-color-background: var(
    --color-surface-secondary-overlay
  );
  --components-toast-notification-color-border: var(--color-border-primary);
  --components-toast-notification-color-heading: var(--color-text-primary);
  --components-toast-notification-color-text: var(--color-text-secondary);
  --components-toast-notification-color-icon: var(--color-invert);
  --components-toast-notification-border-radius-icon: var(
    --border-radius-rounded-slightly
  );
  --components-badge-info-color-background: var(--color-traffic-info);
  --components-badge-warning-color-background: var(--color-traffic-warning);
  --components-badge-success-color-background: var(--color-traffic-success);
  --components-badge-danger-color-background: var(--color-traffic-danger);
  --components-label-error-font-weight: var(--font-weight-regular);
  --components-label-error-font-size: var(--font-size-body-s);
  --components-button-primary-info-color-background: var(
    --components-pill-prominent-info-color-indicator
  );
  --components-button-primary-info-color-border: var(
    --components-button-primary-info-color-background
  );
  --components-button-primary-info-color-text: var(--color-text-invert-primary);
  --components-button-secondary-info-color-background: var(--color-invert);
  --components-button-secondary-info-color-border: var(
    --components-button-secondary-color-border
  );
  --components-button-secondary-info-color-text: var(
    --components-pill-prominent-info-color-text
  );
  --components-button-text-info-color-text: var(
    --components-button-secondary-info-color-text
  );
}
