import react from "@vitejs/plugin-react-swc";
import fs from "fs";
import path from "path";
import { defineConfig, loadEnv } from "vite";
import dts from "vite-plugin-dts";
import viteTsConfigPaths from "vite-tsconfig-paths";
import { coverageConfigDefaults } from "vitest/config";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const ENV = loadEnv(mode, process.cwd());
  const themeFile = ENV.VITE_APP_THEME === "otai" ? "otai.css" : "oneteam.css";
  return {
    resolve: {
      alias: {
        "@theme": path.resolve(__dirname, "src/tokens/stylesheets", themeFile)
      }
    },
    server: {
      open: true,
      port: 3000
    },
    plugins: [
      react(),
      viteTsConfigPaths(),
      dts({ rollupTypes: true }),
      {
        // This plugin writes the theme info to a file
        name: "write-theme-info",
        writeBundle() {
          const outputFilePath = path.resolve(
            __dirname,
            "dist",
            "theme-info.json"
          );
          fs.writeFileSync(
            outputFilePath,
            JSON.stringify({ themeFile }, null, 2)
          );
        }
      }
    ],
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler" // or 'modern'
        }
      }
    },
    build: {
      lib: {
        entry: path.resolve(__dirname, "src/index.ts"),
        name: "OneTheme",
        fileName: "index"
      },
      sourcemap: true,
      rollupOptions: {
        // make sure to externalize deps that shouldn't be bundled
        // into your library
        external: [
          "react",
          "react-hook-form",
          "@atlaskit/pragmatic-drag-and-drop",
          "@atlaskit/pragmatic-drag-and-drop-hitbox",
          "@monaco-editor/react"
        ],
        output: {
          // Provide global variables to use in the UMD build
          // for externalized deps
          globals: {
            react: "React",
            "react-hook-form": "ReactHookForm",
            "@atlaskit/pragmatic-drag-and-drop": "PragmaticDragAndDrop",
            "@atlaskit/pragmatic-drag-and-drop-hitbox":
              "PragmaticDragAndDropHitbox",
            "@monaco-editor/react": "MonacoEditor"
          }
        }
      }
    },
    test: {
      globals: true,
      environment: "jsdom",
      setupFiles: "./tests/setup.ts",
      coverage: {
        provider: "v8",
        reporter: ["text", "lcov", "html", "json"],
        reportsDirectory: "coverage/unit",
        include: ["src/**/*.{ts,tsx}"],
        exclude: [...coverageConfigDefaults.exclude, "scripts/**"]
      },
      testTimeout: 5000 // increase if you want to debug
    }
  };
});
