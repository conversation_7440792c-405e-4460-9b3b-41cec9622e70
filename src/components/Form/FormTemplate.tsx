import React, {
  FormE<PERSON>H<PERSON>ler,
  <PERSON>ps<PERSON><PERSON><PERSON>hildren,
  ReactNode,
  useCallback,
  useEffect,
  useRef
} from "react";

interface FormTemplateProps {
  autoComplete: string;
  onSubmit?: FormEventHandler;
  formButtons: ReactNode;
  className?: string;
  hideFormButtons?: boolean;
  disabled?: boolean;
}

export const FormTemplate = ({
  autoComplete,
  onSubmit,
  formButtons,
  className = "",
  hideFormButtons = false,
  disabled,
  children
}: PropsWithChildren<FormTemplateProps>) => {
  const formRef = useRef<HTMLFormElement>(null);

  const submitForm: React.FormEventHandler = useCallback(
    (event: React.FormEvent<HTMLFormElement>) => {
      (document.activeElement as HTMLInputElement)?.blur();
      setTimeout(() => {
        onSubmit?.(event as unknown as React.FormEvent<HTMLFormElement>);
      }, 0); // Use setTimeout to ensure the form submission is processed after the blur
    },
    [onSubmit]
  );

  useEffect(() => {
    if (hideFormButtons || disabled || !onSubmit) {
      return;
    }

    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === "Enter" && !event.shiftKey && !event.metaKey) {
        if (
          (document.activeElement instanceof HTMLInputElement ||
            document.activeElement instanceof HTMLTextAreaElement ||
            document.activeElement instanceof HTMLSelectElement) &&
          formRef.current?.contains(document.activeElement)
        ) {
          return;
        }
        event.preventDefault();
        submitForm(event as unknown as React.FormEvent<HTMLFormElement>);
      }
    };

    document.addEventListener("keydown", handleKeyPress);

    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [disabled, hideFormButtons, onSubmit, submitForm]);

  return (
    <form
      ref={formRef}
      autoComplete={autoComplete}
      className={`ot-form ${className}`}
      onSubmit={onSubmit ? submitForm : undefined}
    >
      {children}
      {formButtons}
    </form>
  );
};
