import react from "@vitejs/plugin-react-swc";
import path from "path";
import { defineConfig, loadEnv } from "vite";
import viteTsConfigPaths from "vite-tsconfig-paths";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const ENV = loadEnv(mode, process.cwd());
  const themeFile = ENV.VITE_APP_THEME === "otai" ? "otai.css" : "oneteam.css";
  return {
    resolve: {
      alias: {
        "@theme": path.resolve(
          __dirname,
          "../src/tokens/stylesheets",
          themeFile
        )
      }
    },
    plugins: [react(), viteTsConfigPaths()],
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler" // or 'modern'
        }
      }
    }
  };
});
