import React from "react";

import { Stack } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react-vite";

import { FlowStepVariant } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { FlowStepVariantPill } from "./FlowStepVariantPill";

const meta: Meta<typeof FlowStepVariantPill> = {
  component: FlowStepVariantPill,
  title: "configuration/FlowStepVariantPill"
};

export default meta;

type Story = StoryObj<typeof FlowStepVariantPill>;

const defaultArgs: Story["args"] = {};

const showAllRender = (args: Story["args"]) => {
  return (
    <Stack gap="200">
      {Object.values(FlowStepVariant).map(variant => {
        return (
          <FlowStepVariantPill {...args} variant={variant as FlowStepVariant} />
        );
      })}
    </Stack>
  );
};

export const ShowAll: Story = {
  args: defaultArgs,
  render: showAllRender
};
