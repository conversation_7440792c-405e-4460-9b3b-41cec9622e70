import React, { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { Box, Label, Stack } from "@oneteam/onetheme";

import { QuestionListAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionListAnswer";

import { useDictionary } from "@src/hooks/useDictionary";
import { AnswerLocation } from "@src/types/AnnotationLocation";
import { Question } from "@src/types/Question";
import { ListQuestionProperties } from "@src/types/QuestionProperties";
import { FormAnswer, ListAnswer } from "@src/types/collection/CollectionForm";

import { OTAIFormFieldCommonProps } from "../OTAIFormType";
import "./OTAIFormFieldTable.scss";

export const OTAIFormFieldListQuestion = ({
  id,
  question,
  commonProps,
  value,
  onPaste,
  location
}: {
  id: string;
  question: Question<ListQuestionProperties>;
  commonProps: OTAIFormFieldCommonProps;
  value?: FormAnswer<ListAnswer>;
  onPaste?: (event: React.ClipboardEvent<HTMLInputElement>) => void;
  location?: AnswerLocation;
}) => {
  const d = useDictionary();
  const { setValue } = useFormContext();

  useEffect(() => {
    setValue(id, undefined);
  }, [id, question, setValue]);

  return (
    <Stack gap="025">
      <Label
        label={commonProps.label ?? ""}
        description={commonProps.description}
        required={commonProps.required}
      />
      <Box width="100" overflow="auto">
        <QuestionListAnswer
          d={d}
          question={question}
          answer={value}
          answerAccessor={id}
          onPaste={onPaste}
          disableAddRow={commonProps.disabled || question.properties?.disabled}
          location={location}
          disabled={commonProps.disabled || question.properties?.disabled}
        />
      </Box>
    </Stack>
  );
};
