import { createContext, useContext } from "react";

import { FlowExecutionDocument } from "@src/types/FlowConfiguration/FlowExecution";
import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { WorkspaceDocument } from "@src/types/documentTypes";

export type FlowExecutionContextType = {
  workspaceDocument?: WorkspaceDocument;
  selectedStepId?: string;
  setSelectedStepId: (step?: string) => void;
  executionFlow?: FlowExecutionDocument;
  setExecutionFlow?: (flowExecution?: FlowExecutionDocument) => void;
  rootExecutionFlow?: FlowExecutionDocument;
  setRootExecutionFlow?: (flowExecution?: FlowExecutionDocument) => void;
  executionFlowForModal?: FlowExecutionDocument; // Used for steps inside an iterator
  setExecutionFlowForModal?: (flowExecution?: FlowExecutionDocument) => void;
  pathToCurrentExecution?: string[];
  setPathToCurrentExecution?: (path?: string[]) => void;
  iteratorStepPageMap?: Record<FlowStepId, number>;
  updateIteratorStepPageMap?: (stepId: FlowStepId, page: number) => void;
  summaryModalOpen?: boolean;
  setSummaryModalOpen?: (isOpen: boolean) => void;
  settings?: {
    displayJson: boolean;
    showMiniMap: boolean;
  };
  setSettings: (settings: {
    displayJson: boolean;
    showMiniMap: boolean;
  }) => void;
};

export const FlowExecutionContext = createContext<FlowExecutionContextType>({
  workspaceDocument: undefined,
  selectedStepId: undefined,
  setSelectedStepId: () => {},
  executionFlow: undefined,
  setExecutionFlow: () => {},
  executionFlowForModal: undefined,
  rootExecutionFlow: undefined,
  setRootExecutionFlow: () => {},
  setExecutionFlowForModal: () => {},
  pathToCurrentExecution: [],
  setPathToCurrentExecution: () => {},
  iteratorStepPageMap: {},
  updateIteratorStepPageMap: () => {},
  summaryModalOpen: true,
  setSummaryModalOpen: () => {},
  settings: {
    displayJson: false,
    showMiniMap: true
  },
  setSettings: () => {}
});

export const useFlowExecutionContext = () => useContext(FlowExecutionContext);
