import type { StorybookConfig } from "@storybook/react-vite";

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],

  addons: [
    "@storybook/addon-links",
    "@chromatic-com/storybook",
    "@storybook/addon-docs"
  ],

  core: {
    builder: "@storybook/builder-vite"
  },

  framework: {
    name: "@storybook/react-vite",
    options: {}
  },

  staticDirs: ["../public"]
};
export default config;
