import { Question, QuestionTypes } from "../types/Question";
import {
  BooleanQuestionProperties,
  DateQuestionProperties,
  ListQuestionProperties,
  NumberQuestionProperties,
  SelectQuestionProperties,
  TableQuestionProperties,
  TextQuestionProperties
} from "../types/QuestionProperties";

export class MockFormQuestion {
  static readonly LIST_WITH_TEXT_QUESTION = {
    type: QuestionTypes.LIST,
    id: "L1",
    properties: {
      required: true,
      maxLength: 5,
      minLength: 2,
      items: [
        {
          type: QuestionTypes.TEXT,
          id: "q1",
          properties: {
            allowReuseAcrossForms: false,
            placeholder: "1"
          },
          text: "Text Qn1"
        } as Question<TextQuestionProperties>
      ]
    },
    text: "List Qn1"
  } as Question<ListQuestionProperties>;

  static readonly TABLE_WITH_ALL_QUESTION_TYPES = {
    type: QuestionTypes.TABLE,
    id: "T1",
    properties: {
      columns: [
        {
          type: QuestionTypes.TEXT,
          id: "q1",
          properties: {
            allowReuseAcrossForms: false,
            maxLength: 5,
            minLength: 2,
            placeholder: "1",
            required: true
          },
          text: "Text Qn1"
        } as Question<TextQuestionProperties>,
        {
          type: QuestionTypes.NUMBER,
          id: "q2",
          properties: {
            allowReuseAcrossForms: false,
            placeholder: "1",
            required: true
          },
          text: "Number Qn2"
        } as Question<NumberQuestionProperties>,
        {
          type: QuestionTypes.DATE,
          id: "q3",
          properties: {
            allowReuseAcrossForms: false,
            placeholder: "1",
            required: true
          },
          identifier: "date-q3",
          text: "Date Qn3"
        } as Question<DateQuestionProperties>,
        {
          type: QuestionTypes.SELECT,
          id: "q4",
          properties: {
            allowReuseAcrossForms: false,
            options: [{ value: "option1" }, { value: "option2" }],
            placeholder: "1",
            required: true
          },
          identifier: "select-q4",
          text: "Select Qn4"
        } as Question<SelectQuestionProperties>,
        {
          type: QuestionTypes.BOOLEAN,
          id: "q5",
          properties: {
            allowReuseAcrossForms: false,
            placeholder: "1",
            required: true
          },
          identifier: "boolean-q5",
          text: "Boolean Qn5"
        } as Question<BooleanQuestionProperties>
      ]
    },
    text: "Table Qn1"
  } as Question<TableQuestionProperties>;

  static readonly TABLE_QUESTION_WITH_REQUIRED_QUESTIONS = {
    type: QuestionTypes.TABLE,
    id: "T1",
    properties: {
      columns: [
        {
          type: QuestionTypes.TEXT,
          id: "q1",
          properties: {
            allowReuseAcrossForms: false,
            maxLength: 5,
            minLength: 2,
            placeholder: "1",
            required: true
          },
          text: "Text Qn1"
        } as Question<TextQuestionProperties>,
        {
          type: QuestionTypes.TEXT,
          id: "q2",
          properties: {
            allowReuseAcrossForms: false,
            maxLength: 5,
            minLength: 2,
            placeholder: "1",
            required: false
          },
          text: "Text Qn2"
        } as Question<TextQuestionProperties>
      ]
    },
    text: "Table Qn1"
  } as Question<TableQuestionProperties>;

  static readonly TABLE_QUESTION_WITH_NO_REQUIRED_QUESTIONS = {
    type: QuestionTypes.TABLE,
    id: "T1",
    properties: {
      columns: [
        {
          type: QuestionTypes.TEXT,
          id: "q1",
          properties: {
            allowReuseAcrossForms: false,
            maxLength: 5,
            minLength: 2,
            placeholder: "1",
            required: false
          },
          text: "Text Qn1"
        } as Question<TextQuestionProperties>,
        {
          type: QuestionTypes.TEXT,
          id: "q2",
          properties: {
            allowReuseAcrossForms: false,
            maxLength: 5,
            minLength: 2,
            placeholder: "1",
            required: false
          },
          text: "Text Qn2"
        } as Question<TextQuestionProperties>
      ]
    },
    text: "Table Qn1"
  } as Question<TableQuestionProperties>;
}
