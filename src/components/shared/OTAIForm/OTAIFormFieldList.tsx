import React, { useCallback, useMemo } from "react";

import { Inline, Stack } from "@oneteam/onetheme";
import flatten from "lodash/flatten";

import { Question } from "@src/types/Question";

import { OTAIFormFieldQuestion } from "./OTAIFormFieldQuestion";
import { OTAIFormFieldPositioning } from "./OTAIFormType";

export const OTAIFormFieldList = ({
  questions,
  positioning = [],
  autoFocusFirstField
}: {
  questions: Question[];
  positioning?: OTAIFormFieldPositioning;
  autoFocusFirstField?: boolean;
}) => {
  const questionById = useMemo(() => {
    return questions.reduce(
      (acc, question) => {
        acc[question.id ?? question.identifier] = question;
        return acc;
      },
      {} as Record<string, Question>
    );
  }, [questions]);

  // TODO: support grid/positioning later
  const additionalQuestions = useMemo(() => {
    if (!positioning) {
      return [];
    }
    const flatQuestionPositioning = flatten(positioning);
    return Object.keys(questionById)
      .filter(id => !flatQuestionPositioning.includes(id))
      .map(id => questionById[id]);
  }, [positioning, questionById]);

  const renderQuestion = useCallback(
    (question: Question, isFirst?: boolean) => {
      return (
        <OTAIFormFieldQuestion
          key={question.id ?? question.identifier}
          question={question}
          autoFocus={autoFocusFirstField && isFirst}
        />
      );
    },
    [autoFocusFirstField]
  );

  const questionsList = useMemo(() => {
    return positioning?.map((row, rowIndex) => {
      if (typeof row === "string") {
        return renderQuestion(questionById[row], rowIndex === 0);
      }
      return (
        <Inline width="100" key={rowIndex} gap="150" contentsWidth="100">
          {row.map((questionId, index) =>
            renderQuestion(
              questionById[`${questionId}`],
              rowIndex === 0 && index === 0
            )
          )}
        </Inline>
      );
    });
  }, [positioning, questionById, renderQuestion]);

  return (
    <Stack gap="150" contentsWidth="100">
      {questionsList}
      {additionalQuestions.map((question: Question, index) =>
        renderQuestion(question, questionsList.length === 0 && index === 0)
      )}
    </Stack>
  );
};
