import {
  CompoundCondition,
  Condition,
  ConditionItem,
  Operators
} from "@src/types/FlowConfiguration/Condition";

type LogicalOperator = "AND" | "OR" | null;
interface StringParserState {
  inQuotes: boolean;
  parenCount: number;
}
interface LogicalSplit {
  logicOperator: LogicalOperator;
  parts: string[];
}
const OPERATORS: Record<string, LogicalOperator> = {
  " AND ": "AND",
  " OR ": "OR"
};

const updateParserState = (char: string, state: StringParserState): void => {
  if (char === '"') {
    state.inQuotes = !state.inQuotes;
  } else if (!state.inQuotes) {
    if (char === "(") {
      state.parenCount++;
    } else if (char === ")") {
      state.parenCount--;
    }
  }
};

const countTopLevelParens = (str: string): number => {
  const state: StringParserState = { inQuotes: false, parenCount: 0 };

  for (const char of str) {
    updateParserState(char, state);
    if (!state.inQuotes && state.parenCount < 0) {
      return state.parenCount;
    }
  }

  return state.parenCount;
};

const splitLogicalOperators = (str: string): LogicalSplit => {
  const parts: string[] = [];
  let current = "";
  const state: StringParserState = { inQuotes: false, parenCount: 0 };
  let foundOperator: LogicalOperator = null;

  const checkOperator = (pos: number): LogicalOperator => {
    for (const [op, value] of Object.entries(OPERATORS)) {
      if (str.slice(pos).startsWith(op)) {
        return value;
      }
    }
    return null;
  };

  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    updateParserState(char, state);

    if (state.parenCount === 0 && !state.inQuotes) {
      const operator = checkOperator(i);
      if (operator) {
        parts.push(current);
        current = "";
        i += operator.length + 1;
        foundOperator = operator;
        continue;
      }
    }
    current += char;
  }

  parts.push(current);

  return {
    logicOperator: foundOperator,
    parts: parts.filter(Boolean).map(p => p.trim())
  };
};

export function parseCondition(conditionStr: string): Condition | null {
  // Remove outer parentheses and trim
  conditionStr = conditionStr.trim();
  while (conditionStr.startsWith("(") && conditionStr.endsWith(")")) {
    const inner = conditionStr.slice(1, -1).trim();
    if (countTopLevelParens(inner) >= 0) {
      conditionStr = inner;
    } else {
      break;
    }
  }

  const { logicOperator, parts } = splitLogicalOperators(conditionStr);
  if (logicOperator && parts.length > 1) {
    return {
      [logicOperator]: parts.map(part => parseCondition(part))
    } as CompoundCondition;
  }

  // Handle isEmpty
  if (conditionStr.includes("isEmpty")) {
    return {
      lhs: "",
      operator: "isEmpty"
    } as ConditionItem;
  }

  // Handle other operators
  const operatorMatch = conditionStr.match(
    /(>=|<=|>|<|contains|doesNotContain|isEmpty|isNotEmpty|=|!=)/
  );
  if (!operatorMatch) {
    return null;
  }
  const operator = operatorMatch[0] as Operators;
  const [lhs, rhs] = conditionStr.split(operator);
  return {
    lhs: parseValue(lhs),
    operator: operator,
    rhs: rhs ? parseValue(rhs) : undefined
  };
}

function parseValue(value: string): string {
  value = value.trim();
  // Remove quotes if present
  if (value.startsWith('"') && value.endsWith('"')) {
    return value.slice(1, -1);
  }
  return value;
}

export const isCompoundConditionType = (
  condition: Condition
): condition is CompoundCondition => {
  if (!condition || typeof condition !== "object") {
    return false;
  }
  return "AND" in condition || "OR" in condition;
};
